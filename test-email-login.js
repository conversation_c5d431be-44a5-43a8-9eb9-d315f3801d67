// 测试邮箱登录流程的脚本
const axios = require('axios');

const API_BASE = 'http://localhost:8080/api';
const FRONTEND_BASE = 'http://localhost:3000';

async function testEmailLogin() {
  console.log('🧪 开始测试邮箱登录流程...\n');

  const testEmail = '<EMAIL>';
  
  try {
    // 1. 发送注册验证码
    console.log('📧 1. 发送注册验证码...');
    const sendCodeResponse = await axios.post(`${API_BASE}/auth/email/send-code`, {
      email: testEmail,
      type: 'REGISTER'
    });
    
    console.log('✅ 验证码发送成功:', sendCodeResponse.data);
    
    // 2. 从数据库获取验证码（模拟）
    console.log('\n🔍 2. 查询数据库中的验证码...');
    // 这里我们需要从后端日志中获取验证码，或者查询数据库
    
    // 3. 模拟注册
    console.log('\n👤 3. 模拟用户注册...');
    const mockCode = '123456'; // 这里应该是从数据库或日志中获取的真实验证码
    
    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/email/register`, {
        email: testEmail,
        username: 'testuser',
        code: mockCode,
        bio: '这是测试用户'
      });
      
      console.log('✅ 注册成功:', {
        accessToken: registerResponse.data.data.accessToken ? '已获取' : '未获取',
        user: registerResponse.data.data.user.username,
        userId: registerResponse.data.data.user.id
      });
      
      // 4. 测试token是否有效
      console.log('\n🔐 4. 测试访问令牌...');
      const meResponse = await axios.get(`${API_BASE}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${registerResponse.data.data.accessToken}`
        }
      });
      
      console.log('✅ 令牌有效，用户信息:', meResponse.data.data.username);
      
    } catch (registerError) {
      if (registerError.response?.data?.message?.includes('已被注册')) {
        console.log('ℹ️  用户已存在，尝试登录...');
        
        // 发送登录验证码
        await axios.post(`${API_BASE}/auth/email/send-code`, {
          email: testEmail,
          type: 'LOGIN'
        });
        
        console.log('📧 登录验证码已发送');
        
        // 模拟登录
        const loginResponse = await axios.post(`${API_BASE}/auth/email/login`, {
          email: testEmail,
          code: mockCode
        });
        
        console.log('✅ 登录成功:', {
          accessToken: loginResponse.data.data.accessToken ? '已获取' : '未获取',
          user: loginResponse.data.data.user.username,
          userId: loginResponse.data.data.user.id
        });
      } else {
        throw registerError;
      }
    }
    
    console.log('\n🎉 邮箱登录流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testEmailLogin();
