#!/bin/bash

# 项目状态检查脚本

echo "🔍 检查中文AI提示词交易平台状态..."
echo ""

# 检查Docker服务状态
echo "📦 Docker服务状态:"
if command -v docker &> /dev/null; then
    if docker-compose ps 2>/dev/null | grep -q "Up"; then
        docker-compose ps
    else
        echo "❌ Docker服务未运行"
    fi
else
    echo "❌ Docker未安装"
fi

echo ""

# 检查端口占用
echo "🔌 端口占用情况:"
ports=(3000 8080 3306 6379)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        process=$(lsof -i :$port | tail -n 1 | awk '{print $1, $2}')
        echo "✅ 端口 $port: $process"
    else
        echo "❌ 端口 $port: 未占用"
    fi
done

echo ""

# 检查服务健康状态
echo "🏥 服务健康检查:"

# 检查前端
if curl -f http://localhost:3000 &> /dev/null; then
    echo "✅ 前端服务: 正常"
else
    echo "❌ 前端服务: 异常"
fi

# 检查后端
if curl -f http://localhost:8080/api/actuator/health &> /dev/null; then
    echo "✅ 后端服务: 正常"
    # 获取健康详情
    health_status=$(curl -s http://localhost:8080/api/actuator/health | jq -r '.status' 2>/dev/null)
    if [ "$health_status" = "UP" ]; then
        echo "   状态: UP"
    else
        echo "   状态: $health_status"
    fi
else
    echo "❌ 后端服务: 异常"
fi

# 检查数据库连接
if command -v mysql &> /dev/null; then
    if mysql -h localhost -P 3306 -u root -pa123456789A -e "SELECT 1;" &> /dev/null; then
        echo "✅ MySQL数据库: 连接正常"
    else
        echo "❌ MySQL数据库: 连接失败"
    fi
else
    echo "⚠️  MySQL客户端未安装，无法检查数据库连接"
fi

# 检查Redis连接
if command -v redis-cli &> /dev/null; then
    if redis-cli -h localhost -p 6379 ping &> /dev/null; then
        echo "✅ Redis缓存: 连接正常"
    else
        echo "❌ Redis缓存: 连接失败"
    fi
else
    echo "⚠️  Redis客户端未安装，无法检查Redis连接"
fi

echo ""

# 显示日志摘要
echo "📋 最近日志摘要:"
if docker-compose ps &> /dev/null; then
    echo "--- 后端日志 (最近10行) ---"
    docker-compose logs --tail=10 backend 2>/dev/null || echo "无法获取后端日志"
    echo ""
    echo "--- 前端日志 (最近10行) ---"
    docker-compose logs --tail=10 frontend 2>/dev/null || echo "无法获取前端日志"
else
    echo "Docker Compose服务未运行"
fi

echo ""
echo "🔗 快速链接:"
echo "📱 前端: http://localhost:3000"
echo "🔧 后端: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/api/swagger-ui.html"
echo "🏥 健康检查: http://localhost:8080/api/actuator/health"
