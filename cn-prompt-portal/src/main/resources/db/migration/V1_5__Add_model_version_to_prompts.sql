-- 添加模型版本字段到提示词表
ALTER TABLE prompts ADD COLUMN model_version VARCHAR(50) COMMENT '模型版本';

-- 为现有数据设置默认版本
UPDATE prompts SET model_version = 'gpt-3.5-turbo' WHERE model = 'ChatGPT' AND model_version IS NULL;
UPDATE prompts SET model_version = 'gpt-4' WHERE model = 'GPT-4' AND model_version IS NULL;
UPDATE prompts SET model_version = 'claude-3-sonnet' WHERE model = 'Claude' AND model_version IS NULL;
UPDATE prompts SET model_version = 'gemini-pro' WHERE model = 'Gemini' AND model_version IS NULL;
UPDATE prompts SET model_version = 'qwen-max' WHERE model = 'Qwen' AND model_version IS NULL;
UPDATE prompts SET model_version = 'ernie-bot-4' WHERE model = 'ERNIE Bot' AND model_version IS NULL;
UPDATE prompts SET model_version = 'spark-3.5' WHERE model = 'Spark' AND model_version IS NULL;
UPDATE prompts SET model_version = 'yi-large' WHERE model = 'Yi' AND model_version IS NULL;
UPDATE prompts SET model_version = 'moonshot-v1-8k' WHERE model = 'Moonshot' AND model_version IS NULL;
UPDATE prompts SET model_version = 'deepseek-chat' WHERE model = 'DeepSeek' AND model_version IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_prompts_model_version ON prompts(model, model_version);
