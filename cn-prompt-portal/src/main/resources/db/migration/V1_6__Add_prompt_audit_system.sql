-- 添加提示词审核相关字段
ALTER TABLE prompts ADD COLUMN audit_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审核状态: PENDING-待审核, APPROVED-已通过, REJECTED-已拒绝';
ALTER TABLE prompts ADD COLUMN audit_reason TEXT COMMENT '审核原因（拒绝时填写）';
ALTER TABLE prompts ADD COLUMN auditor_id BIGINT COMMENT '审核员ID';
ALTER TABLE prompts ADD COLUMN audit_time TIMESTAMP NULL COMMENT '审核时间';

-- 为现有数据设置默认审核状态
UPDATE prompts SET audit_status = 'APPROVED' WHERE audit_status IS NULL;

-- 添加索引
CREATE INDEX idx_prompts_audit_status ON prompts(audit_status);
CREATE INDEX idx_prompts_auditor_id ON prompts(auditor_id);

-- 创建收藏表
CREATE TABLE user_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    prompt_id BIGINT NOT NULL COMMENT '提示词ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    UNIQUE KEY uk_user_prompt (user_id, prompt_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
) COMMENT '用户收藏表';

-- 创建评价表
CREATE TABLE prompt_reviews (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    prompt_id BIGINT NOT NULL COMMENT '提示词ID',
    rating INT NOT NULL COMMENT '评分(1-5星)',
    content TEXT COMMENT '评价内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_prompt_review (user_id, prompt_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
) COMMENT '提示词评价表';

-- 添加提示词平均评分和评价数量字段
ALTER TABLE prompts ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分';
ALTER TABLE prompts ADD COLUMN review_count INT DEFAULT 0 COMMENT '评价数量';

-- 添加用户表新字段
ALTER TABLE users ADD COLUMN real_name VARCHAR(50) COMMENT '真实姓名';
ALTER TABLE users ADD COLUMN avatar VARCHAR(500) COMMENT '头像URL';
ALTER TABLE users ADD COLUMN phone VARCHAR(20) COMMENT '手机号';

-- 添加索引
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_prompt_id ON user_favorites(prompt_id);
CREATE INDEX idx_prompt_reviews_user_id ON prompt_reviews(user_id);
CREATE INDEX idx_prompt_reviews_prompt_id ON prompt_reviews(prompt_id);
CREATE INDEX idx_prompts_rating ON prompts(average_rating DESC);
