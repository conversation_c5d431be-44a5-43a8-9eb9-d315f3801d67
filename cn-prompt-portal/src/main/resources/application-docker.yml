# Docker环境配置
server:
  port: 8080

spring:
  application:
    name: cn-prompt-portal
    
  datasource:
    url: ${SPRING_DATASOURCE_URL:********************************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:root}
    password: ${SPRING_DATASOURCE_PASSWORD:a123456789A}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
        
  redis:
    host: ${SPRING_REDIS_HOST:redis}
    port: ${SPRING_REDIS_PORT:6379}
    timeout: 2000ms
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: -1ms

# 应用配置
app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey123456789PromptStore2024Docker}
    access-token-expiration: 3600
    refresh-token-expiration: 2592000
    
  wechat:
    app-id: ${WECHAT_APP_ID:your_wechat_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_wechat_app_secret}
    redirect-uri: ${WECHAT_REDIRECT_URI:http://localhost:3000/auth/wechat/callback}
    
  cors:
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://127.0.0.1:3000
      - http://127.0.0.1:3001
      - http://frontend:3000
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true

# 日志配置
logging:
  level:
    com.promptstore: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
