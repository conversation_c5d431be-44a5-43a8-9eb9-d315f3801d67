server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: cn-prompt-portal
    
  profiles:
    active: dev
    
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: a123456789A
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        
  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 2000ms
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: -1ms
        
  cache:
    type: redis
    redis:
      time-to-live: 1800000
      
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 应用配置
app:
  jwt:
    secret: mySecretKey123456789PromptStore2024
    access-token-expiration: 3600
    refresh-token-expiration: 2592000
    
  wechat:
    app-id: wxbea605b24766867c
    app-secret: 6c3b89495c386f7bd56a10541b5edbbd
    redirect-uri: ${WECHAT_REDIRECT_URI:http://localhost:3000/auth/wechat/callback}

  # 阿里邮箱企业版配置
  aliyun:
    mail:
      # 阿里邮箱企业版API配置
      access-key-id: LTAI5tPB6MFrU982j3Mo854j
      access-key-secret: ******************************
      # 邮件发送配置
      from-address: support.cnprompt.com
      from-name: ${ALIYUN_MAIL_FROM_NAME:提示词商店}
      # 验证码配置
      verification:
        expire-minutes: 5
        max-attempts: 3
        resend-interval-seconds: 60
    
  payment:
    alipay:
      app-id: ${ALIPAY_APP_ID:your_alipay_app_id}
      private-key: ${ALIPAY_PRIVATE_KEY:your_alipay_private_key}
      public-key: ${ALIPAY_PUBLIC_KEY:your_alipay_public_key}
      gateway-url: https://openapi.alipay.com/gateway.do
      notify-url: ${ALIPAY_NOTIFY_URL:http://localhost:8080/api/webhook/alipay}
    wechat:
      app-id: ${WECHAT_PAY_APP_ID:your_wechat_pay_app_id}
      mch-id: ${WECHAT_MCH_ID:your_wechat_mch_id}
      api-key: ${WECHAT_API_KEY:your_wechat_api_key}
      notify-url: ${WECHAT_PAY_NOTIFY_URL:http://localhost:8080/api/webhook/wechat}
      
  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,webp
    
  cors:
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://127.0.0.1:3000
      - http://127.0.0.1:3001
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true

# 日志配置
logging:
  level:
    com.promptstore: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.promptstore.controller

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-shanghai.aliyuncs.com
    access-key-id: LTAI5tPB6MFrU982j3Mo854j
    access-key-secret: ******************************
    bucket-name: cnprompt
    path-prefix: prompt-images/
    custom-domain: # 可选，自定义域名
    use-https: true
