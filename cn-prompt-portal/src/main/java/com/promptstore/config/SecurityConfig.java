package com.promptstore.config;

import com.promptstore.security.JwtAuthenticationEntryPoint;
import com.promptstore.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security配置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final CorsConfigurationSource corsConfigurationSource;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            
            // 配置会话管理
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            
            // 配置异常处理
            .exceptionHandling(exception -> 
                exception.authenticationEntryPoint(jwtAuthenticationEntryPoint)
            )
            
            // 配置请求授权
            .authorizeHttpRequests(auth -> auth
                // 公开接口
                .requestMatchers(HttpMethod.GET, "/actuator/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .requestMatchers(HttpMethod.POST, "/auth/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/auth/wechat/qrcode").permitAll()
                
                // 分类接口（公开）
                .requestMatchers(HttpMethod.GET, "/categories/**").permitAll()

                // 模型配置接口（公开）
                .requestMatchers(HttpMethod.GET, "/models").permitAll()
                .requestMatchers(HttpMethod.GET, "/models/**").permitAll()

                // 提示词查看接口（公开）
                .requestMatchers(HttpMethod.GET, "/prompts/search").permitAll()
                .requestMatchers(HttpMethod.GET, "/prompts/{id}").permitAll()
                .requestMatchers(HttpMethod.GET, "/prompts/hot").permitAll()
                .requestMatchers(HttpMethod.GET, "/prompts/latest").permitAll()
                .requestMatchers(HttpMethod.GET, "/prompts/user/{userId}").permitAll()
                
                // 用户公开信息接口
                .requestMatchers(HttpMethod.GET, "/users/{id}").permitAll()
                .requestMatchers(HttpMethod.GET, "/users/{id}/stats").permitAll()
                .requestMatchers(HttpMethod.GET, "/users/creators/top").permitAll()
                .requestMatchers(HttpMethod.GET, "/users/search").permitAll()
                
                // 需要认证的接口
                .requestMatchers("/users/me/**").authenticated()
                .requestMatchers(HttpMethod.POST, "/users/apply-creator").authenticated()
                .requestMatchers("/user-profile/**").authenticated()

                // 收藏接口（需要认证）
                .requestMatchers("/favorites/**").authenticated()

                // 评价接口（部分需要认证）
                .requestMatchers(HttpMethod.POST, "/reviews/**").authenticated()
                .requestMatchers(HttpMethod.DELETE, "/reviews/**").authenticated()
                .requestMatchers(HttpMethod.GET, "/reviews/my").authenticated()
                .requestMatchers(HttpMethod.GET, "/reviews/**").permitAll()
                
                // 提示词管理接口（需要创作者权限）
                .requestMatchers(HttpMethod.POST, "/prompts").hasAnyRole("CREATOR", "ADMIN")
                .requestMatchers(HttpMethod.PUT, "/prompts/**").hasAnyRole("CREATOR", "ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/prompts/**").hasAnyRole("CREATOR", "ADMIN")
                .requestMatchers(HttpMethod.POST, "/prompts/*/publish").hasAnyRole("CREATOR", "ADMIN")

                // 文件上传接口（需要认证）
                .requestMatchers("/upload/**").authenticated()

                // 管理员接口
                .requestMatchers("/admin/**").hasRole("ADMIN")
                
                // 其他接口需要认证
                .anyRequest().authenticated()
            );

        // 添加JWT过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
