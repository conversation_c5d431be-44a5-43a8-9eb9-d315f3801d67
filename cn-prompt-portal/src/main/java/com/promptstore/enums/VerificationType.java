package com.promptstore.enums;

/**
 * 验证类型枚举
 */
public enum VerificationType {
    /**
     * 注册验证
     */
    REGISTER("注册验证"),
    
    /**
     * 登录验证
     */
    LOGIN("登录验证"),
    
    /**
     * 重置密码
     */
    RESET_PASSWORD("重置密码"),
    
    /**
     * 邮箱验证
     */
    EMAIL_VERIFICATION("邮箱验证"),
    
    /**
     * 绑定邮箱
     */
    BIND_EMAIL("绑定邮箱");

    private final String description;

    VerificationType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
