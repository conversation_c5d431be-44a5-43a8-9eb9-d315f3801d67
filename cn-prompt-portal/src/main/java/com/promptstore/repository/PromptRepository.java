package com.promptstore.repository;

import com.promptstore.entity.Prompt;
import com.promptstore.enums.AuditStatus;
import com.promptstore.enums.PromptStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 提示词数据访问层
 */
@Repository
public interface PromptRepository extends JpaRepository<Prompt, Long>, JpaSpecificationExecutor<Prompt> {

    /**
     * 根据用户ID查找提示词
     */
    Page<Prompt> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找提示词
     */
    Page<Prompt> findByUserIdAndStatus(Long userId, PromptStatus status, Pageable pageable);

    /**
     * 根据分类ID查找提示词
     */
    Page<Prompt> findByCategoryId(Long categoryId, Pageable pageable);

    /**
     * 根据分类ID和状态查找提示词
     */
    Page<Prompt> findByCategoryIdAndStatus(Long categoryId, PromptStatus status, Pageable pageable);

    /**
     * 根据AI模型查找提示词
     */
    Page<Prompt> findByAiModel(String aiModel, Pageable pageable);

    /**
     * 根据AI模型和状态查找提示词
     */
    Page<Prompt> findByAiModelAndStatus(String aiModel, PromptStatus status, Pageable pageable);

    /**
     * 根据状态查找提示词
     */
    Page<Prompt> findByStatus(PromptStatus status, Pageable pageable);

    /**
     * 查找精选提示词
     */
    Page<Prompt> findByIsFeaturedTrueAndStatus(PromptStatus status, Pageable pageable);

    /**
     * 查找免费提示词
     */
    Page<Prompt> findByIsFreeTrueAndStatus(PromptStatus status, Pageable pageable);

    /**
     * 根据价格范围查找提示词
     */
    Page<Prompt> findByPriceBetweenAndStatus(BigDecimal minPrice, BigDecimal maxPrice, PromptStatus status, Pageable pageable);

    /**
     * 标题模糊查询
     */
    Page<Prompt> findByTitleContainingIgnoreCaseAndStatus(String title, PromptStatus status, Pageable pageable);

    /**
     * 描述模糊查询
     */
    Page<Prompt> findByDescriptionContainingIgnoreCaseAndStatus(String description, PromptStatus status, Pageable pageable);

    /**
     * 标题或描述模糊查询
     */
    @Query("SELECT p FROM Prompt p WHERE p.status = :status AND (LOWER(p.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Prompt> findByKeywordAndStatus(@Param("keyword") String keyword, @Param("status") PromptStatus status, Pageable pageable);

    /**
     * 增加浏览次数
     */
    @Modifying
    @Query("UPDATE Prompt p SET p.viewCount = p.viewCount + 1 WHERE p.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加下载次数
     */
    @Modifying
    @Query("UPDATE Prompt p SET p.downloadCount = p.downloadCount + 1 WHERE p.id = :id")
    void incrementDownloadCount(@Param("id") Long id);

    /**
     * 增加点赞次数
     */
    @Modifying
    @Query("UPDATE Prompt p SET p.likeCount = p.likeCount + 1 WHERE p.id = :id")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞次数
     */
    @Modifying
    @Query("UPDATE Prompt p SET p.likeCount = p.likeCount - 1 WHERE p.id = :id AND p.likeCount > 0")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 更新评分
     */
    @Modifying
    @Query("UPDATE Prompt p SET p.ratingAvg = :ratingAvg, p.ratingCount = :ratingCount WHERE p.id = :id")
    void updateRating(@Param("id") Long id, @Param("ratingAvg") BigDecimal ratingAvg, @Param("ratingCount") Integer ratingCount);

    /**
     * 获取热门提示词
     */
    @Query("SELECT p FROM Prompt p WHERE p.status = 'APPROVED' ORDER BY p.downloadCount DESC, p.viewCount DESC")
    Page<Prompt> findHotPrompts(Pageable pageable);

    /**
     * 获取最新提示词
     */
    @Query("SELECT p FROM Prompt p WHERE p.status = 'APPROVED' ORDER BY p.publishedAt DESC")
    Page<Prompt> findLatestPrompts(Pageable pageable);

    /**
     * 获取评分最高的提示词
     */
    @Query("SELECT p FROM Prompt p WHERE p.status = 'APPROVED' AND p.ratingCount >= 5 ORDER BY p.ratingAvg DESC")
    Page<Prompt> findTopRatedPrompts(Pageable pageable);

    /**
     * 根据用户ID统计提示词数量
     */
    long countByUserIdAndStatus(Long userId, PromptStatus status);

    /**
     * 根据分类ID统计提示词数量
     */
    long countByCategoryIdAndStatus(Long categoryId, PromptStatus status);

    /**
     * 获取用户的已购买提示词ID列表
     */
    @Query("SELECT DISTINCT oi.promptId FROM OrderItem oi JOIN Order o ON oi.orderId = o.id WHERE o.userId = :userId AND o.status = 'PAID'")
    List<Long> findPurchasedPromptIds(@Param("userId") Long userId);

    /**
     * 根据审核状态统计数量
     */
    long countByAuditStatus(AuditStatus auditStatus);

    /**
     * 根据审核状态查询提示词
     */
    Page<Prompt> findByAuditStatus(AuditStatus auditStatus, Pageable pageable);

    /**
     * 根据用户ID和审核状态查询提示词
     */
    Page<Prompt> findByUserIdAndAuditStatus(Long userId, AuditStatus auditStatus, Pageable pageable);

    /**
     * 统计用户的提示词数量
     */
    long countByUserId(Long userId);
}
