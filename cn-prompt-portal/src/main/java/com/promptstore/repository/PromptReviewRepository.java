package com.promptstore.repository;

import com.promptstore.entity.PromptReview;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 提示词评价数据访问层
 */
@Repository
public interface PromptReviewRepository extends JpaRepository<PromptReview, Long> {

    /**
     * 根据用户ID和提示词ID查找评价记录
     */
    Optional<PromptReview> findByUserIdAndPromptId(Long userId, Long promptId);

    /**
     * 检查用户是否已评价指定提示词
     */
    boolean existsByUserIdAndPromptId(Long userId, Long promptId);

    /**
     * 获取提示词的评价列表（分页）
     */
    @Query("SELECT pr FROM PromptReview pr JOIN FETCH pr.user u WHERE pr.promptId = :promptId ORDER BY pr.createdAt DESC")
    Page<PromptReview> findByPromptIdOrderByCreatedAtDesc(@Param("promptId") Long promptId, Pageable pageable);

    /**
     * 获取用户的评价列表（分页）
     */
    @Query("SELECT pr FROM PromptReview pr JOIN FETCH pr.prompt p WHERE pr.userId = :userId ORDER BY pr.createdAt DESC")
    Page<PromptReview> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计提示词的评价数量
     */
    long countByPromptId(Long promptId);

    /**
     * 计算提示词的平均评分
     */
    @Query("SELECT AVG(pr.rating) FROM PromptReview pr WHERE pr.promptId = :promptId")
    BigDecimal calculateAverageRating(@Param("promptId") Long promptId);

    /**
     * 统计各星级的评价数量
     */
    @Query("SELECT pr.rating, COUNT(pr) FROM PromptReview pr WHERE pr.promptId = :promptId GROUP BY pr.rating ORDER BY pr.rating DESC")
    Object[][] countByPromptIdGroupByRating(@Param("promptId") Long promptId);

    /**
     * 统计用户评价数量
     */
    long countByUserId(Long userId);

    /**
     * 删除用户对指定提示词的评价
     */
    void deleteByUserIdAndPromptId(Long userId, Long promptId);
}
