package com.promptstore.repository;

import com.promptstore.entity.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 分类数据访问层
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    /**
     * 根据slug查找分类
     */
    Optional<Category> findBySlug(String slug);

    /**
     * 检查slug是否存在
     */
    boolean existsBySlug(String slug);

    /**
     * 根据父分类ID查找子分类
     */
    List<Category> findByParentIdAndIsActiveTrue(Long parentId);

    /**
     * 查找所有根分类（父分类ID为null）
     */
    List<Category> findByParentIdIsNullAndIsActiveTrueOrderBySortOrder();

    /**
     * 查找所有活跃分类
     */
    List<Category> findByIsActiveTrueOrderBySortOrder();

    /**
     * 分页查找活跃分类
     */
    Page<Category> findByIsActiveTrue(Pageable pageable);

    /**
     * 根据名称模糊查询
     */
    Page<Category> findByNameContainingIgnoreCaseAndIsActiveTrue(String name, Pageable pageable);

    /**
     * 增加提示词数量
     */
    @Modifying
    @Query("UPDATE Category c SET c.promptCount = c.promptCount + 1 WHERE c.id = :id")
    void incrementPromptCount(@Param("id") Long id);

    /**
     * 减少提示词数量
     */
    @Modifying
    @Query("UPDATE Category c SET c.promptCount = c.promptCount - 1 WHERE c.id = :id AND c.promptCount > 0")
    void decrementPromptCount(@Param("id") Long id);

    /**
     * 获取热门分类（按提示词数量排序）
     */
    @Query("SELECT c FROM Category c WHERE c.isActive = true ORDER BY c.promptCount DESC")
    Page<Category> findHotCategories(Pageable pageable);

    /**
     * 检查分类是否有子分类
     */
    boolean existsByParentId(Long parentId);

    /**
     * 获取分类层级路径
     */
    @Query(value = """
        WITH RECURSIVE category_path AS (
            SELECT id, name, parent_id, CAST(name AS CHAR(1000)) as path, 0 as level
            FROM categories 
            WHERE id = :categoryId
            
            UNION ALL
            
            SELECT c.id, c.name, c.parent_id, CONCAT(c.name, ' > ', cp.path), cp.level + 1
            FROM categories c
            INNER JOIN category_path cp ON c.id = cp.parent_id
        )
        SELECT path FROM category_path WHERE parent_id IS NULL
        """, nativeQuery = true)
    String getCategoryPath(@Param("categoryId") Long categoryId);
}
