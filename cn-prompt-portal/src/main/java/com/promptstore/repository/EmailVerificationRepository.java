package com.promptstore.repository;

import com.promptstore.entity.EmailVerification;
import com.promptstore.enums.VerificationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邮箱验证码仓库
 */
@Repository
public interface EmailVerificationRepository extends JpaRepository<EmailVerification, Long> {

    /**
     * 根据邮箱和验证类型查找最新的验证码
     */
    Optional<EmailVerification> findTopByEmailAndTypeOrderByCreatedAtDesc(String email, VerificationType type);

    /**
     * 根据邮箱、验证码和类型查找验证记录
     */
    Optional<EmailVerification> findByEmailAndCodeAndType(String email, String code, VerificationType type);

    /**
     * 查找指定时间内的验证码记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.email = :email AND ev.type = :type AND ev.createdAt > :since")
    List<EmailVerification> findByEmailAndTypeAndCreatedAtAfter(
        @Param("email") String email, 
        @Param("type") VerificationType type, 
        @Param("since") LocalDateTime since
    );

    /**
     * 删除过期的验证码
     */
    @Modifying
    @Query("DELETE FROM EmailVerification ev WHERE ev.expiresAt < :now")
    int deleteExpiredVerifications(@Param("now") LocalDateTime now);

    /**
     * 标记验证码为已使用
     */
    @Modifying
    @Query("UPDATE EmailVerification ev SET ev.isUsed = true WHERE ev.id = :id")
    int markAsUsed(@Param("id") Long id);

    /**
     * 增加尝试次数
     */
    @Modifying
    @Query("UPDATE EmailVerification ev SET ev.attempts = ev.attempts + 1 WHERE ev.id = :id")
    int incrementAttempts(@Param("id") Long id);
}
