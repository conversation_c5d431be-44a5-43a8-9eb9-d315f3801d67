package com.promptstore.repository;

import com.promptstore.entity.Order;
import com.promptstore.enums.OrderStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 订单数据访问层
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

    /**
     * 根据订单号查找订单
     */
    Optional<Order> findByOrderNo(String orderNo);

    /**
     * 根据用户ID查找订单
     */
    Page<Order> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找订单
     */
    Page<Order> findByUserIdAndStatus(Long userId, OrderStatus status, Pageable pageable);

    /**
     * 检查用户是否已购买某个提示词
     */
    @Query("SELECT COUNT(oi) > 0 FROM Order o JOIN o.orderItems oi " +
           "WHERE o.userId = :userId AND oi.promptId = :promptId AND o.status = :status")
    boolean existsByUserIdAndPromptIdAndStatus(@Param("userId") Long userId, 
                                             @Param("promptId") Long promptId, 
                                             @Param("status") OrderStatus status);

    /**
     * 根据状态查找订单
     */
    Page<Order> findByStatus(OrderStatus status, Pageable pageable);

    /**
     * 根据用户ID统计订单数量
     */
    long countByUserId(Long userId);

    /**
     * 根据用户ID和状态统计订单数量
     */
    long countByUserIdAndStatus(Long userId, OrderStatus status);

    /**
     * 检查订单号是否存在
     */
    boolean existsByOrderNo(String orderNo);

    /**
     * 查找用户最新的订单
     */
    Order findTopByUserIdOrderByIdDesc(Long userId);
}
