package com.promptstore.repository;

import com.promptstore.entity.OrderItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单项数据访问层
 */
@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, Long> {

    /**
     * 根据订单ID查找订单项
     */
    List<OrderItem> findByOrderId(Long orderId);

    /**
     * 根据提示词ID查找订单项
     */
    List<OrderItem> findByPromptId(Long promptId);

    /**
     * 根据创作者ID查找订单项
     */
    Page<OrderItem> findByCreatorId(Long creatorId, Pageable pageable);

    /**
     * 根据订单ID删除订单项
     */
    void deleteByOrderId(Long orderId);

    /**
     * 统计创作者的总收益
     */
    @Query("SELECT COALESCE(SUM(oi.creatorEarnings), 0) FROM OrderItem oi WHERE oi.creatorId = :creatorId")
    BigDecimal sumCreatorEarningsByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 统计平台的总收益
     */
    @Query("SELECT COALESCE(SUM(oi.platformEarnings), 0) FROM OrderItem oi")
    BigDecimal sumPlatformEarnings();

    /**
     * 统计提示词的销售数量
     */
    @Query("SELECT COUNT(oi) FROM OrderItem oi WHERE oi.promptId = :promptId")
    long countByPromptId(@Param("promptId") Long promptId);

    /**
     * 统计创作者的销售数量
     */
    @Query("SELECT COUNT(oi) FROM OrderItem oi WHERE oi.creatorId = :creatorId")
    long countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取热门提示词（按销售数量排序）
     */
    @Query("SELECT oi.promptId, COUNT(oi) as saleCount FROM OrderItem oi " +
           "GROUP BY oi.promptId ORDER BY saleCount DESC")
    Page<Object[]> findHotPrompts(Pageable pageable);

    /**
     * 获取创作者的热门提示词
     */
    @Query("SELECT oi.promptId, COUNT(oi) as saleCount FROM OrderItem oi " +
           "WHERE oi.creatorId = :creatorId " +
           "GROUP BY oi.promptId ORDER BY saleCount DESC")
    Page<Object[]> findHotPromptsByCreator(@Param("creatorId") Long creatorId, Pageable pageable);

    /**
     * 检查用户是否购买过某个提示词
     */
    @Query("SELECT COUNT(oi) > 0 FROM OrderItem oi JOIN Order o ON oi.orderId = o.id " +
           "WHERE o.userId = :userId AND oi.promptId = :promptId AND o.status = 'PAID'")
    boolean existsByUserIdAndPromptId(@Param("userId") Long userId, @Param("promptId") Long promptId);
}
