package com.promptstore.repository;

import com.promptstore.entity.User;
import com.promptstore.enums.UserRole;
import com.promptstore.enums.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据微信OpenID查找用户
     */
    Optional<User> findByWechatOpenId(String wechatOpenId);

    /**
     * 根据微信UnionID查找用户
     */
    Optional<User> findByWechatUnionId(String wechatUnionId);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查微信OpenID是否存在
     */
    boolean existsByWechatOpenId(String wechatOpenId);

    /**
     * 根据角色查找用户
     */
    Page<User> findByRole(UserRole role, Pageable pageable);

    /**
     * 根据状态查找用户
     */
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    /**
     * 根据用户名模糊查询
     */
    Page<User> findByUsernameContainingIgnoreCase(String username, Pageable pageable);

    /**
     * 根据用户名模糊查询和角色查找用户
     */
    Page<User> findByUsernameContainingIgnoreCaseAndRole(String username, UserRole role, Pageable pageable);

    /**
     * 增加用户总收入
     */
    @Modifying
    @Query("UPDATE User u SET u.totalEarnings = u.totalEarnings + :amount WHERE u.id = :userId")
    void incrementTotalEarnings(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新用户总收入
     */
    @Modifying
    @Query("UPDATE User u SET u.totalEarnings = u.totalEarnings + :amount WHERE u.id = :userId")
    void updateTotalEarnings(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 增加用户总支出
     */
    @Modifying
    @Query("UPDATE User u SET u.totalSpent = u.totalSpent + :amount WHERE u.id = :userId")
    void incrementTotalSpent(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新用户总支出
     */
    @Modifying
    @Query("UPDATE User u SET u.totalSpent = u.totalSpent + :amount WHERE u.id = :userId")
    void updateTotalSpent(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 增加关注者数量
     */
    @Modifying
    @Query("UPDATE User u SET u.followerCount = u.followerCount + 1 WHERE u.id = :userId")
    void incrementFollowerCount(@Param("userId") Long userId);

    /**
     * 减少关注者数量
     */
    @Modifying
    @Query("UPDATE User u SET u.followerCount = u.followerCount - 1 WHERE u.id = :userId AND u.followerCount > 0")
    void decrementFollowerCount(@Param("userId") Long userId);

    /**
     * 增加关注数量
     */
    @Modifying
    @Query("UPDATE User u SET u.followingCount = u.followingCount + 1 WHERE u.id = :userId")
    void incrementFollowingCount(@Param("userId") Long userId);

    /**
     * 减少关注数量
     */
    @Modifying
    @Query("UPDATE User u SET u.followingCount = u.followingCount - 1 WHERE u.id = :userId AND u.followingCount > 0")
    void decrementFollowingCount(@Param("userId") Long userId);

    /**
     * 获取创作者排行榜
     */
    @Query("SELECT u FROM User u WHERE u.role = 'CREATOR' AND u.status = 'ACTIVE' ORDER BY u.totalEarnings DESC")
    Page<User> findTopCreators(Pageable pageable);
}
