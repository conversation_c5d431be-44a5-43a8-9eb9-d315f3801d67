package com.promptstore.repository;

import com.promptstore.entity.UserFavorite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户收藏数据访问层
 */
@Repository
public interface UserFavoriteRepository extends JpaRepository<UserFavorite, Long> {

    /**
     * 根据用户ID和提示词ID查找收藏记录
     */
    Optional<UserFavorite> findByUserIdAndPromptId(Long userId, Long promptId);

    /**
     * 检查用户是否收藏了指定提示词
     */
    boolean existsByUserIdAndPromptId(Long userId, Long promptId);

    /**
     * 获取用户收藏的提示词列表（分页）
     */
    @Query("SELECT uf FROM UserFavorite uf JOIN FETCH uf.prompt p WHERE uf.userId = :userId ORDER BY uf.createdAt DESC")
    Page<UserFavorite> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户收藏的提示词ID列表
     */
    @Query("SELECT uf.promptId FROM UserFavorite uf WHERE uf.userId = :userId")
    List<Long> findPromptIdsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户收藏数量
     */
    long countByUserId(Long userId);

    /**
     * 统计提示词被收藏数量
     */
    long countByPromptId(Long promptId);

    /**
     * 删除用户对指定提示词的收藏
     */
    void deleteByUserIdAndPromptId(Long userId, Long promptId);
}
