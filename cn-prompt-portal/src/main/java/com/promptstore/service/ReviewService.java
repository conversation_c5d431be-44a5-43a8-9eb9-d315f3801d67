package com.promptstore.service;

import com.promptstore.dto.request.ReviewRequest;
import com.promptstore.dto.response.ReviewResponse;
import com.promptstore.dto.response.ReviewStatistics;
import com.promptstore.entity.Prompt;
import com.promptstore.entity.PromptReview;
import com.promptstore.repository.PromptRepository;
import com.promptstore.repository.PromptReviewRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 评价服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReviewService {

    private final PromptReviewRepository reviewRepository;
    private final PromptRepository promptRepository;

    /**
     * 添加或更新评价
     */
    @Transactional
    public ReviewResponse addOrUpdateReview(Long userId, Long promptId, ReviewRequest request) {
        // 检查提示词是否存在
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new RuntimeException("提示词不存在"));

        // 检查是否已经评价过
        PromptReview review = reviewRepository.findByUserIdAndPromptId(userId, promptId)
            .orElse(null);

        if (review == null) {
            // 创建新评价
            review = PromptReview.builder()
                .userId(userId)
                .promptId(promptId)
                .rating(request.getRating())
                .content(request.getContent())
                .build();
        } else {
            // 更新现有评价
            review.setRating(request.getRating());
            review.setContent(request.getContent());
        }

        review = reviewRepository.save(review);

        // 更新提示词的评分统计
        updatePromptRatingStatistics(promptId);

        log.info("评价保存成功: userId={}, promptId={}, rating={}", userId, promptId, request.getRating());

        return ReviewResponse.from(review);
    }

    /**
     * 删除评价
     */
    @Transactional
    public void deleteReview(Long userId, Long promptId) {
        if (!reviewRepository.existsByUserIdAndPromptId(userId, promptId)) {
            throw new RuntimeException("评价不存在");
        }

        reviewRepository.deleteByUserIdAndPromptId(userId, promptId);

        // 更新提示词的评分统计
        updatePromptRatingStatistics(promptId);

        log.info("评价删除成功: userId={}, promptId={}", userId, promptId);
    }

    /**
     * 获取用户对提示词的评价
     */
    public ReviewResponse getUserReview(Long userId, Long promptId) {
        return reviewRepository.findByUserIdAndPromptId(userId, promptId)
            .map(ReviewResponse::from)
            .orElse(null);
    }

    /**
     * 获取提示词的评价列表
     */
    public Page<ReviewResponse> getPromptReviews(Long promptId, Pageable pageable) {
        Page<PromptReview> reviews = reviewRepository.findByPromptIdOrderByCreatedAtDesc(promptId, pageable);
        return reviews.map(ReviewResponse::from);
    }

    /**
     * 获取用户的评价列表
     */
    public Page<ReviewResponse> getUserReviews(Long userId, Pageable pageable) {
        Page<PromptReview> reviews = reviewRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return reviews.map(ReviewResponse::from);
    }

    /**
     * 获取提示词评价统计
     */
    public ReviewStatistics getPromptReviewStatistics(Long promptId) {
        long totalCount = reviewRepository.countByPromptId(promptId);
        BigDecimal averageRating = reviewRepository.calculateAverageRating(promptId);
        
        // 获取各星级分布
        Object[][] ratingData = reviewRepository.countByPromptIdGroupByRating(promptId);
        Map<Integer, Long> ratingDistribution = new HashMap<>();
        
        // 初始化所有星级为0
        for (int i = 1; i <= 5; i++) {
            ratingDistribution.put(i, 0L);
        }
        
        // 填充实际数据
        for (Object[] data : ratingData) {
            Integer rating = (Integer) data[0];
            Long count = (Long) data[1];
            ratingDistribution.put(rating, count);
        }

        return ReviewStatistics.builder()
            .totalCount(totalCount)
            .averageRating(averageRating != null ? averageRating.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
            .ratingDistribution(ratingDistribution)
            .build();
    }

    /**
     * 检查用户是否已评价
     */
    public boolean hasUserReviewed(Long userId, Long promptId) {
        return reviewRepository.existsByUserIdAndPromptId(userId, promptId);
    }

    /**
     * 更新提示词的评分统计
     */
    @Transactional
    public void updatePromptRatingStatistics(Long promptId) {
        long reviewCount = reviewRepository.countByPromptId(promptId);
        BigDecimal averageRating = reviewRepository.calculateAverageRating(promptId);

        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new RuntimeException("提示词不存在"));

        prompt.setReviewCount((int) reviewCount);
        prompt.setAverageRating(averageRating != null ? averageRating.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

        promptRepository.save(prompt);

        log.info("更新提示词评分统计: promptId={}, reviewCount={}, averageRating={}", 
            promptId, reviewCount, averageRating);
    }
}
