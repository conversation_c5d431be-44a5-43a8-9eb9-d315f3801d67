package com.promptstore.service;

import com.promptstore.dto.request.AuditRequest;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.entity.Prompt;
import com.promptstore.enums.AuditStatus;
import com.promptstore.repository.PromptRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 审核服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final PromptRepository promptRepository;

    /**
     * 获取待审核的提示词列表（分页）
     */
    public Page<PromptResponse> getPendingPrompts(Pageable pageable, AuditStatus status, String keyword) {
        Specification<Prompt> spec = Specification.where(null);

        // 审核状态筛选
        if (status != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("auditStatus"), status));
        }

        // 关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            spec = spec.and((root, query, cb) -> 
                cb.or(
                    cb.like(root.get("title"), "%" + keyword + "%"),
                    cb.like(root.get("description"), "%" + keyword + "%")
                )
            );
        }

        // 按创建时间倒序
        Page<Prompt> prompts = promptRepository.findAll(spec, pageable);
        
        return prompts.map(PromptResponse::from);
    }

    /**
     * 审核提示词
     */
    @Transactional
    public void auditPrompt(Long promptId, Long auditorId, AuditRequest request) {
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new RuntimeException("提示词不存在"));

        // 更新审核信息
        prompt.setAuditStatus(request.getAuditStatus());
        prompt.setAuditReason(request.getAuditReason());
        prompt.setAuditorId(auditorId);
        prompt.setAuditTime(LocalDateTime.now());

        // 如果审核通过，设置发布时间
        if (request.getAuditStatus() == AuditStatus.APPROVED && prompt.getPublishedAt() == null) {
            prompt.setPublishedAt(LocalDateTime.now());
        }

        promptRepository.save(prompt);

        log.info("提示词审核完成: promptId={}, auditorId={}, status={}, reason={}", 
            promptId, auditorId, request.getAuditStatus(), request.getAuditReason());
    }

    /**
     * 获取审核统计信息
     */
    public AuditStatistics getAuditStatistics() {
        long pendingCount = promptRepository.countByAuditStatus(AuditStatus.PENDING);
        long approvedCount = promptRepository.countByAuditStatus(AuditStatus.APPROVED);
        long rejectedCount = promptRepository.countByAuditStatus(AuditStatus.REJECTED);

        return AuditStatistics.builder()
            .pendingCount(pendingCount)
            .approvedCount(approvedCount)
            .rejectedCount(rejectedCount)
            .totalCount(pendingCount + approvedCount + rejectedCount)
            .build();
    }

    /**
     * 审核统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class AuditStatistics {
        private long pendingCount;
        private long approvedCount;
        private long rejectedCount;
        private long totalCount;
    }
}
