package com.promptstore.service;

import com.promptstore.dto.request.PasswordChangeRequest;
import com.promptstore.dto.request.UserProfileUpdateRequest;
import com.promptstore.dto.response.UserProfileResponse;
import com.promptstore.entity.User;
import com.promptstore.repository.PromptRepository;
import com.promptstore.repository.PromptReviewRepository;
import com.promptstore.repository.UserFavoriteRepository;
import com.promptstore.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户信息服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {

    private final UserRepository userRepository;
    private final PromptRepository promptRepository;
    private final UserFavoriteRepository favoriteRepository;
    private final PromptReviewRepository reviewRepository;
    private final PasswordEncoder passwordEncoder;
    private final FileUploadService fileUploadService;

    /**
     * 获取用户信息
     */
    public UserProfileResponse getUserProfile(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 获取统计信息
        long promptCount = promptRepository.countByUserId(userId);
        long favoriteCount = favoriteRepository.countByUserId(userId);
        long reviewCount = reviewRepository.countByUserId(userId);

        return UserProfileResponse.from(user, promptCount, favoriteCount, reviewCount);
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public UserProfileResponse updateUserProfile(Long userId, UserProfileUpdateRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查用户名是否已被使用
        if (request.getUsername() != null && !request.getUsername().equals(user.getUsername())) {
            if (userRepository.existsByUsername(request.getUsername())) {
                throw new RuntimeException("用户名已被使用");
            }
            user.setUsername(request.getUsername());
        }

        // 检查邮箱是否已被使用
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new RuntimeException("邮箱已被使用");
            }
            user.setEmail(request.getEmail());
        }

        // 更新其他字段
        if (request.getBio() != null) {
            user.setBio(request.getBio());
        }
        if (request.getAvatar() != null) {
            user.setAvatar(request.getAvatar());
        }
        if (request.getRealName() != null) {
            user.setRealName(request.getRealName());
        }
        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }

        User savedUser = userRepository.save(user);

        log.info("用户信息更新成功: userId={}, username={}", userId, savedUser.getUsername());

        return UserProfileResponse.from(savedUser);
    }

    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long userId, PasswordChangeRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 验证当前密码
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPasswordHash())) {
            throw new RuntimeException("当前密码不正确");
        }

        // 验证新密码和确认密码是否一致
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("新密码和确认密码不一致");
        }

        // 更新密码
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 上传头像
     */
    @Transactional
    public String uploadAvatar(Long userId, MultipartFile file) {
        try {
            // 上传文件
            String avatarUrl = fileUploadService.uploadImage(file, userId);

            // 更新用户头像
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

            user.setAvatar(avatarUrl);
            userRepository.save(user);

            log.info("用户头像上传成功: userId={}, avatarUrl={}", userId, avatarUrl);

            return avatarUrl;
        } catch (Exception e) {
            log.error("用户头像上传失败: userId={}", userId, e);
            throw new RuntimeException("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户基本信息（公开信息）
     */
    public UserProfileResponse getPublicUserProfile(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 获取统计信息
        long promptCount = promptRepository.countByUserId(userId);
        long favoriteCount = favoriteRepository.countByUserId(userId);
        long reviewCount = reviewRepository.countByUserId(userId);

        UserProfileResponse response = UserProfileResponse.from(user, promptCount, favoriteCount, reviewCount);
        
        // 隐藏私人信息
        response.setEmail(null);
        response.setPhone(null);
        response.setRealName(null);

        return response;
    }
}
