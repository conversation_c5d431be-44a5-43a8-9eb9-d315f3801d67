package com.promptstore.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.promptstore.dto.WechatAccessToken;
import com.promptstore.dto.WechatUserInfo;
import com.promptstore.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 微信服务
 */
@Service
@Slf4j
public class WechatService {

    @Value("${app.wechat.app-id}")
    private String appId;

    @Value("${app.wechat.app-secret}")
    private String appSecret;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public WechatService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 通过code获取用户信息
     */
    public WechatUserInfo getUserInfo(String code) {
        // 1. 通过code获取access_token
        WechatAccessToken accessToken = getAccessToken(code);
        
        // 2. 通过access_token获取用户信息
        return getUserInfoByToken(accessToken.getAccessToken(), accessToken.getOpenId());
    }

    /**
     * 通过code获取access_token
     */
    private WechatAccessToken getAccessToken(String code) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
            appId, appSecret, code
        );
        
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            
            if (jsonNode.has("errcode")) {
                String errorMsg = jsonNode.get("errmsg").asText();
                log.error("获取微信access_token失败: {}", errorMsg);
                throw new BusinessException("获取微信access_token失败：" + errorMsg);
            }
            
            return WechatAccessToken.builder()
                .accessToken(jsonNode.get("access_token").asText())
                .expiresIn(jsonNode.get("expires_in").asInt())
                .refreshToken(jsonNode.get("refresh_token").asText())
                .openId(jsonNode.get("openid").asText())
                .scope(jsonNode.get("scope").asText())
                .build();
                
        } catch (Exception e) {
            log.error("获取微信access_token失败", e);
            throw new BusinessException("微信登录失败，请重试");
        }
    }

    /**
     * 通过access_token获取用户信息
     */
    private WechatUserInfo getUserInfoByToken(String accessToken, String openId) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
            accessToken, openId
        );
        
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            
            if (jsonNode.has("errcode")) {
                String errorMsg = jsonNode.get("errmsg").asText();
                log.error("获取微信用户信息失败: {}", errorMsg);
                throw new BusinessException("获取微信用户信息失败：" + errorMsg);
            }
            
            return WechatUserInfo.builder()
                .openId(jsonNode.get("openid").asText())
                .unionId(jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : null)
                .nickname(jsonNode.get("nickname").asText())
                .avatarUrl(jsonNode.get("headimgurl").asText())
                .sex(jsonNode.get("sex").asInt())
                .country(jsonNode.get("country").asText())
                .province(jsonNode.get("province").asText())
                .city(jsonNode.get("city").asText())
                .build();
                
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            throw new BusinessException("获取用户信息失败，请重试");
        }
    }

    /**
     * 验证access_token是否有效
     */
    public boolean validateAccessToken(String accessToken, String openId) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/auth?access_token=%s&openid=%s",
            accessToken, openId
        );
        
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            
            return jsonNode.get("errcode").asInt() == 0;
        } catch (Exception e) {
            log.error("验证微信access_token失败", e);
            return false;
        }
    }

    /**
     * 刷新access_token
     */
    public WechatAccessToken refreshAccessToken(String refreshToken) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=%s&grant_type=refresh_token&refresh_token=%s",
            appId, refreshToken
        );
        
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            
            if (jsonNode.has("errcode")) {
                String errorMsg = jsonNode.get("errmsg").asText();
                log.error("刷新微信access_token失败: {}", errorMsg);
                throw new BusinessException("刷新微信access_token失败：" + errorMsg);
            }
            
            return WechatAccessToken.builder()
                .accessToken(jsonNode.get("access_token").asText())
                .expiresIn(jsonNode.get("expires_in").asInt())
                .refreshToken(jsonNode.get("refresh_token").asText())
                .openId(jsonNode.get("openid").asText())
                .scope(jsonNode.get("scope").asText())
                .build();
                
        } catch (Exception e) {
            log.error("刷新微信access_token失败", e);
            throw new BusinessException("刷新令牌失败，请重新登录");
        }
    }
}
