package com.promptstore.service;

import com.promptstore.dto.response.CategoryResponse;
import com.promptstore.entity.Category;
import com.promptstore.exception.ResourceNotFoundException;
import com.promptstore.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class CategoryService {

    private final CategoryRepository categoryRepository;

    /**
     * 获取所有活跃的根分类
     */
    @Cacheable(value = "categories", key = "'root'")
    public List<CategoryResponse> getRootCategories() {
        List<Category> categories = categoryRepository.findByParentIdIsNullAndIsActiveTrueOrderBySortOrder();
        
        return categories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定父分类的子分类
     */
    @Cacheable(value = "categories", key = "'children:' + #parentId")
    public List<CategoryResponse> getChildCategories(Long parentId) {
        List<Category> categories = categoryRepository.findByParentIdAndIsActiveTrue(parentId);
        
        return categories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有活跃分类
     */
    @Cacheable(value = "categories", key = "'all'")
    public List<CategoryResponse> getAllCategories() {
        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrder();
        
        return categories.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 分页获取分类
     */
    public Page<CategoryResponse> getCategories(Pageable pageable) {
        Page<Category> categoryPage = categoryRepository.findByIsActiveTrue(pageable);
        
        return categoryPage.map(this::convertToResponse);
    }

    /**
     * 根据ID获取分类
     */
    public CategoryResponse getCategoryById(Long id) {
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));
                
        return convertToResponse(category);
    }

    /**
     * 根据slug获取分类
     */
    public CategoryResponse getCategoryBySlug(String slug) {
        Category category = categoryRepository.findBySlug(slug)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));
                
        return convertToResponse(category);
    }

    /**
     * 搜索分类
     */
    public Page<CategoryResponse> searchCategories(String name, Pageable pageable) {
        Page<Category> categoryPage = categoryRepository.findByNameContainingIgnoreCaseAndIsActiveTrue(name, pageable);
        
        return categoryPage.map(this::convertToResponse);
    }

    /**
     * 获取热门分类
     */
    @Cacheable(value = "categories", key = "'hot:' + #limit")
    public List<CategoryResponse> getHotCategories(int limit) {
        Page<Category> categoryPage = categoryRepository.findHotCategories(
                org.springframework.data.domain.PageRequest.of(0, limit)
        );
        
        return categoryPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应DTO
     */
    private CategoryResponse convertToResponse(Category category) {
        return CategoryResponse.builder()
                .id(category.getId())
                .name(category.getName())
                .slug(category.getSlug())
                .description(category.getDescription())
                .parentId(category.getParentId())
                .iconUrl(category.getIconUrl())
                .sortOrder(category.getSortOrder())
                .promptCount(category.getPromptCount())
                .isActive(category.getIsActive())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
    }
}
