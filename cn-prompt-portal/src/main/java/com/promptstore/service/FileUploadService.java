package com.promptstore.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.HttpMethod;
import com.promptstore.config.OssConfig;
import com.promptstore.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileUploadService {

    private final OSS ossClient;
    private final OssConfig ossConfig;

    /**
     * 支持的图片格式
     */
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    /**
     * 最大文件大小 (5MB)
     */
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传图片文件
     *
     * @param file 上传的文件
     * @param userId 用户ID
     * @return 文件访问URL
     */
    public String uploadImage(MultipartFile file, Long userId) {
        try {
            // 验证文件
            validateImageFile(file);

            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename(), userId);

            // 设置对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            metadata.setContentDisposition("inline");

            // 上传文件
            try (InputStream inputStream = file.getInputStream()) {
                PutObjectRequest putObjectRequest = new PutObjectRequest(
                        ossConfig.getBucketName(),
                        fileName,
                        inputStream,
                        metadata
                );

                PutObjectResult result = ossClient.putObject(putObjectRequest);
                log.info("文件上传成功: userId={}, fileName={}, etag={}", 
                        userId, fileName, result.getETag());

                // 返回文件访问URL
                return getFileUrl(fileName);
            }

        } catch (IOException e) {
            log.error("文件上传失败: userId={}, fileName={}", userId, file.getOriginalFilename(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        try {
            if (!StringUtils.hasText(fileUrl)) {
                return;
            }

            // 从URL中提取文件名
            String fileName = extractFileNameFromUrl(fileUrl);
            if (StringUtils.hasText(fileName)) {
                ossClient.deleteObject(ossConfig.getBucketName(), fileName);
                log.info("文件删除成功: fileName={}", fileName);
            }
        } catch (Exception e) {
            log.error("文件删除失败: fileUrl={}", fileUrl, e);
            // 删除失败不抛异常，避免影响主业务
        }
    }

    /**
     * 批量删除文件
     *
     * @param fileUrls 文件URL列表
     */
    public void deleteFiles(List<String> fileUrls) {
        if (fileUrls == null || fileUrls.isEmpty()) {
            return;
        }

        for (String fileUrl : fileUrls) {
            deleteFile(fileUrl);
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小不能超过5MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!ALLOWED_IMAGE_TYPES.contains(contentType)) {
            throw new BusinessException("只支持JPG、PNG、GIF、WebP格式的图片");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new BusinessException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        List<String> allowedExtensions = Arrays.asList("jpg", "jpeg", "png", "gif", "webp");
        if (!allowedExtensions.contains(extension)) {
            throw new BusinessException("文件扩展名不支持");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename, Long userId) {
        String extension = getFileExtension(originalFilename);
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        return String.format("%s%s/user_%d/%s.%s", 
                ossConfig.getPathPrefix(), dateStr, userId, uuid, extension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 获取文件访问URL（返回带签名的URL）
     */
    private String getFileUrl(String fileName) {
        try {
            // 对于OSS私有存储，直接返回带签名的URL（2小时有效期）
            return generatePresignedUrl(fileName, 120);
        } catch (Exception e) {
            log.warn("生成签名URL失败，返回原始URL: fileName={}", fileName, e);

            // 如果签名URL生成失败，返回原始URL作为fallback
            if (StringUtils.hasText(ossConfig.getCustomDomain())) {
                // 使用自定义域名
                String protocol = ossConfig.isUseHttps() ? "https" : "http";
                return String.format("%s://%s/%s", protocol, ossConfig.getCustomDomain(), fileName);
            } else {
                // 使用默认域名
                String protocol = ossConfig.isUseHttps() ? "https" : "http";
                return String.format("%s://%s.%s/%s",
                        protocol, ossConfig.getBucketName(), ossConfig.getEndpoint(), fileName);
            }
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String fileUrl) {
        if (!StringUtils.hasText(fileUrl)) {
            return null;
        }

        try {
            // 移除协议和域名部分
            String fileName = fileUrl;
            if (fileName.startsWith("http://") || fileName.startsWith("https://")) {
                int slashIndex = fileName.indexOf("/", 8); // 跳过 "https://"
                if (slashIndex > 0) {
                    fileName = fileName.substring(slashIndex + 1);
                }
            }
            return fileName;
        } catch (Exception e) {
            log.warn("无法从URL中提取文件名: {}", fileUrl, e);
            return null;
        }
    }

    /**
     * 生成带签名的访问URL
     *
     * @param objectKey OSS对象键
     * @param expireMinutes 过期时间（分钟）
     * @return 带签名的URL
     */
    public String generatePresignedUrl(String objectKey, int expireMinutes) {
        try {
            // 设置过期时间
            Date expiration = new Date(System.currentTimeMillis() + expireMinutes * 60 * 1000L);

            // 创建生成预签名URL的请求
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                ossConfig.getBucketName(),
                objectKey,
                HttpMethod.GET
            );
            request.setExpiration(expiration);

            // 生成预签名URL
            URL url = ossClient.generatePresignedUrl(request);

            log.debug("生成预签名URL成功: objectKey={}, expireMinutes={}", objectKey, expireMinutes);
            return url.toString();

        } catch (Exception e) {
            log.error("生成预签名URL失败: objectKey={}", objectKey, e);
            throw new BusinessException("生成图片访问链接失败");
        }
    }

    /**
     * 从完整URL中提取ObjectKey并生成新的签名URL
     *
     * @param fullUrl 完整的OSS URL
     * @param expireMinutes 过期时间（分钟）
     * @return 带签名的URL
     */
    public String generatePresignedUrlFromFullUrl(String fullUrl, int expireMinutes) {
        try {
            if (!StringUtils.hasText(fullUrl)) {
                return null;
            }

            // 从完整URL中提取ObjectKey
            String objectKey = extractObjectKeyFromUrl(fullUrl);
            if (objectKey == null) {
                log.warn("无法从URL中提取ObjectKey: {}", fullUrl);
                return fullUrl; // 返回原URL
            }

            return generatePresignedUrl(objectKey, expireMinutes);

        } catch (Exception e) {
            log.error("从完整URL生成预签名URL失败: fullUrl={}", fullUrl, e);
            return fullUrl; // 返回原URL
        }
    }

    /**
     * 从完整URL中提取ObjectKey
     */
    private String extractObjectKeyFromUrl(String fullUrl) {
        try {
            // 移除域名部分，只保留路径
            String bucketDomain = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint();
            if (fullUrl.startsWith(bucketDomain)) {
                String objectKey = fullUrl.substring(bucketDomain.length() + 1); // +1 for the '/'
                // 移除查询参数（如果有的话）
                int queryIndex = objectKey.indexOf('?');
                if (queryIndex > 0) {
                    objectKey = objectKey.substring(0, queryIndex);
                }
                return objectKey;
            }
            return null;
        } catch (Exception e) {
            log.warn("提取ObjectKey失败: {}", fullUrl, e);
            return null;
        }
    }
}
