package com.promptstore.service;

import com.promptstore.dto.request.OrderCreateRequest;
import com.promptstore.dto.response.OrderResponse;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.entity.Order;
import com.promptstore.entity.OrderItem;
import com.promptstore.entity.Prompt;
import com.promptstore.entity.User;
import com.promptstore.enums.OrderStatus;
import com.promptstore.enums.PaymentMethod;
import com.promptstore.exception.BusinessException;
import com.promptstore.exception.ResourceNotFoundException;
import com.promptstore.repository.OrderRepository;
import com.promptstore.repository.OrderItemRepository;
import com.promptstore.repository.PromptRepository;
import com.promptstore.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 订单服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final PromptRepository promptRepository;
    private final UserRepository userRepository;
    private final PaymentService paymentService;

    /**
     * 创建订单
     */
    public OrderResponse createOrder(OrderCreateRequest request, Long userId) {
        // 验证用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 验证提示词
        List<Prompt> prompts = new ArrayList<>();
        List<Prompt> freePrompts = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Long promptId : request.getPromptIds()) {
            Prompt prompt = promptRepository.findById(promptId)
                    .orElseThrow(() -> new ResourceNotFoundException("提示词不存在: " + promptId));

            // 检查提示词状态
            if (prompt.getStatus() != com.promptstore.enums.PromptStatus.APPROVED) {
                throw new BusinessException("提示词未审核通过: " + prompt.getTitle());
            }

            // 检查是否已购买
            if (orderRepository.existsByUserIdAndPromptIdAndStatus(userId, promptId, OrderStatus.PAID)) {
                throw new BusinessException("您已购买过此提示词: " + prompt.getTitle());
            }

            if (prompt.getIsFree()) {
                freePrompts.add(prompt);
            } else {
                prompts.add(prompt);
                totalAmount = totalAmount.add(prompt.getPrice());
            }
        }

        // 处理免费提示词 - 直接创建已支付订单
        if (!freePrompts.isEmpty()) {
            for (Prompt freePrompt : freePrompts) {
                String freeOrderNo = generateOrderNo();
                Order freeOrder = Order.builder()
                        .orderNo(freeOrderNo)
                        .userId(userId)
                        .totalAmount(BigDecimal.ZERO)
                        .discountAmount(BigDecimal.ZERO)
                        .finalAmount(BigDecimal.ZERO)
                        .status(OrderStatus.PAID)
                        .paymentMethod(request.getPaymentMethod())
                        .paidAt(LocalDateTime.now())
                        .build();

                Order savedFreeOrder = orderRepository.save(freeOrder);

                // 创建免费订单项
                OrderItem freeOrderItem = OrderItem.builder()
                        .orderId(savedFreeOrder.getId())
                        .promptId(freePrompt.getId())
                        .promptTitle(freePrompt.getTitle())
                        .unitPrice(BigDecimal.ZERO)
                        .quantity(1)
                        .totalPrice(BigDecimal.ZERO)
                        .creatorId(freePrompt.getUserId())
                        .commissionRate(BigDecimal.ZERO)
                        .creatorEarnings(BigDecimal.ZERO)
                        .platformEarnings(BigDecimal.ZERO)
                        .build();

                orderItemRepository.save(freeOrderItem);

                log.info("用户 {} 获取免费提示词: {}", userId, freePrompt.getTitle());
            }
        }

        // 如果只有免费提示词，返回成功响应
        if (prompts.isEmpty()) {
            // 返回最后一个免费订单的信息
            if (!freePrompts.isEmpty()) {
                Order lastFreeOrder = orderRepository.findTopByUserIdOrderByIdDesc(userId);
                return convertToResponse(lastFreeOrder);
            }
        }

        // 创建订单
        String orderNo = generateOrderNo();
        Order order = Order.builder()
                .orderNo(orderNo)
                .userId(userId)
                .totalAmount(totalAmount)
                .discountAmount(BigDecimal.ZERO)
                .finalAmount(totalAmount)
                .status(OrderStatus.PENDING)
                .paymentMethod(request.getPaymentMethod())
                .build();

        Order savedOrder = orderRepository.save(order);

        // 创建订单项
        List<OrderItem> orderItems = new ArrayList<>();
        for (Prompt prompt : prompts) {
            BigDecimal commissionRate = new BigDecimal("0.7000"); // 70%给创作者
            BigDecimal creatorEarnings = prompt.getPrice().multiply(commissionRate);
            BigDecimal platformEarnings = prompt.getPrice().subtract(creatorEarnings);

            OrderItem orderItem = OrderItem.builder()
                    .orderId(savedOrder.getId())
                    .promptId(prompt.getId())
                    .promptTitle(prompt.getTitle())
                    .unitPrice(prompt.getPrice())
                    .quantity(1)
                    .totalPrice(prompt.getPrice())
                    .creatorId(prompt.getUserId())
                    .commissionRate(commissionRate)
                    .creatorEarnings(creatorEarnings)
                    .platformEarnings(platformEarnings)
                    .build();

            orderItems.add(orderItem);
        }

        savedOrder.setOrderItems(orderItems);
        orderRepository.save(savedOrder);

        log.info("创建订单成功: orderNo={}, userId={}, amount={}", orderNo, userId, totalAmount);

        return convertToResponse(savedOrder);
    }

    /**
     * 获取用户订单列表
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderResponse> getUserOrders(Long userId, OrderStatus status, 
                                                    Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
        
        Page<Order> orderPage;
        if (status != null) {
            orderPage = orderRepository.findByUserIdAndStatus(userId, status, pageable);
        } else {
            orderPage = orderRepository.findByUserId(userId, pageable);
        }

        List<OrderResponse> orders = orderPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResponse.of(
                orders,
                orderPage.getTotalElements(),
                page,
                size
        );
    }

    /**
     * 获取用户订单列表
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderResponse> getUserOrders(Long userId, String status, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "id"));

        Page<Order> orderPage;
        if (status != null && !status.isEmpty()) {
            try {
                OrderStatus orderStatus = OrderStatus.valueOf(status.toUpperCase());
                orderPage = orderRepository.findByUserIdAndStatus(userId, orderStatus, pageable);
            } catch (IllegalArgumentException e) {
                throw new BusinessException("无效的订单状态: " + status);
            }
        } else {
            orderPage = orderRepository.findByUserId(userId, pageable);
        }

        List<OrderResponse> orderResponses = orderPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResponse.<OrderResponse>builder()
                .data(orderResponses)
                .total(orderPage.getTotalElements())
                .page(page)
                .size(size)
                .totalPages(orderPage.getTotalPages())
                .hasNext(orderPage.hasNext())
                .hasPrevious(orderPage.hasPrevious())
                .build();
    }

    /**
     * 根据ID获取订单
     */
    @Transactional(readOnly = true)
    public OrderResponse getOrderById(Long id, Long userId) {
        Order order = orderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        // 检查权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权访问此订单");
        }

        return convertToResponse(order);
    }

    /**
     * 根据订单号获取订单
     */
    @Transactional(readOnly = true)
    public OrderResponse getOrderByNo(String orderNo, Long userId) {
        Order order = orderRepository.findByOrderNo(orderNo)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        // 检查权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权访问此订单");
        }

        return convertToResponse(order);
    }

    /**
     * 支付订单
     */
    public String payOrder(Long orderId, Long userId) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        // 检查权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权操作此订单");
        }

        // 检查订单状态
        if (order.getStatus() != OrderStatus.PENDING) {
            throw new BusinessException("订单状态不正确");
        }

        // 调用支付服务
        String paymentUrl = paymentService.createPayment(order);

        log.info("创建支付: orderId={}, userId={}, paymentUrl={}", orderId, userId, paymentUrl);

        return paymentUrl;
    }

    /**
     * 处理支付回调
     */
    public void handlePaymentCallback(String orderNo, String paymentId, boolean success) {
        Order order = orderRepository.findByOrderNo(orderNo)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        if (success) {
            // 支付成功
            order.setStatus(OrderStatus.PAID);
            order.setPaymentId(paymentId);
            order.setPaidAt(LocalDateTime.now());

            // 更新提示词下载次数
            for (OrderItem item : order.getOrderItems()) {
                promptRepository.incrementDownloadCount(item.getPromptId());
            }

            // 更新创作者收益
            for (OrderItem item : order.getOrderItems()) {
                userRepository.updateTotalEarnings(item.getCreatorId(), item.getCreatorEarnings());
            }

            // 更新用户消费
            userRepository.updateTotalSpent(order.getUserId(), order.getFinalAmount());

            log.info("订单支付成功: orderNo={}, paymentId={}", orderNo, paymentId);
        } else {
            // 支付失败，可以选择取消订单或保持待支付状态
            log.warn("订单支付失败: orderNo={}, paymentId={}", orderNo, paymentId);
        }

        orderRepository.save(order);
    }

    /**
     * 取消订单
     */
    public void cancelOrder(Long orderId, Long userId) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        // 检查权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权操作此订单");
        }

        // 检查订单状态
        if (order.getStatus() != OrderStatus.PENDING) {
            throw new BusinessException("只能取消待支付的订单");
        }

        order.setStatus(OrderStatus.CANCELLED);
        order.setCancelledAt(LocalDateTime.now());
        orderRepository.save(order);

        log.info("取消订单: orderId={}, userId={}", orderId, userId);
    }

    /**
     * 检查用户是否已购买提示词
     */
    @Transactional(readOnly = true)
    public boolean hasUserPurchasedPrompt(Long userId, Long promptId) {
        return orderRepository.existsByUserIdAndPromptIdAndStatus(userId, promptId, OrderStatus.PAID);
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 转换为响应DTO
     */
    private OrderResponse convertToResponse(Order order) {
        return OrderResponse.builder()
                .id(order.getId())
                .orderNo(order.getOrderNo())
                .userId(order.getUserId())
                .totalAmount(order.getTotalAmount())
                .discountAmount(order.getDiscountAmount())
                .finalAmount(order.getFinalAmount())
                .status(order.getStatus())
                .paymentMethod(order.getPaymentMethod())
                .paymentId(order.getPaymentId())
                .paidAt(order.getPaidAt())
                .cancelledAt(order.getCancelledAt())
                .refundedAt(order.getRefundedAt())
                .createdAt(order.getCreatedAt())
                .updatedAt(order.getUpdatedAt())
                .build();
    }
}
