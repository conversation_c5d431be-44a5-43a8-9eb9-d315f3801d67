package com.promptstore.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dm.model.v20151123.SingleSendMailRequest;
import com.aliyuncs.dm.model.v20151123.SingleSendMailResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.promptstore.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 阿里云邮箱服务
 */
@Service
@Slf4j
public class AliyunMailService {

    @Value("${app.aliyun.mail.access-key-id}")
    private String accessKeyId;

    @Value("${app.aliyun.mail.access-key-secret}")
    private String accessKeySecret;

    @Value("${app.aliyun.mail.from-address}")
    private String fromAddress;

    @Value("${app.aliyun.mail.from-name}")
    private String fromName;

    private static final String REGION_ID = "cn-hangzhou";

    /**
     * 发送验证码邮件
     */
    public void sendVerificationCode(String toEmail, String code, String type) {
        try {
            // 创建阿里云客户端
            DefaultProfile profile = DefaultProfile.getProfile(REGION_ID, accessKeyId, accessKeySecret);
            IAcsClient client = new DefaultAcsClient(profile);

            // 构建邮件请求
            SingleSendMailRequest request = new SingleSendMailRequest();
            request.setAccountName(fromAddress);
            request.setFromAlias(fromName);
            request.setAddressType(1);
            request.setToAddress(toEmail);
            request.setReplyToAddress(false);
            
            // 设置邮件主题和内容
            String subject = getEmailSubject(type);
            String htmlBody = buildEmailContent(code, type);
            
            request.setSubject(subject);
            request.setHtmlBody(htmlBody);

            // 发送邮件
            SingleSendMailResponse response = client.getAcsResponse(request);
            
            if (response != null && "OK".equals(response.getRequestId())) {
                log.info("验证码邮件发送成功: email={}, type={}, code={}", toEmail, type, code);
            } else {
                log.error("验证码邮件发送失败: email={}, response={}", toEmail, response);
                throw new BusinessException("邮件发送失败");
            }
            
        } catch (ClientException e) {
            log.error("阿里云邮件服务异常: email={}, error={}", toEmail, e.getMessage(), e);
            throw new BusinessException("邮件发送失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("发送验证码邮件异常: email={}", toEmail, e);
            throw new BusinessException("邮件发送失败");
        }
    }

    /**
     * 获取邮件主题
     */
    private String getEmailSubject(String type) {
        switch (type) {
            case "REGISTER":
                return "【提示词商店】注册验证码";
            case "LOGIN":
                return "【提示词商店】登录验证码";
            case "RESET_PASSWORD":
                return "【提示词商店】重置密码验证码";
            case "EMAIL_VERIFICATION":
                return "【提示词商店】邮箱验证码";
            case "BIND_EMAIL":
                return "【提示词商店】绑定邮箱验证码";
            default:
                return "【提示词商店】验证码";
        }
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(String code, String type) {
        String action = getActionDescription(type);
        
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>验证码邮件</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .code-box { background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
                    .code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
                    .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>提示词商店</h1>
                        <p>您的专业AI提示词交易平台</p>
                    </div>
                    <div class="content">
                        <h2>%s验证码</h2>
                        <p>您好！</p>
                        <p>您正在进行%s操作，请使用以下验证码完成验证：</p>
                        
                        <div class="code-box">
                            <div class="code">%s</div>
                        </div>
                        
                        <div class="warning">
                            <strong>安全提醒：</strong>
                            <ul>
                                <li>验证码有效期为5分钟，请及时使用</li>
                                <li>请勿将验证码告知他人</li>
                                <li>如非本人操作，请忽略此邮件</li>
                            </ul>
                        </div>
                        
                        <p>如有任何问题，请联系我们的客服团队。</p>
                        <p>感谢您使用提示词商店！</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>© 2024 提示词商店 版权所有</p>
                    </div>
                </div>
            </body>
            </html>
            """, action, action, code);
    }

    /**
     * 获取操作描述
     */
    private String getActionDescription(String type) {
        switch (type) {
            case "REGISTER":
                return "注册";
            case "LOGIN":
                return "登录";
            case "RESET_PASSWORD":
                return "重置密码";
            case "EMAIL_VERIFICATION":
                return "邮箱验证";
            case "BIND_EMAIL":
                return "绑定邮箱";
            default:
                return "验证";
        }
    }
}
