package com.promptstore.service;

import com.promptstore.entity.Order;
import com.promptstore.enums.PaymentMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 支付服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentService {

    @Value("${app.payment.alipay.app-id:}")
    private String alipayAppId;

    @Value("${app.payment.wechat.app-id:}")
    private String wechatAppId;

    @Value("${app.payment.notify-url:http://localhost:8080/api/webhook/payments}")
    private String notifyUrl;

    @Value("${app.payment.return-url:http://localhost:8080/api/payment/return}")
    private String returnUrl;

    /**
     * 创建支付
     */
    public String createPayment(Order order) {
        switch (order.getPaymentMethod()) {
            case ALIPAY:
                return createAlipayPayment(order);
            case WECHAT:
                return createWechatPayment(order);
            default:
                throw new IllegalArgumentException("不支持的支付方式: " + order.getPaymentMethod());
        }
    }

    /**
     * 创建支付宝支付
     */
    private String createAlipayPayment(Order order) {
        // TODO: 集成支付宝SDK
        log.info("创建支付宝支付: orderNo={}, amount={}", order.getOrderNo(), order.getFinalAmount());
        
        // 模拟支付URL
        return String.format("https://openapi.alipay.com/gateway.do?method=alipay.trade.page.pay&app_id=%s&out_trade_no=%s&total_amount=%s&subject=提示词购买&notify_url=%s&return_url=%s",
                alipayAppId, order.getOrderNo(), order.getFinalAmount(), notifyUrl, returnUrl);
    }

    /**
     * 创建微信支付
     */
    private String createWechatPayment(Order order) {
        // TODO: 集成微信支付SDK
        log.info("创建微信支付: orderNo={}, amount={}", order.getOrderNo(), order.getFinalAmount());
        
        // 模拟支付URL
        return String.format("https://api.mch.weixin.qq.com/pay/unifiedorder?appid=%s&out_trade_no=%s&total_fee=%s&body=提示词购买&notify_url=%s",
                wechatAppId, order.getOrderNo(), order.getFinalAmount().multiply(new java.math.BigDecimal("100")).intValue(), notifyUrl);
    }

    /**
     * 验证支付回调签名
     */
    public boolean verifyPaymentCallback(PaymentMethod paymentMethod, String signature, String data) {
        switch (paymentMethod) {
            case ALIPAY:
                return verifyAlipaySignature(signature, data);
            case WECHAT:
                return verifyWechatSignature(signature, data);
            default:
                return false;
        }
    }

    /**
     * 验证支付宝签名
     */
    private boolean verifyAlipaySignature(String signature, String data) {
        // TODO: 实现支付宝签名验证
        log.info("验证支付宝签名: signature={}", signature);
        return true; // 模拟验证通过
    }

    /**
     * 验证微信签名
     */
    private boolean verifyWechatSignature(String signature, String data) {
        // TODO: 实现微信签名验证
        log.info("验证微信签名: signature={}", signature);
        return true; // 模拟验证通过
    }

    /**
     * 处理支付宝回调
     */
    public PaymentCallbackResult handleAlipayCallback(String tradeNo, String outTradeNo, String tradeStatus) {
        log.info("处理支付宝回调: tradeNo={}, outTradeNo={}, tradeStatus={}", tradeNo, outTradeNo, tradeStatus);
        
        boolean success = "TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus);
        
        return PaymentCallbackResult.builder()
                .success(success)
                .orderNo(outTradeNo)
                .paymentId(tradeNo)
                .build();
    }

    /**
     * 处理微信回调
     */
    public PaymentCallbackResult handleWechatCallback(String transactionId, String outTradeNo, String resultCode) {
        log.info("处理微信回调: transactionId={}, outTradeNo={}, resultCode={}", transactionId, outTradeNo, resultCode);
        
        boolean success = "SUCCESS".equals(resultCode);
        
        return PaymentCallbackResult.builder()
                .success(success)
                .orderNo(outTradeNo)
                .paymentId(transactionId)
                .build();
    }

    /**
     * 支付回调结果
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class PaymentCallbackResult {
        private boolean success;
        private String orderNo;
        private String paymentId;
        private String message;
    }
}
