package com.promptstore.service;

import com.promptstore.dto.response.PromptResponse;
import com.promptstore.entity.UserFavorite;
import com.promptstore.repository.PromptRepository;
import com.promptstore.repository.UserFavoriteRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 收藏服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FavoriteService {

    private final UserFavoriteRepository userFavoriteRepository;
    private final PromptRepository promptRepository;

    /**
     * 添加收藏
     */
    @Transactional
    public void addFavorite(Long userId, Long promptId) {
        // 检查提示词是否存在
        if (!promptRepository.existsById(promptId)) {
            throw new RuntimeException("提示词不存在");
        }

        // 检查是否已经收藏
        if (userFavoriteRepository.existsByUserIdAndPromptId(userId, promptId)) {
            throw new RuntimeException("已经收藏过该提示词");
        }

        // 创建收藏记录
        UserFavorite favorite = UserFavorite.builder()
            .userId(userId)
            .promptId(promptId)
            .build();

        userFavoriteRepository.save(favorite);

        log.info("添加收藏成功: userId={}, promptId={}", userId, promptId);
    }

    /**
     * 取消收藏
     */
    @Transactional
    public void removeFavorite(Long userId, Long promptId) {
        if (!userFavoriteRepository.existsByUserIdAndPromptId(userId, promptId)) {
            throw new RuntimeException("未收藏该提示词");
        }

        userFavoriteRepository.deleteByUserIdAndPromptId(userId, promptId);

        log.info("取消收藏成功: userId={}, promptId={}", userId, promptId);
    }

    /**
     * 切换收藏状态
     */
    @Transactional
    public boolean toggleFavorite(Long userId, Long promptId) {
        if (userFavoriteRepository.existsByUserIdAndPromptId(userId, promptId)) {
            removeFavorite(userId, promptId);
            return false; // 取消收藏
        } else {
            addFavorite(userId, promptId);
            return true; // 添加收藏
        }
    }

    /**
     * 检查是否已收藏
     */
    public boolean isFavorited(Long userId, Long promptId) {
        return userFavoriteRepository.existsByUserIdAndPromptId(userId, promptId);
    }

    /**
     * 获取用户收藏列表
     */
    public Page<PromptResponse> getUserFavorites(Long userId, Pageable pageable) {
        Page<UserFavorite> favorites = userFavoriteRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        
        return favorites.map(favorite -> {
            PromptResponse response = PromptResponse.from(favorite.getPrompt());
            response.setIsFavorited(true); // 标记为已收藏
            return response;
        });
    }

    /**
     * 获取用户收藏的提示词ID列表
     */
    public List<Long> getUserFavoritePromptIds(Long userId) {
        return userFavoriteRepository.findPromptIdsByUserId(userId);
    }

    /**
     * 统计用户收藏数量
     */
    public long getUserFavoriteCount(Long userId) {
        return userFavoriteRepository.countByUserId(userId);
    }

    /**
     * 统计提示词被收藏数量
     */
    public long getPromptFavoriteCount(Long promptId) {
        return userFavoriteRepository.countByPromptId(promptId);
    }
}
