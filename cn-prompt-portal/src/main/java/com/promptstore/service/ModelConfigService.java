package com.promptstore.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模型配置服务
 * 管理AI模型及其版本的配置信息
 */
@Service
@Slf4j
public class ModelConfigService {

    /**
     * 模型版本配置
     */
    private static final Map<String, List<ModelVersion>> MODEL_VERSIONS = new LinkedHashMap<>();

    static {
        // ChatGPT系列
        MODEL_VERSIONS.put("ChatGPT", Arrays.asList(
            new ModelVersion("gpt-3.5-turbo", "GPT-3.5 Turbo", "快速响应，成本较低"),
            new ModelVersion("gpt-3.5-turbo-16k", "GPT-3.5 Turbo 16K", "支持更长上下文"),
            new ModelVersion("gpt-3.5-turbo-1106", "GPT-3.5 Turbo (Nov 2023)", "最新优化版本")
        ));

        // GPT-4系列
        MODEL_VERSIONS.put("GPT-4", Arrays.asList(
            new ModelVersion("gpt-4", "GPT-4", "最强推理能力"),
            new ModelVersion("gpt-4-32k", "GPT-4 32K", "超长上下文支持"),
            new ModelVersion("gpt-4-1106-preview", "GPT-4 Turbo", "最新GPT-4版本"),
            new ModelVersion("gpt-4-vision-preview", "GPT-4 Vision", "支持图像理解")
        ));

        // Claude系列
        MODEL_VERSIONS.put("Claude", Arrays.asList(
            new ModelVersion("claude-3-haiku", "Claude 3 Haiku", "快速轻量版本"),
            new ModelVersion("claude-3-sonnet", "Claude 3 Sonnet", "平衡性能版本"),
            new ModelVersion("claude-3-opus", "Claude 3 Opus", "最强性能版本"),
            new ModelVersion("claude-2.1", "Claude 2.1", "经典稳定版本")
        ));

        // Gemini系列
        MODEL_VERSIONS.put("Gemini", Arrays.asList(
            new ModelVersion("gemini-pro", "Gemini Pro", "多模态能力"),
            new ModelVersion("gemini-pro-vision", "Gemini Pro Vision", "图像理解能力"),
            new ModelVersion("gemini-ultra", "Gemini Ultra", "最强版本（限量）")
        ));

        // 通义千问系列
        MODEL_VERSIONS.put("Qwen", Arrays.asList(
            new ModelVersion("qwen-turbo", "通义千问-Turbo", "快速响应版本"),
            new ModelVersion("qwen-plus", "通义千问-Plus", "增强版本"),
            new ModelVersion("qwen-max", "通义千问-Max", "最强版本"),
            new ModelVersion("qwen-max-longcontext", "通义千问-Max-LC", "长文本版本")
        ));

        // 文心一言系列
        MODEL_VERSIONS.put("ERNIE Bot", Arrays.asList(
            new ModelVersion("ernie-bot", "文心一言", "基础版本"),
            new ModelVersion("ernie-bot-turbo", "文心一言-Turbo", "快速版本"),
            new ModelVersion("ernie-bot-4", "文心一言4.0", "最新版本"),
            new ModelVersion("ernie-bot-8k", "文心一言-8K", "长文本版本")
        ));

        // 讯飞星火系列
        MODEL_VERSIONS.put("Spark", Arrays.asList(
            new ModelVersion("spark-lite", "星火Lite", "轻量版本"),
            new ModelVersion("spark-pro", "星火Pro", "专业版本"),
            new ModelVersion("spark-3.5", "星火3.5", "最新版本"),
            new ModelVersion("spark-max", "星火Max", "最强版本")
        ));

        // 零一万物系列
        MODEL_VERSIONS.put("Yi", Arrays.asList(
            new ModelVersion("yi-medium", "Yi-Medium", "中等规模版本"),
            new ModelVersion("yi-large", "Yi-Large", "大规模版本"),
            new ModelVersion("yi-large-turbo", "Yi-Large-Turbo", "快速大模型"),
            new ModelVersion("yi-vision", "Yi-Vision", "多模态版本")
        ));

        // Moonshot系列
        MODEL_VERSIONS.put("Moonshot", Arrays.asList(
            new ModelVersion("moonshot-v1-8k", "Moonshot-v1-8K", "8K上下文版本"),
            new ModelVersion("moonshot-v1-32k", "Moonshot-v1-32K", "32K上下文版本"),
            new ModelVersion("moonshot-v1-128k", "Moonshot-v1-128K", "128K长文本版本")
        ));

        // DeepSeek系列
        MODEL_VERSIONS.put("DeepSeek", Arrays.asList(
            new ModelVersion("deepseek-chat", "DeepSeek Chat", "对话版本"),
            new ModelVersion("deepseek-coder", "DeepSeek Coder", "代码专用版本"),
            new ModelVersion("deepseek-math", "DeepSeek Math", "数学专用版本")
        ));
    }

    /**
     * 获取所有模型列表
     */
    public List<String> getAllModels() {
        return new ArrayList<>(MODEL_VERSIONS.keySet());
    }

    /**
     * 获取指定模型的版本列表
     */
    public List<ModelVersion> getModelVersions(String model) {
        return MODEL_VERSIONS.getOrDefault(model, Collections.emptyList());
    }

    /**
     * 获取所有模型及其版本的完整配置
     */
    public Map<String, List<ModelVersion>> getAllModelVersions() {
        return new HashMap<>(MODEL_VERSIONS);
    }

    /**
     * 验证模型和版本的有效性
     */
    public boolean isValidModelVersion(String model, String version) {
        List<ModelVersion> versions = MODEL_VERSIONS.get(model);
        if (versions == null) {
            return false;
        }
        return versions.stream().anyMatch(v -> v.getValue().equals(version));
    }

    /**
     * 获取模型的默认版本
     */
    public String getDefaultVersion(String model) {
        List<ModelVersion> versions = MODEL_VERSIONS.get(model);
        if (versions == null || versions.isEmpty()) {
            return null;
        }
        return versions.get(0).getValue(); // 返回第一个版本作为默认版本
    }

    /**
     * 模型版本信息类
     */
    public static class ModelVersion {
        private String value;      // 版本值（用于存储）
        private String label;      // 显示名称
        private String description; // 版本描述

        public ModelVersion(String value, String label, String description) {
            this.value = value;
            this.label = label;
            this.description = description;
        }

        // Getters
        public String getValue() { return value; }
        public String getLabel() { return label; }
        public String getDescription() { return description; }

        // Setters
        public void setValue(String value) { this.value = value; }
        public void setLabel(String label) { this.label = label; }
        public void setDescription(String description) { this.description = description; }
    }
}
