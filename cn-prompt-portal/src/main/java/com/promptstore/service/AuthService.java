package com.promptstore.service;

import com.promptstore.dto.WechatUserInfo;
import com.promptstore.dto.request.EmailLoginRequest;
import com.promptstore.dto.request.EmailRegisterRequest;
import com.promptstore.dto.response.LoginResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.entity.User;
import com.promptstore.enums.UserRole;
import com.promptstore.enums.UserStatus;
import com.promptstore.enums.VerificationType;
import com.promptstore.exception.BusinessException;
import com.promptstore.exception.UnauthorizedException;
import com.promptstore.repository.UserRepository;
import com.promptstore.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 认证服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final WechatService wechatService;
    private final JwtTokenProvider tokenProvider;
    private final RedisTemplate<String, Object> redisTemplate;
    private final EmailVerificationService emailVerificationService;

    /**
     * 微信登录
     */
    public LoginResponse wechatLogin(String code) {
        // 1. 通过code获取微信用户信息
        WechatUserInfo wechatUserInfo = wechatService.getUserInfo(code);
        
        // 2. 查找或创建用户
        User user = userRepository.findByWechatOpenId(wechatUserInfo.getOpenId())
            .orElseGet(() -> createUserFromWechat(wechatUserInfo));
            
        // 3. 检查用户状态
        checkUserStatus(user);

        // 4. 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);

        // 5. 生成JWT Token
        return generateLoginResponse(user);
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String refreshToken) {
        // 1. 验证刷新令牌
        if (!tokenProvider.validateToken(refreshToken)) {
            throw new UnauthorizedException("刷新令牌无效");
        }
        
        // 2. 检查令牌类型
        String tokenType = tokenProvider.getTokenTypeFromToken(refreshToken);
        if (!"refresh".equals(tokenType)) {
            throw new UnauthorizedException("令牌类型错误");
        }
        
        // 3. 获取用户ID
        Long userId = tokenProvider.getUserIdFromToken(refreshToken);
        
        // 4. 检查Redis中的刷新令牌
        String storedToken = (String) redisTemplate.opsForValue().get("refresh_token:" + userId);
        if (!refreshToken.equals(storedToken)) {
            throw new UnauthorizedException("刷新令牌已失效");
        }
        
        // 5. 获取用户信息
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new UnauthorizedException("用户不存在"));
            
        // 6. 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new UnauthorizedException("用户状态异常");
        }
        
        // 7. 生成新的访问令牌
        String newAccessToken = tokenProvider.generateAccessToken(user);
        
        // 8. 构建响应
        UserResponse userResponse = buildUserResponse(user);
        
        return LoginResponse.builder()
            .accessToken(newAccessToken)
            .refreshToken(refreshToken) // 刷新令牌保持不变
            .expiresIn(tokenProvider.getAccessTokenExpiration())
            .user(userResponse)
            .build();
    }

    /**
     * 邮箱注册
     */
    public LoginResponse emailRegister(EmailRegisterRequest request) {
        // 1. 验证验证码
        if (!emailVerificationService.verifyCode(request.getEmail(), request.getCode(), VerificationType.REGISTER)) {
            throw new BusinessException("验证码无效或已过期");
        }

        // 2. 检查邮箱是否已注册
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("该邮箱已被注册");
        }

        // 3. 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 4. 创建用户
        User user = User.builder()
            .username(request.getUsername())
            .email(request.getEmail())
            .bio(request.getBio())
            .role(UserRole.USER)
            .status(UserStatus.ACTIVE)
            .emailVerified(true) // 通过验证码注册，邮箱已验证
            .build();

        User savedUser = userRepository.save(user);
        log.info("邮箱注册成功: username={}, email={}", request.getUsername(), request.getEmail());

        // 5. 生成JWT Token
        return generateLoginResponse(savedUser);
    }

    /**
     * 邮箱登录
     */
    public LoginResponse emailLogin(EmailLoginRequest request) {
        // 1. 验证验证码
        if (!emailVerificationService.verifyCode(request.getEmail(), request.getCode(), VerificationType.LOGIN)) {
            throw new BusinessException("验证码无效或已过期");
        }

        // 2. 查找用户
        User user = userRepository.findByEmail(request.getEmail())
            .orElseThrow(() -> new BusinessException("该邮箱尚未注册，请先注册"));

        // 3. 检查用户状态
        checkUserStatus(user);

        // 4. 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);

        log.info("邮箱登录成功: email={}, username={}", request.getEmail(), user.getUsername());

        // 5. 生成JWT Token
        return generateLoginResponse(user);
    }

    /**
     * 登出
     */
    public void logout(Long userId) {
        // 删除Redis中的刷新Token
        redisTemplate.delete("refresh_token:" + userId);
        log.info("用户 {} 已登出", userId);
    }

    /**
     * 从微信用户信息创建用户
     */
    private User createUserFromWechat(WechatUserInfo wechatUserInfo) {
        // 生成唯一用户名
        String username = generateUniqueUsername(wechatUserInfo.getNickname());
        
        User user = User.builder()
            .username(username)
            .wechatOpenId(wechatUserInfo.getOpenId())
            .wechatUnionId(wechatUserInfo.getUnionId())
            .avatarUrl(wechatUserInfo.getAvatarUrl())
            .role(UserRole.USER)
            .status(UserStatus.ACTIVE)
            .build();
            
        User savedUser = userRepository.save(user);
        log.info("创建新用户: {}, 微信OpenID: {}", username, wechatUserInfo.getOpenId());
        
        return savedUser;
    }

    /**
     * 生成唯一用户名
     */
    private String generateUniqueUsername(String nickname) {
        // 清理昵称，只保留字母、数字和中文
        String baseUsername = nickname.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
        
        // 限制长度
        if (baseUsername.length() > 20) {
            baseUsername = baseUsername.substring(0, 20);
        }
        
        // 如果清理后为空，使用默认前缀
        if (baseUsername.isEmpty()) {
            baseUsername = "用户";
        }
        
        // 检查唯一性，如果重复则添加数字后缀
        String username = baseUsername;
        int suffix = 1;
        while (userRepository.existsByUsername(username)) {
            username = baseUsername + suffix++;
        }
        
        return username;
    }

    /**
     * 检查用户状态
     */
    private void checkUserStatus(User user) {
        if (user.getStatus() == UserStatus.BANNED) {
            throw new UnauthorizedException("账户已被封禁，请联系客服");
        }

        if (user.getStatus() == UserStatus.INACTIVE) {
            throw new UnauthorizedException("账户已被停用，请联系客服");
        }
    }

    /**
     * 生成登录响应
     */
    private LoginResponse generateLoginResponse(User user) {
        // 1. 生成JWT Token
        String accessToken = tokenProvider.generateAccessToken(user);
        String refreshToken = tokenProvider.generateRefreshToken(user);

        // 2. 存储刷新Token到Redis
        redisTemplate.opsForValue().set(
            "refresh_token:" + user.getId(),
            refreshToken,
            Duration.ofSeconds(tokenProvider.getRefreshTokenExpiration())
        );

        // 3. 构建响应
        UserResponse userResponse = buildUserResponse(user);

        return LoginResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .expiresIn(tokenProvider.getAccessTokenExpiration())
            .user(userResponse)
            .build();
    }

    /**
     * 构建用户响应对象
     */
    private UserResponse buildUserResponse(User user) {
        return UserResponse.builder()
            .id(user.getId())
            .username(user.getUsername())
            .email(user.getEmail())
            .phone(user.getPhone())
            .avatarUrl(user.getAvatarUrl())
            .bio(user.getBio())
            .role(user.getRole())
            .status(user.getStatus())
            .emailVerified(user.getEmailVerified())
            .phoneVerified(user.getPhoneVerified())
            .totalEarnings(user.getTotalEarnings())
            .totalSpent(user.getTotalSpent())
            .followerCount(user.getFollowerCount())
            .followingCount(user.getFollowingCount())
            .createdAt(user.getCreatedAt())
            .updatedAt(user.getUpdatedAt())
            .lastLoginAt(user.getLastLoginAt())
            .build();
    }
}
