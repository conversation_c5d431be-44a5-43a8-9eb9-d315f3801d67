package com.promptstore.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.promptstore.dto.request.PromptCreateRequest;
import com.promptstore.dto.request.PromptSearchRequest;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.entity.Category;
import com.promptstore.entity.Prompt;
import com.promptstore.entity.User;
import com.promptstore.enums.AuditStatus;
import com.promptstore.enums.PromptStatus;
import com.promptstore.exception.BusinessException;
import com.promptstore.exception.ResourceNotFoundException;
import com.promptstore.repository.CategoryRepository;
import com.promptstore.repository.PromptRepository;
import com.promptstore.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提示词服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PromptService {

    private final PromptRepository promptRepository;
    private final UserRepository userRepository;
    private final CategoryRepository categoryRepository;
    private final ObjectMapper objectMapper;

    /**
     * 搜索提示词
     */
    public PageResponse<PromptResponse> searchPrompts(PromptSearchRequest request, Long currentUserId) {
        // 构建查询条件
        Specification<Prompt> spec = buildSearchSpecification(request);
        
        // 构建排序
        Sort sort = buildSort(request.getSortBy());
        
        // 分页查询
        Pageable pageable = PageRequest.of(
            request.getPage() - 1, 
            request.getSize(), 
            sort
        );
        
        Page<Prompt> promptPage = promptRepository.findAll(spec, pageable);
        
        // 转换为响应DTO
        List<PromptResponse> prompts = promptPage.getContent().stream()
            .map(prompt -> convertToResponse(prompt, currentUserId, false))
            .collect(Collectors.toList());
            
        return PageResponse.of(
            prompts,
            promptPage.getTotalElements(),
            request.getPage(),
            request.getSize()
        );
    }

    /**
     * 根据ID获取提示词详情
     */
    public PromptResponse getPromptById(Long id, Long currentUserId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));
            
        // 增加浏览次数
        promptRepository.incrementViewCount(id);
        
        return convertToResponse(prompt, currentUserId, true);
    }

    /**
     * 创建提示词
     */
    public PromptResponse createPrompt(PromptCreateRequest request, Long userId) {
        // 验证用户是否存在
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
            
        // 验证分类是否存在
        Category category = categoryRepository.findById(request.getCategoryId())
            .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));

        // 验证价格逻辑
        if (!request.getIsFree() && (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BusinessException("付费提示词的价格必须大于0");
        }

        // 如果是免费提示词，将价格设置为0
        BigDecimal finalPrice = request.getIsFree() ? BigDecimal.ZERO : request.getPrice();

        // 创建提示词实体
        Prompt prompt = Prompt.builder()
            .userId(userId)
            .title(request.getTitle())
            .description(request.getDescription())
            .content(request.getContent())
            .aiModel(request.getAiModel())
            .modelVersion(request.getModelVersion())
            .categoryId(request.getCategoryId())
            .price(finalPrice)
            .originalPrice(request.getOriginalPrice())
            .previewImages(convertListToJson(request.getPreviewImages()))
            .exampleOutputs(convertListToJson(request.getExampleOutputs()))
            .usageInstructions(request.getUsageInstructions())
            .tags(convertListToJson(request.getTags()))
            .isFree(request.getIsFree())
            .imageUrl(request.getImageUrl())
            .status(PromptStatus.DRAFT)
            .build();
            
        Prompt savedPrompt = promptRepository.save(prompt);
        
        log.info("用户 {} 创建了提示词: {}", userId, savedPrompt.getId());
        
        return convertToResponse(savedPrompt, userId, true);
    }

    /**
     * 更新提示词
     */
    public PromptResponse updatePrompt(Long id, PromptCreateRequest request, Long userId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));
            
        // 检查权限
        if (!prompt.getUserId().equals(userId)) {
            throw new BusinessException("无权修改此提示词");
        }
        
        // 已通过审核的提示词修改后需要重新审核
        boolean needReaudit = (prompt.getAuditStatus() == AuditStatus.APPROVED);
        
        // 验证分类是否存在
        if (!prompt.getCategoryId().equals(request.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));
        }

        // 验证价格逻辑
        if (!request.getIsFree() && (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BusinessException("付费提示词的价格必须大于0");
        }

        // 如果是免费提示词，将价格设置为0
        BigDecimal finalPrice = request.getIsFree() ? BigDecimal.ZERO : request.getPrice();

        // 更新字段
        prompt.setTitle(request.getTitle());
        prompt.setDescription(request.getDescription());
        prompt.setContent(request.getContent());
        prompt.setAiModel(request.getAiModel());
        prompt.setModelVersion(request.getModelVersion());
        prompt.setCategoryId(request.getCategoryId());
        prompt.setPrice(finalPrice);
        prompt.setOriginalPrice(request.getOriginalPrice());
        prompt.setPreviewImages(convertListToJson(request.getPreviewImages()));
        prompt.setExampleOutputs(convertListToJson(request.getExampleOutputs()));
        prompt.setUsageInstructions(request.getUsageInstructions());
        prompt.setTags(convertListToJson(request.getTags()));
        prompt.setIsFree(request.getIsFree());
        prompt.setImageUrl(request.getImageUrl());

        // 如果需要重新审核，重置审核状态
        if (needReaudit) {
            prompt.setAuditStatus(AuditStatus.PENDING);
            prompt.setAuditReason(null);
            prompt.setAuditorId(null);
            prompt.setAuditTime(null);
        }

        Prompt savedPrompt = promptRepository.save(prompt);

        String logMessage = needReaudit ? "用户 {} 更新了提示词，重新进入审核: {}" : "用户 {} 更新了提示词: {}";
        log.info(logMessage, userId, id);

        return convertToResponse(savedPrompt, userId, true);
    }

    /**
     * 获取提示词编辑信息
     */
    public PromptResponse getPromptForEdit(Long id, Long userId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));

        // 检查权限
        if (!prompt.getUserId().equals(userId)) {
            throw new BusinessException("无权编辑此提示词");
        }

        return convertToResponse(prompt, userId, true);
    }

    /**
     * 删除提示词
     */
    public void deletePrompt(Long id, Long userId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));
            
        // 检查权限
        if (!prompt.getUserId().equals(userId)) {
            throw new BusinessException("无权删除此提示词");
        }
        
        prompt.setStatus(PromptStatus.DELETED);
        promptRepository.save(prompt);
        
        log.info("用户 {} 删除了提示词: {}", userId, id);
    }

    /**
     * 发布提示词
     */
    public void publishPrompt(Long id, Long userId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));
            
        // 检查权限
        if (!prompt.getUserId().equals(userId)) {
            throw new BusinessException("无权发布此提示词");
        }
        
        // 只有草稿状态的提示词可以发布
        if (prompt.getStatus() != PromptStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的提示词可以发布");
        }
        
        prompt.setStatus(PromptStatus.PENDING);
        promptRepository.save(prompt);
        
        log.info("用户 {} 发布了提示词: {}", userId, id);
    }

    /**
     * 获取用户的提示词列表
     */
    public PageResponse<PromptResponse> getUserPrompts(Long userId, PromptStatus status, 
                                                      Integer page, Integer size, Long currentUserId) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
        
        Page<Prompt> promptPage;
        if (status != null) {
            promptPage = promptRepository.findByUserIdAndStatus(userId, status, pageable);
        } else {
            promptPage = promptRepository.findByUserId(userId, pageable);
        }
        
        List<PromptResponse> prompts = promptPage.getContent().stream()
            .map(prompt -> convertToResponse(prompt, currentUserId, false))
            .collect(Collectors.toList());
            
        return PageResponse.of(
            prompts,
            promptPage.getTotalElements(),
            page,
            size
        );
    }

    /**
     * 获取热门提示词
     */
    public List<PromptResponse> getHotPrompts(int limit, Long currentUserId) {
        Pageable pageable = PageRequest.of(0, limit);
        Page<Prompt> promptPage = promptRepository.findHotPrompts(pageable);
        
        return promptPage.getContent().stream()
            .map(prompt -> convertToResponse(prompt, currentUserId, false))
            .collect(Collectors.toList());
    }

    /**
     * 获取最新提示词
     */
    public List<PromptResponse> getLatestPrompts(int limit, Long currentUserId) {
        Pageable pageable = PageRequest.of(0, limit);
        Page<Prompt> promptPage = promptRepository.findLatestPrompts(pageable);
        
        return promptPage.getContent().stream()
            .map(prompt -> convertToResponse(prompt, currentUserId, false))
            .collect(Collectors.toList());
    }

    /**
     * 构建搜索条件
     */
    private Specification<Prompt> buildSearchSpecification(PromptSearchRequest request) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 只查询已审核通过的提示词
            predicates.add(cb.equal(root.get("status"), PromptStatus.APPROVED));
            
            // 关键词搜索
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword().toLowerCase() + "%";
                predicates.add(cb.or(
                    cb.like(cb.lower(root.get("title")), keyword),
                    cb.like(cb.lower(root.get("description")), keyword)
                ));
            }
            
            // 分类过滤
            if (request.getCategoryId() != null) {
                predicates.add(cb.equal(root.get("categoryId"), request.getCategoryId()));
            }
            
            // AI模型过滤
            if (StringUtils.hasText(request.getAiModel())) {
                predicates.add(cb.equal(root.get("aiModel"), request.getAiModel()));
            }
            
            // 价格范围过滤
            if (request.getMinPrice() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("price"), request.getMinPrice()));
            }
            if (request.getMaxPrice() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("price"), request.getMaxPrice()));
            }
            
            // 免费过滤
            if (request.getIsFree() != null) {
                predicates.add(cb.equal(root.get("isFree"), request.getIsFree()));
            }
            
            // 精选过滤
            if (request.getIsFeatured() != null) {
                predicates.add(cb.equal(root.get("isFeatured"), request.getIsFeatured()));
            }
            
            // 创作者过滤
            if (request.getCreatorId() != null) {
                predicates.add(cb.equal(root.get("userId"), request.getCreatorId()));
            }
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建排序
     */
    private Sort buildSort(String sortBy) {
        return switch (sortBy) {
            case "price_asc" -> Sort.by(Sort.Direction.ASC, "price");
            case "price_desc" -> Sort.by(Sort.Direction.DESC, "price");
            case "rating" -> Sort.by(Sort.Direction.DESC, "ratingAvg");
            case "popular" -> Sort.by(Sort.Direction.DESC, "downloadCount");
            case "newest" -> Sort.by(Sort.Direction.DESC, "createdAt");
            default -> Sort.by(Sort.Direction.DESC, "createdAt");
        };
    }

    /**
     * 转换为响应DTO
     */
    private PromptResponse convertToResponse(Prompt prompt, Long currentUserId, boolean includeContent) {
        PromptResponse response = PromptResponse.from(prompt);
        
        // 设置分类名称
        if (prompt.getCategoryId() != null) {
            categoryRepository.findById(prompt.getCategoryId())
                .ifPresent(category -> response.setCategoryName(category.getName()));
        }
        
        // 设置创作者信息
        if (prompt.getUser() != null) {
            response.setCreator(UserResponse.publicInfo(
                prompt.getUser().getId(),
                prompt.getUser().getUsername(),
                prompt.getUser().getAvatarUrl(),
                prompt.getUser().getBio(),
                prompt.getUser().getRole(),
                prompt.getUser().getFollowerCount(),
                prompt.getUser().getFollowingCount(),
                prompt.getUser().getCreatedAt()
            ));
        }
        
        // 解析JSON字段
        response.setPreviewImages(convertJsonToList(prompt.getPreviewImages()));
        response.setExampleOutputs(convertJsonToList(prompt.getExampleOutputs()));
        response.setTags(convertJsonToList(prompt.getTags()));
        
        // 如果不包含内容，则清空敏感信息
        if (!includeContent) {
            response.setContent(null);
            response.setUsageInstructions(null);
        }
        
        // TODO: 设置是否已购买和是否已收藏
        response.setIsPurchased(false);
        response.setIsLiked(false);
        
        return response;
    }

    /**
     * 将列表转换为JSON字符串
     */
    private String convertListToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.error("转换列表为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为列表
     */
    private List<String> convertJsonToList(String json) {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, List.class);
        } catch (JsonProcessingException e) {
            log.error("转换JSON为列表失败", e);
            return new ArrayList<>();
        }
    }
}
