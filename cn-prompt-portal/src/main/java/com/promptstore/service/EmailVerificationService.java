package com.promptstore.service;

import com.promptstore.entity.EmailVerification;
import com.promptstore.enums.VerificationType;
import com.promptstore.exception.BusinessException;
import com.promptstore.repository.EmailVerificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 邮箱验证服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class EmailVerificationService {

    private final EmailVerificationRepository verificationRepository;
    private final AliyunMailService mailService;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${app.aliyun.mail.verification.expire-minutes:5}")
    private int expireMinutes;

    @Value("${app.aliyun.mail.verification.max-attempts:3}")
    private int maxAttempts;

    @Value("${app.aliyun.mail.verification.resend-interval-seconds:60}")
    private int resendIntervalSeconds;

    private static final Random RANDOM = new Random();

    /**
     * 发送验证码
     */
    public void sendVerificationCode(String email, VerificationType type) {
        // 1. 检查发送频率限制
        checkSendFrequency(email, type);

        // 2. 生成验证码
        String code = generateVerificationCode();

        // 3. 保存验证码到数据库
        EmailVerification verification = EmailVerification.builder()
            .email(email)
            .code(code)
            .type(type)
            .expiresAt(LocalDateTime.now().plusMinutes(expireMinutes))
            .build();

        verificationRepository.save(verification);

        // 4. 发送邮件
        try {
//            mailService.sendVerificationCode(email, code, type.name());
            log.info("验证码发送成功: email={}, type={},code={}", email, type,code);
        } catch (Exception e) {
            log.error("验证码发送失败: email={}, type={}", email, type, e);
            throw new BusinessException("验证码发送失败，请稍后重试");
        }

        // 5. 设置Redis限制
        setRateLimitInRedis(email, type);
    }

    /**
     * 验证验证码
     */
    public boolean verifyCode(String email, String code, VerificationType type) {
        // 1. 查找验证码记录
        EmailVerification verification = verificationRepository
            .findByEmailAndCodeAndType(email, code, type)
            .orElse(null);

        if (verification == null) {
            log.warn("验证码不存在: email={}, code={}, type={}", email, code, type);
            return false;
        }

        // 2. 检查是否已使用
        if (verification.getIsUsed()) {
            log.warn("验证码已使用: email={}, code={}, type={}", email, code, type);
            return false;
        }

        // 3. 检查是否过期
        if (verification.isExpired()) {
            log.warn("验证码已过期: email={}, code={}, type={}", email, code, type);
            return false;
        }

        // 4. 检查尝试次数
        if (verification.getAttempts() >= maxAttempts) {
            log.warn("验证码尝试次数超限: email={}, code={}, type={}, attempts={}", 
                email, code, type, verification.getAttempts());
            return false;
        }

        // 5. 标记为已使用
        verificationRepository.markAsUsed(verification.getId());

        log.info("验证码验证成功: email={}, type={}", email, type);
        return true;
    }

    /**
     * 验证验证码（带尝试次数增加）
     */
    public boolean verifyCodeWithAttempt(String email, String code, VerificationType type) {
        // 1. 查找验证码记录
        EmailVerification verification = verificationRepository
            .findTopByEmailAndTypeOrderByCreatedAtDesc(email, type)
            .orElse(null);

        if (verification == null) {
            log.warn("验证码记录不存在: email={}, type={}", email, type);
            return false;
        }

        // 2. 增加尝试次数
        verificationRepository.incrementAttempts(verification.getId());

        // 3. 检查验证码是否匹配
        if (!code.equals(verification.getCode())) {
            log.warn("验证码不匹配: email={}, inputCode={}, actualCode={}, type={}", 
                email, code, verification.getCode(), type);
            return false;
        }

        // 4. 检查其他条件
        return verifyCode(email, code, type);
    }

    /**
     * 检查发送频率限制
     */
    private void checkSendFrequency(String email, VerificationType type) {
        String key = String.format("email_verification_limit:%s:%s", email, type.name());
        
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            throw new BusinessException("发送过于频繁，请稍后再试");
        }

        // 检查数据库中的发送记录
        LocalDateTime since = LocalDateTime.now().minusSeconds(resendIntervalSeconds);
        List<EmailVerification> recentVerifications = verificationRepository
            .findByEmailAndTypeAndCreatedAtAfter(email, type, since);

        if (!recentVerifications.isEmpty()) {
            throw new BusinessException("发送过于频繁，请" + resendIntervalSeconds + "秒后再试");
        }
    }

    /**
     * 在Redis中设置频率限制
     */
    private void setRateLimitInRedis(String email, VerificationType type) {
        String key = String.format("email_verification_limit:%s:%s", email, type.name());
        redisTemplate.opsForValue().set(key, "1", Duration.ofSeconds(resendIntervalSeconds));
    }

    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        return String.format("%06d", RANDOM.nextInt(1000000));
    }

    /**
     * 清理过期的验证码
     */
    @Transactional
    public void cleanupExpiredVerifications() {
        int deletedCount = verificationRepository.deleteExpiredVerifications(LocalDateTime.now());
        if (deletedCount > 0) {
            log.info("清理过期验证码: {} 条", deletedCount);
        }
    }

    /**
     * 检查邮箱是否有有效的验证码
     */
    public boolean hasValidVerification(String email, VerificationType type) {
        EmailVerification verification = verificationRepository
            .findTopByEmailAndTypeOrderByCreatedAtDesc(email, type)
            .orElse(null);

        return verification != null && verification.isValid();
    }
}
