package com.promptstore.service;

import com.promptstore.dto.request.UserUpdateRequest;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.entity.User;
import com.promptstore.enums.UserRole;
import com.promptstore.enums.UserStatus;
import com.promptstore.exception.BusinessException;
import com.promptstore.exception.ResourceNotFoundException;
import com.promptstore.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;

    /**
     * 根据ID获取用户信息
     */
    @Transactional(readOnly = true)
    public UserResponse getUserById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
                
        return convertToResponse(user);
    }

    /**
     * 获取用户公开信息
     */
    @Transactional(readOnly = true)
    public UserResponse getUserPublicInfo(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
                
        return UserResponse.publicInfo(
            user.getId(),
            user.getUsername(),
            user.getAvatarUrl(),
            user.getBio(),
            user.getRole(),
            user.getFollowerCount(),
            user.getFollowingCount(),
            user.getCreatedAt()
        );
    }

    /**
     * 更新用户信息
     */
    public UserResponse updateUser(Long id, UserUpdateRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 检查用户名是否已存在
        if (StringUtils.hasText(request.getUsername()) && 
            !request.getUsername().equals(user.getUsername())) {
            if (userRepository.existsByUsername(request.getUsername())) {
                throw new BusinessException("用户名已存在");
            }
            user.setUsername(request.getUsername());
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) && 
            !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new BusinessException("邮箱已存在");
            }
            user.setEmail(request.getEmail());
        }

        // 更新其他字段
        if (StringUtils.hasText(request.getPhone())) {
            user.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getAvatarUrl())) {
            user.setAvatarUrl(request.getAvatarUrl());
        }
        if (StringUtils.hasText(request.getBio())) {
            user.setBio(request.getBio());
        }

        User savedUser = userRepository.save(user);
        
        log.info("用户信息已更新: {}", id);
        
        return convertToResponse(savedUser);
    }

    /**
     * 申请成为创作者
     */
    public void applyForCreator(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        if (user.getRole() != UserRole.USER) {
            throw new BusinessException("只有普通用户可以申请成为创作者");
        }

        // 这里可以添加审核流程，暂时直接升级为创作者
        user.setRole(UserRole.CREATOR);
        userRepository.save(user);
        
        log.info("用户 {} 已成为创作者", userId);
    }

    /**
     * 获取创作者排行榜
     */
    @Transactional(readOnly = true)
    public List<UserResponse> getTopCreators(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        Page<User> userPage = userRepository.findTopCreators(pageable);
        
        return userPage.getContent().stream()
                .map(this::convertToPublicResponse)
                .collect(Collectors.toList());
    }

    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public PageResponse<UserResponse> searchUsers(String username, UserRole role, 
                                                 Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
        
        Page<User> userPage;
        if (StringUtils.hasText(username) && role != null) {
            userPage = userRepository.findByUsernameContainingIgnoreCaseAndRole(username, role, pageable);
        } else if (StringUtils.hasText(username)) {
            userPage = userRepository.findByUsernameContainingIgnoreCase(username, pageable);
        } else if (role != null) {
            userPage = userRepository.findByRole(role, pageable);
        } else {
            userPage = userRepository.findAll(pageable);
        }
        
        List<UserResponse> users = userPage.getContent().stream()
                .map(this::convertToPublicResponse)
                .collect(Collectors.toList());
                
        return PageResponse.of(
            users,
            userPage.getTotalElements(),
            page,
            size
        );
    }

    /**
     * 获取用户统计信息
     */
    @Transactional(readOnly = true)
    public UserStatsResponse getUserStats(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // TODO: 实现统计逻辑
        return UserStatsResponse.builder()
                .totalPrompts(0L)
                .totalSales(0L)
                .totalEarnings(user.getTotalEarnings())
                .totalSpent(user.getTotalSpent())
                .followerCount(user.getFollowerCount())
                .followingCount(user.getFollowingCount())
                .build();
    }

    /**
     * 转换为响应DTO
     */
    private UserResponse convertToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatarUrl(user.getAvatarUrl())
                .bio(user.getBio())
                .role(user.getRole())
                .status(user.getStatus())
                .emailVerified(user.getEmailVerified())
                .phoneVerified(user.getPhoneVerified())
                .totalEarnings(user.getTotalEarnings())
                .totalSpent(user.getTotalSpent())
                .followerCount(user.getFollowerCount())
                .followingCount(user.getFollowingCount())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();
    }

    /**
     * 转换为公开信息响应DTO
     */
    private UserResponse convertToPublicResponse(User user) {
        return UserResponse.publicInfo(
            user.getId(),
            user.getUsername(),
            user.getAvatarUrl(),
            user.getBio(),
            user.getRole(),
            user.getFollowerCount(),
            user.getFollowingCount(),
            user.getCreatedAt()
        );
    }

    /**
     * 用户统计响应DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserStatsResponse {
        private Long totalPrompts;
        private Long totalSales;
        private java.math.BigDecimal totalEarnings;
        private java.math.BigDecimal totalSpent;
        private Integer followerCount;
        private Integer followingCount;
    }
}
