package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.service.ModelConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模型配置控制器
 * 提供AI模型及其版本的配置信息
 */
@RestController
@RequestMapping("/models")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "模型配置", description = "AI模型配置相关接口")
public class ModelConfigController {

    private final ModelConfigService modelConfigService;

    /**
     * 获取所有模型列表
     */
    @GetMapping
    @Operation(summary = "获取所有模型列表", description = "获取系统支持的所有AI模型列表")
    public ResponseEntity<ApiResponse<List<String>>> getAllModels() {
        try {
            List<String> models = modelConfigService.getAllModels();
            log.info("获取模型列表成功，共{}个模型", models.size());
            return ResponseEntity.ok(ApiResponse.success(models));
        } catch (Exception e) {
            log.error("获取模型列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取模型列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定模型的版本列表
     */
    @GetMapping("/{model}/versions")
    @Operation(summary = "获取模型版本列表", description = "获取指定模型的所有版本信息")
    public ResponseEntity<ApiResponse<List<ModelConfigService.ModelVersion>>> getModelVersions(
            @Parameter(description = "模型名称", required = true) 
            @PathVariable String model) {
        
        try {
            List<ModelConfigService.ModelVersion> versions = modelConfigService.getModelVersions(model);
            
            if (versions.isEmpty()) {
                log.warn("未找到模型版本信息: model={}", model);
                return ResponseEntity.ok(ApiResponse.error("未找到指定模型的版本信息"));
            }
            
            log.info("获取模型版本成功: model={}, versions={}", model, versions.size());
            return ResponseEntity.ok(ApiResponse.success(versions));
            
        } catch (Exception e) {
            log.error("获取模型版本失败: model={}", model, e);
            return ResponseEntity.ok(ApiResponse.error("获取模型版本失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有模型及其版本的完整配置
     */
    @GetMapping("/all")
    @Operation(summary = "获取完整模型配置", description = "获取所有模型及其版本的完整配置信息")
    public ResponseEntity<ApiResponse<Map<String, List<ModelConfigService.ModelVersion>>>> getAllModelVersions() {
        try {
            Map<String, List<ModelConfigService.ModelVersion>> allVersions = modelConfigService.getAllModelVersions();
            log.info("获取完整模型配置成功，共{}个模型", allVersions.size());
            return ResponseEntity.ok(ApiResponse.success(allVersions));
        } catch (Exception e) {
            log.error("获取完整模型配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取完整模型配置失败: " + e.getMessage()));
        }
    }

    /**
     * 验证模型和版本的有效性
     */
    @GetMapping("/validate")
    @Operation(summary = "验证模型版本", description = "验证指定的模型和版本是否有效")
    public ResponseEntity<ApiResponse<Boolean>> validateModelVersion(
            @Parameter(description = "模型名称", required = true) 
            @RequestParam String model,
            @Parameter(description = "模型版本", required = true) 
            @RequestParam String version) {
        
        try {
            boolean isValid = modelConfigService.isValidModelVersion(model, version);
            log.info("模型版本验证: model={}, version={}, valid={}", model, version, isValid);
            return ResponseEntity.ok(ApiResponse.success(isValid));
        } catch (Exception e) {
            log.error("模型版本验证失败: model={}, version={}", model, version, e);
            return ResponseEntity.ok(ApiResponse.error("模型版本验证失败: " + e.getMessage()));
        }
    }

    /**
     * 获取模型的默认版本
     */
    @GetMapping("/{model}/default-version")
    @Operation(summary = "获取默认版本", description = "获取指定模型的默认版本")
    public ResponseEntity<ApiResponse<String>> getDefaultVersion(
            @Parameter(description = "模型名称", required = true) 
            @PathVariable String model) {
        
        try {
            String defaultVersion = modelConfigService.getDefaultVersion(model);
            
            if (defaultVersion == null) {
                log.warn("未找到模型默认版本: model={}", model);
                return ResponseEntity.ok(ApiResponse.error("未找到指定模型的默认版本"));
            }
            
            log.info("获取模型默认版本成功: model={}, defaultVersion={}", model, defaultVersion);
            return ResponseEntity.ok(ApiResponse.success(defaultVersion));
            
        } catch (Exception e) {
            log.error("获取模型默认版本失败: model={}", model, e);
            return ResponseEntity.ok(ApiResponse.error("获取模型默认版本失败: " + e.getMessage()));
        }
    }
}
