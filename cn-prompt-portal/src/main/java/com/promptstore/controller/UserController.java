package com.promptstore.controller;

import com.promptstore.dto.request.UserUpdateRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.enums.UserRole;
import com.promptstore.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentUser(Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            UserResponse user = userService.getUserById(userId);
            return ResponseEntity.ok(ApiResponse.success(user));
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    @Operation(summary = "更新当前用户信息", description = "更新当前登录用户的信息")
    public ResponseEntity<ApiResponse<UserResponse>> updateCurrentUser(
            @Parameter(description = "用户信息", required = true) @Valid @RequestBody UserUpdateRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            UserResponse user = userService.updateUser(userId, request);
            return ResponseEntity.ok(ApiResponse.success(user, "更新成功"));
            
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户公开信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取用户公开信息", description = "获取指定用户的公开信息")
    public ResponseEntity<ApiResponse<UserResponse>> getUserPublicInfo(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        try {
            UserResponse user = userService.getUserPublicInfo(id);
            return ResponseEntity.ok(ApiResponse.success(user));
            
        } catch (Exception e) {
            log.error("获取用户公开信息失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 申请成为创作者
     */
    @PostMapping("/apply-creator")
    @Operation(summary = "申请成为创作者", description = "申请将账户升级为创作者")
    public ResponseEntity<ApiResponse<Void>> applyForCreator(Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            userService.applyForCreator(userId);
            return ResponseEntity.ok(ApiResponse.success("申请成功，您已成为创作者"));
            
        } catch (Exception e) {
            log.error("申请创作者失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取创作者排行榜
     */
    @GetMapping("/creators/top")
    @Operation(summary = "获取创作者排行榜", description = "获取收益最高的创作者列表")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getTopCreators(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<UserResponse> creators = userService.getTopCreators(limit);
            return ResponseEntity.ok(ApiResponse.success(creators));
            
        } catch (Exception e) {
            log.error("获取创作者排行榜失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @Operation(summary = "搜索用户", description = "根据用户名和角色搜索用户")
    public ResponseEntity<ApiResponse<PageResponse<UserResponse>>> searchUsers(
            @Parameter(description = "用户名关键词") @RequestParam(required = false) String username,
            @Parameter(description = "用户角色") @RequestParam(required = false) UserRole role,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            PageResponse<UserResponse> users = userService.searchUsers(username, role, page, size);
            return ResponseEntity.ok(ApiResponse.success(users));
            
        } catch (Exception e) {
            log.error("搜索用户失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/{id}/stats")
    @Operation(summary = "获取用户统计信息", description = "获取用户的统计数据")
    public ResponseEntity<ApiResponse<UserService.UserStatsResponse>> getUserStats(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        
        try {
            UserService.UserStatsResponse stats = userService.getUserStats(id);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取用户统计信息失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户统计信息
     */
    @GetMapping("/me/stats")
    @Operation(summary = "获取当前用户统计信息", description = "获取当前登录用户的统计数据")
    public ResponseEntity<ApiResponse<UserService.UserStatsResponse>> getCurrentUserStats(
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            UserService.UserStatsResponse stats = userService.getUserStats(userId);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取当前用户统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            try {
                return Long.parseLong(authentication.getName());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
