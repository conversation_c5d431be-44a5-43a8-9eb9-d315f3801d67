package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 图片代理控制器
 * 用于生成带签名的图片访问URL，防止图片被恶意访问
 */
@Slf4j
@RestController
@RequestMapping("/api/image")
@RequiredArgsConstructor
@Tag(name = "图片代理", description = "图片访问代理API")
public class ImageProxyController {

    private final FileUploadService fileUploadService;

    /**
     * 生成带签名的图片访问URL
     */
    @GetMapping("/signed-url")
    @Operation(summary = "生成签名URL", description = "为OSS图片生成带签名的访问URL")
    public ResponseEntity<ApiResponse<String>> generateSignedUrl(
            @Parameter(description = "图片URL", required = true) 
            @RequestParam String url,
            @Parameter(description = "过期时间（分钟）", example = "60") 
            @RequestParam(defaultValue = "60") int expireMinutes) {
        
        try {
            if (!StringUtils.hasText(url)) {
                return ResponseEntity.ok(ApiResponse.error("图片URL不能为空"));
            }
            
            // 验证过期时间范围（1分钟到24小时）
            if (expireMinutes < 1 || expireMinutes > 1440) {
                return ResponseEntity.ok(ApiResponse.error("过期时间必须在1-1440分钟之间"));
            }
            
            // 生成签名URL
            String signedUrl = fileUploadService.generatePresignedUrlFromFullUrl(url, expireMinutes);
            
            if (signedUrl == null) {
                return ResponseEntity.ok(ApiResponse.error("生成签名URL失败"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(signedUrl));
            
        } catch (Exception e) {
            log.error("生成签名URL失败: url={}", url, e);
            return ResponseEntity.ok(ApiResponse.error("生成签名URL失败: " + e.getMessage()));
        }
    }

    /**
     * 批量生成带签名的图片访问URL
     */
    @PostMapping("/batch-signed-urls")
    @Operation(summary = "批量生成签名URL", description = "为多个OSS图片生成带签名的访问URL")
    public ResponseEntity<ApiResponse<java.util.Map<String, String>>> generateBatchSignedUrls(
            @Parameter(description = "图片URL列表") 
            @RequestBody java.util.List<String> urls,
            @Parameter(description = "过期时间（分钟）", example = "60") 
            @RequestParam(defaultValue = "60") int expireMinutes) {
        
        try {
            if (urls == null || urls.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("图片URL列表不能为空"));
            }
            
            // 限制批量处理数量
            if (urls.size() > 50) {
                return ResponseEntity.ok(ApiResponse.error("批量处理数量不能超过50个"));
            }
            
            // 验证过期时间范围
            if (expireMinutes < 1 || expireMinutes > 1440) {
                return ResponseEntity.ok(ApiResponse.error("过期时间必须在1-1440分钟之间"));
            }
            
            java.util.Map<String, String> result = new java.util.HashMap<>();
            
            for (String url : urls) {
                if (StringUtils.hasText(url)) {
                    String signedUrl = fileUploadService.generatePresignedUrlFromFullUrl(url, expireMinutes);
                    result.put(url, signedUrl != null ? signedUrl : url);
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("批量生成签名URL失败: urls={}", urls, e);
            return ResponseEntity.ok(ApiResponse.error("批量生成签名URL失败: " + e.getMessage()));
        }
    }

    /**
     * 验证图片URL是否为本站图片
     */
    @GetMapping("/validate")
    @Operation(summary = "验证图片URL", description = "验证图片URL是否为本站OSS图片")
    public ResponseEntity<ApiResponse<Boolean>> validateImageUrl(
            @Parameter(description = "图片URL", required = true) 
            @RequestParam String url) {
        
        try {
            if (!StringUtils.hasText(url)) {
                return ResponseEntity.ok(ApiResponse.success(false));
            }
            
            // 检查是否为本站OSS图片
            boolean isValid = url.contains("cnprompt.oss-cn-shanghai.aliyuncs.com") 
                           && url.contains("prompt-images/");
            
            return ResponseEntity.ok(ApiResponse.success(isValid));
            
        } catch (Exception e) {
            log.error("验证图片URL失败: url={}", url, e);
            return ResponseEntity.ok(ApiResponse.success(false));
        }
    }
}
