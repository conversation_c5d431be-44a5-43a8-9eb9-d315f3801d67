package com.promptstore.controller;

import com.promptstore.dto.request.PasswordChangeRequest;
import com.promptstore.dto.request.UserProfileUpdateRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.UserProfileResponse;
import com.promptstore.service.UserProfileService;
import com.promptstore.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户信息控制器
 */
@RestController
@RequestMapping("/user-profile")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户信息管理", description = "用户信息相关接口")
public class UserProfileController {

    private final UserProfileService userProfileService;

    /**
     * 获取当前用户信息
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<UserProfileResponse>> getUserProfile() {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            UserProfileResponse profile = userProfileService.getUserProfile(userId);
            
            return ResponseEntity.ok(ApiResponse.success(profile));
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取用户信息失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的信息")
    public ResponseEntity<ApiResponse<UserProfileResponse>> updateUserProfile(
            @Valid @RequestBody UserProfileUpdateRequest request) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            UserProfileResponse profile = userProfileService.updateUserProfile(userId, request);
            
            log.info("用户信息更新成功: userId={}", userId);
            
            return ResponseEntity.ok(ApiResponse.success(profile, "用户信息更新成功"));
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新失败: " + e.getMessage()));
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    public ResponseEntity<ApiResponse<Void>> changePassword(
            @Valid @RequestBody PasswordChangeRequest request) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            userProfileService.changePassword(userId, request);
            
            log.info("用户密码修改成功: userId={}", userId);
            
            return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return ResponseEntity.ok(ApiResponse.error("修改失败: " + e.getMessage()));
        }
    }

    /**
     * 上传头像
     */
    @PostMapping("/avatar")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "上传头像", description = "上传用户头像")
    public ResponseEntity<ApiResponse<String>> uploadAvatar(
            @Parameter(description = "头像文件", required = true) 
            @RequestParam("file") MultipartFile file) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            String avatarUrl = userProfileService.uploadAvatar(userId, file);
            
            log.info("用户头像上传成功: userId={}, avatarUrl={}", userId, avatarUrl);
            
            return ResponseEntity.ok(ApiResponse.success(avatarUrl, "头像上传成功"));
        } catch (Exception e) {
            log.error("上传头像失败", e);
            return ResponseEntity.ok(ApiResponse.error("上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户公开信息
     */
    @GetMapping("/{userId}/profile")
    @Operation(summary = "获取用户公开信息", description = "获取指定用户的公开信息")
    public ResponseEntity<ApiResponse<UserProfileResponse>> getPublicUserProfile(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        try {
            UserProfileResponse profile = userProfileService.getPublicUserProfile(userId);
            
            return ResponseEntity.ok(ApiResponse.success(profile));
        } catch (Exception e) {
            log.error("获取用户公开信息失败: userId={}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取用户信息失败: " + e.getMessage()));
        }
    }
}
