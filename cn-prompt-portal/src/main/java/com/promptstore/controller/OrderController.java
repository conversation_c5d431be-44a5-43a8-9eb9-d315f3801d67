package com.promptstore.controller;

import com.promptstore.dto.request.OrderCreateRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.OrderResponse;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.enums.OrderStatus;
import com.promptstore.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/orders")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "订单管理", description = "订单相关接口")
public class OrderController {

    private final OrderService orderService;

    /**
     * 创建订单
     */
    @PostMapping
    @Operation(summary = "创建订单", description = "创建新的购买订单")
    public ResponseEntity<ApiResponse<OrderResponse>> createOrder(
            @Parameter(description = "订单信息", required = true) @Valid @RequestBody OrderCreateRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            OrderResponse response = orderService.createOrder(request, userId);
            
            return ResponseEntity.ok(ApiResponse.success(response, "订单创建成功"));
            
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的订单", description = "获取当前用户的订单列表")
    public ResponseEntity<ApiResponse<PageResponse<OrderResponse>>> getMyOrders(
            @Parameter(description = "订单状态") @RequestParam(required = false) OrderStatus status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            PageResponse<OrderResponse> response = orderService.getUserOrders(userId, status, page, size);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/user")
    @Operation(summary = "获取用户订单列表", description = "获取当前用户的所有订单")
    public ResponseEntity<ApiResponse<PageResponse<OrderResponse>>> getUserOrders(
            @Parameter(description = "订单状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {

        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }

            PageResponse<OrderResponse> response = orderService.getUserOrders(userId, status, page, size);

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("获取用户订单列表失败: userId={}", getCurrentUserId(authentication), e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据ID获取订单详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取订单详情", description = "根据ID获取订单详细信息")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderById(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            OrderResponse response = orderService.getOrderById(id, userId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取订单详情失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据订单号获取订单详情
     */
    @GetMapping("/no/{orderNo}")
    @Operation(summary = "根据订单号获取订单", description = "根据订单号获取订单详细信息")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderByNo(
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            OrderResponse response = orderService.getOrderByNo(orderNo, userId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("根据订单号获取订单失败: orderNo={}", orderNo, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/{id}/pay")
    @Operation(summary = "支付订单", description = "创建支付链接")
    public ResponseEntity<ApiResponse<String>> payOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            String paymentUrl = orderService.payOrder(id, userId);
            return ResponseEntity.ok(ApiResponse.success(paymentUrl, "支付链接创建成功"));
            
        } catch (Exception e) {
            log.error("创建支付失败: orderId={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消订单", description = "取消待支付的订单")
    public ResponseEntity<ApiResponse<Void>> cancelOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            orderService.cancelOrder(id, userId);
            
            return ResponseEntity.ok(ApiResponse.success("订单取消成功"));
            
        } catch (Exception e) {
            log.error("取消订单失败: orderId={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 检查是否已购买提示词
     */
    @GetMapping("/check-purchase/{promptId}")
    @Operation(summary = "检查购买状态", description = "检查用户是否已购买指定提示词")
    public ResponseEntity<ApiResponse<Boolean>> checkPurchase(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.success(false));
            }
            
            boolean purchased = orderService.hasUserPurchasedPrompt(userId, promptId);
            
            return ResponseEntity.ok(ApiResponse.success(purchased));
            
        } catch (Exception e) {
            log.error("检查购买状态失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            try {
                return Long.parseLong(authentication.getName());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
