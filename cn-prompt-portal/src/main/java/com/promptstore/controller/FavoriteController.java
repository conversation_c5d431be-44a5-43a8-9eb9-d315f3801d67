package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.service.FavoriteService;
import com.promptstore.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 收藏控制器
 */
@RestController
@RequestMapping("/api/favorites")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "收藏管理", description = "提示词收藏相关接口")
public class FavoriteController {

    private final FavoriteService favoriteService;

    /**
     * 切换收藏状态
     */
    @PostMapping("/{promptId}/toggle")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "切换收藏状态", description = "添加或取消收藏指定的提示词")
    public ResponseEntity<ApiResponse<Boolean>> toggleFavorite(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean isFavorited = favoriteService.toggleFavorite(userId, promptId);
            
            String action = isFavorited ? "添加收藏" : "取消收藏";
            log.info("{}成功: userId={}, promptId={}", action, userId, promptId);
            
            return ResponseEntity.ok(ApiResponse.success(isFavorited, action + "成功"));
        } catch (Exception e) {
            log.error("切换收藏状态失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("操作失败: " + e.getMessage()));
        }
    }

    /**
     * 检查收藏状态
     */
    @GetMapping("/{promptId}/status")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "检查收藏状态", description = "检查当前用户是否收藏了指定的提示词")
    public ResponseEntity<ApiResponse<Boolean>> getFavoriteStatus(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            boolean isFavorited = favoriteService.isFavorited(userId, promptId);
            
            return ResponseEntity.ok(ApiResponse.success(isFavorited));
        } catch (Exception e) {
            log.error("检查收藏状态失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("检查失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/my")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取我的收藏", description = "分页获取当前用户的收藏列表")
    public ResponseEntity<ApiResponse<Page<PromptResponse>>> getMyFavorites(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            Pageable pageable = PageRequest.of(page, size);
            Page<PromptResponse> favorites = favoriteService.getUserFavorites(userId, pageable);
            
            log.info("获取用户收藏列表成功: userId={}, page={}, size={}, total={}", 
                userId, page, size, favorites.getTotalElements());
            
            return ResponseEntity.ok(ApiResponse.success(favorites));
        } catch (Exception e) {
            log.error("获取用户收藏列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取收藏列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取收藏统计
     */
    @GetMapping("/count")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取收藏统计", description = "获取当前用户的收藏数量")
    public ResponseEntity<ApiResponse<Long>> getFavoriteCount() {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            long count = favoriteService.getUserFavoriteCount(userId);
            
            return ResponseEntity.ok(ApiResponse.success(count));
        } catch (Exception e) {
            log.error("获取收藏统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取统计失败: " + e.getMessage()));
        }
    }
}
