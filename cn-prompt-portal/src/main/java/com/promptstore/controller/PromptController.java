package com.promptstore.controller;

import com.promptstore.dto.request.PromptCreateRequest;
import com.promptstore.dto.request.PromptSearchRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.enums.PromptStatus;
import com.promptstore.service.PromptService;
import com.promptstore.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 提示词控制器
 */
@RestController
@RequestMapping("/prompts")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "提示词管理", description = "提示词相关接口")
public class PromptController {

    private final PromptService promptService;

    /**
     * 搜索提示词
     */
    @GetMapping("/search")
    @Operation(summary = "搜索提示词", description = "根据条件搜索提示词")
    public ResponseEntity<ApiResponse<PageResponse<PromptResponse>>> searchPrompts(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "AI模型") @RequestParam(required = false) String aiModel,
            @Parameter(description = "最低价格") @RequestParam(required = false) String minPrice,
            @Parameter(description = "最高价格") @RequestParam(required = false) String maxPrice,
            @Parameter(description = "是否免费") @RequestParam(required = false) Boolean isFree,
            @Parameter(description = "是否精选") @RequestParam(required = false) Boolean isFeatured,
            @Parameter(description = "标签") @RequestParam(required = false) String tag,
            @Parameter(description = "创作者ID") @RequestParam(required = false) Long creatorId,
            @Parameter(description = "排序方式") @RequestParam(defaultValue = "newest") String sortBy,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            Authentication authentication) {

        try {
            Long currentUserId = getCurrentUserId(authentication);
            
            PromptSearchRequest request = PromptSearchRequest.builder()
                    .keyword(keyword)
                    .categoryId(categoryId)
                    .aiModel(aiModel)
                    .minPrice(minPrice != null ? new java.math.BigDecimal(minPrice) : null)
                    .maxPrice(maxPrice != null ? new java.math.BigDecimal(maxPrice) : null)
                    .isFree(isFree)
                    .isFeatured(isFeatured)
                    .tag(tag)
                    .creatorId(creatorId)
                    .sortBy(sortBy)
                    .page(page)
                    .size(size)
                    .build();

            PageResponse<PromptResponse> response = promptService.searchPrompts(request, currentUserId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("搜索提示词失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取提示词详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取提示词详情", description = "根据ID获取提示词详细信息")
    public ResponseEntity<ApiResponse<PromptResponse>> getPromptById(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long currentUserId = getCurrentUserId(authentication);
            PromptResponse response = promptService.getPromptById(id, currentUserId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取提示词详情失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建提示词
     */
    @PostMapping
    @PreAuthorize("hasRole('CREATOR') or hasRole('ADMIN')")
    @Operation(summary = "创建提示词", description = "创建新的提示词")
    public ResponseEntity<ApiResponse<PromptResponse>> createPrompt(
            @Parameter(description = "提示词信息", required = true) @Valid @RequestBody PromptCreateRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            PromptResponse response = promptService.createPrompt(request, userId);
            
            return ResponseEntity.ok(ApiResponse.success(response, "创建成功"));
            
        } catch (Exception e) {
            log.error("创建提示词失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新提示词
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CREATOR') or hasRole('ADMIN')")
    @Operation(summary = "更新提示词", description = "更新提示词信息")
    public ResponseEntity<ApiResponse<PromptResponse>> updatePrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            @Parameter(description = "提示词信息", required = true) @Valid @RequestBody PromptCreateRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            PromptResponse response = promptService.updatePrompt(id, request, userId);
            
            return ResponseEntity.ok(ApiResponse.success(response, "更新成功"));
            
        } catch (Exception e) {
            log.error("更新提示词失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除提示词
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CREATOR') or hasRole('ADMIN')")
    @Operation(summary = "删除提示词", description = "删除指定的提示词")
    public ResponseEntity<ApiResponse<Void>> deletePrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            promptService.deletePrompt(id, userId);
            
            return ResponseEntity.ok(ApiResponse.success("删除成功"));
            
        } catch (Exception e) {
            log.error("删除提示词失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 发布提示词
     */
    @PostMapping("/{id}/publish")
    @PreAuthorize("hasRole('CREATOR') or hasRole('ADMIN')")
    @Operation(summary = "发布提示词", description = "将草稿状态的提示词提交审核")
    public ResponseEntity<ApiResponse<Void>> publishPrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }
            
            promptService.publishPrompt(id, userId);
            
            return ResponseEntity.ok(ApiResponse.success("提交审核成功"));
            
        } catch (Exception e) {
            log.error("发布提示词失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取提示词编辑信息
     */
    @GetMapping("/{id}/edit")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取提示词编辑信息", description = "获取指定提示词的编辑信息")
    public ResponseEntity<ApiResponse<PromptResponse>> getPromptForEdit(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id) {

        try {
            Long userId = SecurityUtils.getCurrentUserId();
            PromptResponse prompt = promptService.getPromptForEdit(id, userId);

            return ResponseEntity.ok(ApiResponse.success(prompt));

        } catch (Exception e) {
            log.error("获取提示词编辑信息失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户的提示词列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户提示词", description = "获取指定用户的提示词列表")
    public ResponseEntity<ApiResponse<PageResponse<PromptResponse>>> getUserPrompts(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "状态过滤") @RequestParam(required = false) PromptStatus status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            Authentication authentication) {
        
        try {
            Long currentUserId = getCurrentUserId(authentication);
            
            PageResponse<PromptResponse> response = promptService.getUserPrompts(
                userId, status, page, size, currentUserId
            );
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取用户提示词失败: userId={}", userId, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取热门提示词
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热门提示词", description = "获取热门提示词列表")
    public ResponseEntity<ApiResponse<List<PromptResponse>>> getHotPrompts(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit,
            Authentication authentication) {
        
        try {
            Long currentUserId = getCurrentUserId(authentication);
            List<PromptResponse> response = promptService.getHotPrompts(limit, currentUserId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取热门提示词失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取最新提示词
     */
    @GetMapping("/latest")
    @Operation(summary = "获取最新提示词", description = "获取最新发布的提示词列表")
    public ResponseEntity<ApiResponse<List<PromptResponse>>> getLatestPrompts(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit,
            Authentication authentication) {
        
        try {
            Long currentUserId = getCurrentUserId(authentication);
            List<PromptResponse> response = promptService.getLatestPrompts(limit, currentUserId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取最新提示词失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            try {
                return Long.parseLong(authentication.getName());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
