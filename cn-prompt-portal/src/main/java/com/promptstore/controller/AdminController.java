package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.PageResponse;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.entity.Prompt;
import com.promptstore.enums.PromptStatus;
import com.promptstore.enums.UserStatus;
import com.promptstore.service.PromptService;
import com.promptstore.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "管理员接口", description = "管理员专用接口")
public class AdminController {

    private final PromptService promptService;
    private final UserService userService;

    /**
     * 获取待审核提示词列表
     */
    @GetMapping("/prompts/pending")
    @Operation(summary = "获取待审核提示词", description = "获取所有待审核的提示词列表")
    public ResponseEntity<ApiResponse<PageResponse<PromptResponse>>> getPendingPrompts(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            Authentication authentication) {
        
        try {
            Long currentUserId = getCurrentUserId(authentication);
            
            // TODO: 实现获取待审核提示词的逻辑
            // PageResponse<PromptResponse> response = promptService.getPendingPrompts(page, size, currentUserId);
            
            // 临时返回空数据
            PageResponse<PromptResponse> response = PageResponse.of(
                java.util.Collections.emptyList(), 0L, page, size
            );
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取待审核提示词失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 审核提示词
     */
    @PostMapping("/prompts/{id}/review")
    @Operation(summary = "审核提示词", description = "审核通过或拒绝提示词")
    public ResponseEntity<ApiResponse<Void>> reviewPrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            @Parameter(description = "审核结果", required = true) @RequestParam PromptStatus status,
            @Parameter(description = "审核意见") @RequestParam(required = false) String comment,
            Authentication authentication) {
        
        try {
            Long adminId = getCurrentUserId(authentication);
            
            // TODO: 实现审核逻辑
            // promptService.reviewPrompt(id, status, comment, adminId);
            
            String message = status == PromptStatus.APPROVED ? "审核通过" : "审核拒绝";
            log.info("管理员 {} 审核提示词 {}: {}", adminId, id, status);
            
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("审核提示词失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    @Operation(summary = "获取用户列表", description = "获取所有用户列表")
    public ResponseEntity<ApiResponse<PageResponse<UserResponse>>> getUsers(
            @Parameter(description = "用户名关键词") @RequestParam(required = false) String username,
            @Parameter(description = "用户状态") @RequestParam(required = false) UserStatus status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            // TODO: 实现获取用户列表的逻辑
            // PageResponse<UserResponse> response = userService.getUsers(username, status, page, size);
            
            // 临时返回空数据
            PageResponse<UserResponse> response = PageResponse.of(
                java.util.Collections.emptyList(), 0L, page, size
            );
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新用户状态
     */
    @PostMapping("/users/{id}/status")
    @Operation(summary = "更新用户状态", description = "激活、禁用或封禁用户")
    public ResponseEntity<ApiResponse<Void>> updateUserStatus(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id,
            @Parameter(description = "用户状态", required = true) @RequestParam UserStatus status,
            @Parameter(description = "操作原因") @RequestParam(required = false) String reason,
            Authentication authentication) {
        
        try {
            Long adminId = getCurrentUserId(authentication);
            
            // TODO: 实现更新用户状态的逻辑
            // userService.updateUserStatus(id, status, reason, adminId);
            
            log.info("管理员 {} 更新用户 {} 状态为: {}", adminId, id, status);
            
            return ResponseEntity.ok(ApiResponse.success("用户状态更新成功"));
            
        } catch (Exception e) {
            log.error("更新用户状态失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取系统统计", description = "获取系统整体统计信息")
    public ResponseEntity<ApiResponse<AdminStatsResponse>> getSystemStats() {
        try {
            // TODO: 实现系统统计逻辑
            AdminStatsResponse stats = AdminStatsResponse.builder()
                    .totalUsers(0L)
                    .totalCreators(0L)
                    .totalPrompts(0L)
                    .pendingPrompts(0L)
                    .totalOrders(0L)
                    .totalRevenue(java.math.BigDecimal.ZERO)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除提示词
     */
    @DeleteMapping("/prompts/{id}")
    @Operation(summary = "删除提示词", description = "管理员删除提示词")
    public ResponseEntity<ApiResponse<Void>> deletePrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            @Parameter(description = "删除原因") @RequestParam(required = false) String reason,
            Authentication authentication) {
        
        try {
            Long adminId = getCurrentUserId(authentication);
            
            // TODO: 实现管理员删除提示词的逻辑
            // promptService.adminDeletePrompt(id, reason, adminId);
            
            log.info("管理员 {} 删除提示词: {}", adminId, id);
            
            return ResponseEntity.ok(ApiResponse.success("提示词删除成功"));
            
        } catch (Exception e) {
            log.error("删除提示词失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 设置提示词为精选
     */
    @PostMapping("/prompts/{id}/featured")
    @Operation(summary = "设置精选提示词", description = "设置或取消提示词精选状态")
    public ResponseEntity<ApiResponse<Void>> setPromptFeatured(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long id,
            @Parameter(description = "是否精选", required = true) @RequestParam Boolean featured,
            Authentication authentication) {
        
        try {
            Long adminId = getCurrentUserId(authentication);
            
            // TODO: 实现设置精选的逻辑
            // promptService.setPromptFeatured(id, featured, adminId);
            
            String message = featured ? "设置为精选" : "取消精选";
            log.info("管理员 {} {} 提示词: {}", adminId, message, id);
            
            return ResponseEntity.ok(ApiResponse.success(message + "成功"));
            
        } catch (Exception e) {
            log.error("设置提示词精选状态失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            try {
                return Long.parseLong(authentication.getName());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 管理员统计响应DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class AdminStatsResponse {
        private Long totalUsers;
        private Long totalCreators;
        private Long totalPrompts;
        private Long pendingPrompts;
        private Long totalOrders;
        private java.math.BigDecimal totalRevenue;
        private Long todayUsers;
        private Long todayOrders;
        private java.math.BigDecimal todayRevenue;
    }
}
