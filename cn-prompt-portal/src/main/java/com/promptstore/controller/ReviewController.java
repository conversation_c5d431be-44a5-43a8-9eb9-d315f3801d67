package com.promptstore.controller;

import com.promptstore.dto.request.ReviewRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.ReviewResponse;
import com.promptstore.dto.response.ReviewStatistics;
import com.promptstore.service.ReviewService;
import com.promptstore.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 评价控制器
 */
@RestController
@RequestMapping("/api/reviews")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "评价管理", description = "提示词评价相关接口")
public class ReviewController {

    private final ReviewService reviewService;

    /**
     * 添加或更新评价
     */
    @PostMapping("/{promptId}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "添加或更新评价", description = "对指定提示词进行评价或更新已有评价")
    public ResponseEntity<ApiResponse<ReviewResponse>> addOrUpdateReview(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId,
            @Valid @RequestBody ReviewRequest request) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            ReviewResponse review = reviewService.addOrUpdateReview(userId, promptId, request);
            
            log.info("评价操作成功: userId={}, promptId={}, rating={}", 
                userId, promptId, request.getRating());
            
            return ResponseEntity.ok(ApiResponse.success(review, "评价成功"));
        } catch (Exception e) {
            log.error("评价操作失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("评价失败: " + e.getMessage()));
        }
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/{promptId}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "删除评价", description = "删除用户对指定提示词的评价")
    public ResponseEntity<ApiResponse<Void>> deleteReview(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            reviewService.deleteReview(userId, promptId);
            
            log.info("删除评价成功: userId={}, promptId={}", userId, promptId);
            
            return ResponseEntity.ok(ApiResponse.success( "删除评价成功"));
        } catch (Exception e) {
            log.error("删除评价失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户对提示词的评价
     */
    @GetMapping("/{promptId}/my")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取我的评价", description = "获取当前用户对指定提示词的评价")
    public ResponseEntity<ApiResponse<ReviewResponse>> getMyReview(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            ReviewResponse review = reviewService.getUserReview(userId, promptId);
            
            return ResponseEntity.ok(ApiResponse.success(review));
        } catch (Exception e) {
            log.error("获取用户评价失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("获取评价失败: " + e.getMessage()));
        }
    }

    /**
     * 获取提示词的评价列表
     */
    @GetMapping("/{promptId}")
    @Operation(summary = "获取提示词评价列表", description = "分页获取指定提示词的所有评价")
    public ResponseEntity<ApiResponse<Page<ReviewResponse>>> getPromptReviews(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ReviewResponse> reviews = reviewService.getPromptReviews(promptId, pageable);
            
            log.info("获取提示词评价列表成功: promptId={}, page={}, size={}, total={}", 
                promptId, page, size, reviews.getTotalElements());
            
            return ResponseEntity.ok(ApiResponse.success(reviews));
        } catch (Exception e) {
            log.error("获取提示词评价列表失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("获取评价列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取提示词评价统计
     */
    @GetMapping("/{promptId}/statistics")
    @Operation(summary = "获取评价统计", description = "获取指定提示词的评价统计信息")
    public ResponseEntity<ApiResponse<ReviewStatistics>> getReviewStatistics(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId) {
        
        try {
            ReviewStatistics statistics = reviewService.getPromptReviewStatistics(promptId);
            
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取评价统计失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("获取统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取我的评价列表
     */
    @GetMapping("/my")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取我的评价列表", description = "分页获取当前用户的所有评价")
    public ResponseEntity<ApiResponse<Page<ReviewResponse>>> getMyReviews(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            Pageable pageable = PageRequest.of(page, size);
            Page<ReviewResponse> reviews = reviewService.getUserReviews(userId, pageable);
            
            log.info("获取用户评价列表成功: userId={}, page={}, size={}, total={}", 
                userId, page, size, reviews.getTotalElements());
            
            return ResponseEntity.ok(ApiResponse.success(reviews));
        } catch (Exception e) {
            log.error("获取用户评价列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取评价列表失败: " + e.getMessage()));
        }
    }
}
