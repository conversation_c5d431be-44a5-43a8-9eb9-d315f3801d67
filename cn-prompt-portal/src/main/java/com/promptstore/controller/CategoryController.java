package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.CategoryResponse;
import com.promptstore.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "分类管理", description = "分类相关接口")
public class CategoryController {

    private final CategoryService categoryService;

    /**
     * 获取所有根分类
     */
    @GetMapping("/root")
    @Operation(summary = "获取根分类", description = "获取所有根分类列表")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getRootCategories() {
        try {
            List<CategoryResponse> categories = categoryService.getRootCategories();
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取根分类失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取所有分类
     */
    @GetMapping
    @Operation(summary = "获取所有分类", description = "获取所有活跃分类列表")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getAllCategories() {
        try {
            List<CategoryResponse> categories = categoryService.getAllCategories();
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取所有分类失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 分页获取分类
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取分类", description = "分页获取分类列表")
    public ResponseEntity<ApiResponse<Page<CategoryResponse>>> getCategories(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<CategoryResponse> categories = categoryService.getCategories(pageable);
            
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("分页获取分类失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据ID获取分类
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    public ResponseEntity<ApiResponse<CategoryResponse>> getCategoryById(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id) {
        
        try {
            CategoryResponse category = categoryService.getCategoryById(id);
            return ResponseEntity.ok(ApiResponse.success(category));
        } catch (Exception e) {
            log.error("获取分类详情失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据slug获取分类
     */
    @GetMapping("/slug/{slug}")
    @Operation(summary = "根据slug获取分类", description = "根据slug获取分类信息")
    public ResponseEntity<ApiResponse<CategoryResponse>> getCategoryBySlug(
            @Parameter(description = "分类slug", required = true) @PathVariable String slug) {
        
        try {
            CategoryResponse category = categoryService.getCategoryBySlug(slug);
            return ResponseEntity.ok(ApiResponse.success(category));
        } catch (Exception e) {
            log.error("根据slug获取分类失败: slug={}", slug, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取子分类
     */
    @GetMapping("/{parentId}/children")
    @Operation(summary = "获取子分类", description = "获取指定父分类的子分类列表")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getChildCategories(
            @Parameter(description = "父分类ID", required = true) @PathVariable Long parentId) {
        
        try {
            List<CategoryResponse> categories = categoryService.getChildCategories(parentId);
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取子分类失败: parentId={}", parentId, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 搜索分类
     */
    @GetMapping("/search")
    @Operation(summary = "搜索分类", description = "根据名称搜索分类")
    public ResponseEntity<ApiResponse<Page<CategoryResponse>>> searchCategories(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String name,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<CategoryResponse> categories = categoryService.searchCategories(name, pageable);
            
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("搜索分类失败: name={}", name, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取热门分类
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热门分类", description = "获取热门分类列表")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getHotCategories(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<CategoryResponse> categories = categoryService.getHotCategories(limit);
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取热门分类失败", e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }
}
