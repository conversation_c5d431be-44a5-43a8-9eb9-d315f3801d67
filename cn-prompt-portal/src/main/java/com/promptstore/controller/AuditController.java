package com.promptstore.controller;

import com.promptstore.dto.request.AuditRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.PromptResponse;
import com.promptstore.enums.AuditStatus;
import com.promptstore.service.AuditService;
import com.promptstore.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 审核管理控制器
 */
@RestController
@RequestMapping("/api/admin/audit")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "审核管理", description = "提示词审核相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AuditController {

    private final AuditService auditService;

    /**
     * 获取待审核提示词列表
     */
    @GetMapping("/prompts")
    @Operation(summary = "获取待审核提示词列表", description = "分页获取待审核的提示词列表，支持状态筛选和关键词搜索")
    public ResponseEntity<ApiResponse<Page<PromptResponse>>> getAuditPrompts(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "审核状态筛选") @RequestParam(required = false) AuditStatus status,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword) {
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<PromptResponse> prompts = auditService.getPendingPrompts(pageable, status, keyword);
            
            log.info("获取审核列表成功: page={}, size={}, status={}, keyword={}, total={}", 
                page, size, status, keyword, prompts.getTotalElements());
            
            return ResponseEntity.ok(ApiResponse.success(prompts));
        } catch (Exception e) {
            log.error("获取审核列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取审核列表失败: " + e.getMessage()));
        }
    }

    /**
     * 审核提示词
     */
    @PostMapping("/prompts/{promptId}")
    @Operation(summary = "审核提示词", description = "对指定的提示词进行审核，可以通过或拒绝")
    public ResponseEntity<ApiResponse<Void>> auditPrompt(
            @Parameter(description = "提示词ID", required = true) @PathVariable Long promptId,
            @Valid @RequestBody AuditRequest request) {
        
        try {
            Long auditorId = SecurityUtils.getCurrentUserId();
            auditService.auditPrompt(promptId, auditorId, request);
            
            log.info("提示词审核成功: promptId={}, auditorId={}, status={}", 
                promptId, auditorId, request.getAuditStatus());
            
            return ResponseEntity.ok(ApiResponse.success( "审核完成"));
        } catch (Exception e) {
            log.error("提示词审核失败: promptId={}", promptId, e);
            return ResponseEntity.ok(ApiResponse.error("审核失败: " + e.getMessage()));
        }
    }

    /**
     * 获取审核统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取审核统计信息", description = "获取各种审核状态的统计数据")
    public ResponseEntity<ApiResponse<AuditService.AuditStatistics>> getAuditStatistics() {
        try {
            AuditService.AuditStatistics statistics = auditService.getAuditStatistics();
            
            log.info("获取审核统计成功: pending={}, approved={}, rejected={}", 
                statistics.getPendingCount(), statistics.getApprovedCount(), statistics.getRejectedCount());
            
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取审核统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取审核统计失败: " + e.getMessage()));
        }
    }
}
