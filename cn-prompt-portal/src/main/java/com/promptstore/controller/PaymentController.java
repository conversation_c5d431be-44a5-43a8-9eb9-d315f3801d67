package com.promptstore.controller;

import com.promptstore.enums.PaymentMethod;
import com.promptstore.service.OrderService;
import com.promptstore.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 支付回调控制器
 */
@RestController
@RequestMapping("/payment")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "支付回调", description = "支付回调接口")
public class PaymentController {

    private final PaymentService paymentService;
    private final OrderService orderService;

    /**
     * 支付宝支付回调
     */
    @PostMapping("/alipay/notify")
    @Operation(summary = "支付宝支付回调", description = "处理支付宝支付结果通知")
    public ResponseEntity<String> alipayNotify(HttpServletRequest request) {
        try {
            // 获取支付宝回调参数
            Map<String, String[]> parameterMap = request.getParameterMap();
            
            // 提取关键参数
            String tradeNo = getParameter(parameterMap, "trade_no");
            String outTradeNo = getParameter(parameterMap, "out_trade_no");
            String tradeStatus = getParameter(parameterMap, "trade_status");
            String sign = getParameter(parameterMap, "sign");
            
            log.info("收到支付宝回调: tradeNo={}, outTradeNo={}, tradeStatus={}", 
                    tradeNo, outTradeNo, tradeStatus);

            // 验证签名
            String data = buildSignData(parameterMap);
            if (!paymentService.verifyPaymentCallback(PaymentMethod.ALIPAY, sign, data)) {
                log.error("支付宝回调签名验证失败");
                return ResponseEntity.ok("failure");
            }

            // 处理支付结果
            PaymentService.PaymentCallbackResult result = 
                    paymentService.handleAlipayCallback(tradeNo, outTradeNo, tradeStatus);
            
            orderService.handlePaymentCallback(result.getOrderNo(), result.getPaymentId(), result.isSuccess());

            return ResponseEntity.ok("success");
            
        } catch (Exception e) {
            log.error("处理支付宝回调失败", e);
            return ResponseEntity.ok("failure");
        }
    }

    /**
     * 微信支付回调
     */
    @PostMapping("/wechat/notify")
    @Operation(summary = "微信支付回调", description = "处理微信支付结果通知")
    public ResponseEntity<String> wechatNotify(@RequestBody String xmlData) {
        try {
            log.info("收到微信支付回调: {}", xmlData);

            // TODO: 解析微信XML回调数据
            // 这里简化处理，实际需要解析XML
            String transactionId = "mock_transaction_id";
            String outTradeNo = "mock_out_trade_no";
            String resultCode = "SUCCESS";
            String sign = "mock_sign";

            // 验证签名
            if (!paymentService.verifyPaymentCallback(PaymentMethod.WECHAT, sign, xmlData)) {
                log.error("微信支付回调签名验证失败");
                return ResponseEntity.ok(buildWechatFailResponse());
            }

            // 处理支付结果
            PaymentService.PaymentCallbackResult result = 
                    paymentService.handleWechatCallback(transactionId, outTradeNo, resultCode);
            
            orderService.handlePaymentCallback(result.getOrderNo(), result.getPaymentId(), result.isSuccess());

            return ResponseEntity.ok(buildWechatSuccessResponse());
            
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return ResponseEntity.ok(buildWechatFailResponse());
        }
    }

    /**
     * 支付宝支付返回页面
     */
    @GetMapping("/alipay/return")
    @Operation(summary = "支付宝支付返回", description = "支付宝支付完成后的返回页面")
    public ResponseEntity<String> alipayReturn(HttpServletRequest request) {
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            String outTradeNo = getParameter(parameterMap, "out_trade_no");
            String tradeNo = getParameter(parameterMap, "trade_no");
            
            log.info("支付宝支付返回: outTradeNo={}, tradeNo={}", outTradeNo, tradeNo);
            
            // 重定向到前端支付结果页面
            String redirectUrl = String.format("/payment/result?orderNo=%s&success=true", outTradeNo);
            
            return ResponseEntity.ok()
                    .header("Location", redirectUrl)
                    .body("支付成功，正在跳转...");
            
        } catch (Exception e) {
            log.error("处理支付宝返回失败", e);
            return ResponseEntity.ok("支付处理失败");
        }
    }

    /**
     * 获取参数值
     */
    private String getParameter(Map<String, String[]> parameterMap, String key) {
        String[] values = parameterMap.get(key);
        return values != null && values.length > 0 ? values[0] : null;
    }

    /**
     * 构建签名数据
     */
    private String buildSignData(Map<String, String[]> parameterMap) {
        // TODO: 实现支付宝签名数据构建逻辑
        return "mock_sign_data";
    }

    /**
     * 构建微信成功响应
     */
    private String buildWechatSuccessResponse() {
        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
    }

    /**
     * 构建微信失败响应
     */
    private String buildWechatFailResponse() {
        return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>";
    }
}
