package com.promptstore.controller;

import com.promptstore.dto.request.EmailLoginRequest;
import com.promptstore.dto.request.EmailRegisterRequest;
import com.promptstore.dto.request.SendVerificationCodeRequest;
import com.promptstore.dto.response.ApiResponse;
import com.promptstore.dto.response.LoginResponse;
import com.promptstore.dto.response.UserResponse;
import com.promptstore.dto.response.WechatQRCodeResponse;
import com.promptstore.entity.User;
import com.promptstore.exception.ResourceNotFoundException;
import com.promptstore.repository.UserRepository;
import com.promptstore.service.AuthService;
import com.promptstore.service.EmailVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final EmailVerificationService emailVerificationService;
    private final UserRepository userRepository;

    @Value("${app.wechat.app-id}")
    private String wechatAppId;

    @Value("${app.wechat.redirect-uri}")
    private String redirectUri;

    /**
     * 获取微信登录二维码
     */
    @GetMapping("/wechat/qrcode")
    @Operation(summary = "获取微信登录二维码", description = "生成微信扫码登录的二维码URL")
    public ResponseEntity<ApiResponse<WechatQRCodeResponse>> getWechatQRCode() {
        try {
            // 生成状态参数，用于防止CSRF攻击
            String state = UUID.randomUUID().toString().replace("-", "");
            
            // 构建微信登录URL
            String qrCodeUrl = String.format(
                "https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect",
                wechatAppId,
                URLEncoder.encode(redirectUri, StandardCharsets.UTF_8),
                state
            );
            
            WechatQRCodeResponse response = WechatQRCodeResponse.builder()
                .qrCodeUrl(qrCodeUrl)
                .state(state)
                .build();
                
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("生成微信登录二维码失败", e);
            return ResponseEntity.ok(ApiResponse.error("生成二维码失败"));
        }
    }

    /**
     * 微信扫码登录
     */
    @PostMapping("/wechat/login")
    @Operation(summary = "微信扫码登录", description = "通过微信授权码完成登录")
    public ResponseEntity<ApiResponse<LoginResponse>> wechatLogin(
            @Parameter(description = "微信授权码", required = true)
            @RequestParam String code,
            @Parameter(description = "状态参数")
            @RequestParam(required = false) String state) {
        
        try {
            log.info("微信登录请求: code={}, state={}", code, state);
            
            LoginResponse response = authService.wechatLogin(code);
            
            log.info("用户 {} 微信登录成功", response.getUser().getUsername());
            
            return ResponseEntity.ok(ApiResponse.success(response, "登录成功"));
            
        } catch (Exception e) {
            log.error("微信登录失败: code={}", code, e);
            return ResponseEntity.ok(ApiResponse.error(400, e.getMessage()));
        }
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新访问令牌", description = "使用刷新令牌获取新的访问令牌")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(
            @Parameter(description = "刷新令牌", required = true)
            @RequestHeader("Authorization") String refreshToken) {
        
        try {
            // 移除Bearer前缀
            if (refreshToken.startsWith("Bearer ")) {
                refreshToken = refreshToken.substring(7);
            }
            
            LoginResponse response = authService.refreshToken(refreshToken);
            
            return ResponseEntity.ok(ApiResponse.success(response, "令牌刷新成功"));
            
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return ResponseEntity.ok(ApiResponse.unauthorized(e.getMessage()));
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "退出登录，清除服务端会话")
    public ResponseEntity<ApiResponse<Void>> logout(Authentication authentication) {
        try {
            if (authentication != null && authentication.isAuthenticated()) {
                Long userId = Long.parseLong(authentication.getName());
                authService.logout(userId);
                
                log.info("用户 {} 已登出", userId);
            }
            
            return ResponseEntity.ok(ApiResponse.<Void>success("登出成功"));
            
        } catch (Exception e) {
            log.error("登出失败", e);
            return ResponseEntity.ok(ApiResponse.<Void>error("登出失败"));
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/email/send-code")
    @Operation(summary = "发送邮箱验证码", description = "向指定邮箱发送验证码")
    public ResponseEntity<ApiResponse<Void>> sendVerificationCode(
            @Valid @RequestBody SendVerificationCodeRequest request) {
        try {
            emailVerificationService.sendVerificationCode(request.getEmail(), request.getType());

            log.info("验证码发送成功: email={}, type={}", request.getEmail(), request.getType());

            return ResponseEntity.ok(ApiResponse.<Void>success("验证码发送成功"));

        } catch (Exception e) {
            log.error("发送验证码失败: email={}, type={}", request.getEmail(), request.getType(), e);
            return ResponseEntity.ok(ApiResponse.<Void>error(e.getMessage()));
        }
    }

    /**
     * 邮箱注册
     */
    @PostMapping("/email/register")
    @Operation(summary = "邮箱注册", description = "使用邮箱和验证码注册新用户")
    public ResponseEntity<ApiResponse<LoginResponse>> emailRegister(
            @Valid @RequestBody EmailRegisterRequest request) {
        try {
            LoginResponse response = authService.emailRegister(request);

            log.info("用户邮箱注册成功: email={}, username={}", request.getEmail(), request.getUsername());

            return ResponseEntity.ok(ApiResponse.success(response, "注册成功"));

        } catch (Exception e) {
            log.error("邮箱注册失败: email={}, username={}", request.getEmail(), request.getUsername(), e);
            return ResponseEntity.ok(ApiResponse.error(400, e.getMessage()));
        }
    }

    /**
     * 邮箱登录
     */
    @PostMapping("/email/login")
    @Operation(summary = "邮箱登录", description = "使用邮箱和验证码登录")
    public ResponseEntity<ApiResponse<LoginResponse>> emailLogin(
            @Valid @RequestBody EmailLoginRequest request) {
        try {
            LoginResponse response = authService.emailLogin(request);

            log.info("用户邮箱登录成功: email={}", request.getEmail());

            return ResponseEntity.ok(ApiResponse.success(response, "登录成功"));

        } catch (Exception e) {
            log.error("邮箱登录失败: email={}", request.getEmail(), e);
            return ResponseEntity.ok(ApiResponse.error(400, e.getMessage()));
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentUser(Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.ok(ApiResponse.unauthorized("未登录"));
            }

            // 获取用户ID
            Long userId = Long.valueOf(authentication.getPrincipal().toString());

            // 获取用户详细信息
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

            // 转换为响应DTO
            UserResponse userResponse = UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .bio(user.getBio())
                .role(user.getRole())
                .status(user.getStatus())
                .emailVerified(user.getEmailVerified())
                .phoneVerified(user.getPhoneVerified())
                .totalEarnings(user.getTotalEarnings())
                .totalSpent(user.getTotalSpent())
                .followerCount(user.getFollowerCount())
                .followingCount(user.getFollowingCount())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();

            return ResponseEntity.ok(ApiResponse.success(userResponse, "获取成功"));

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取用户信息失败"));
        }
    }
}
