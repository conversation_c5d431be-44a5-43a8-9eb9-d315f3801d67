package com.promptstore.controller;

import com.promptstore.dto.response.ApiResponse;
import com.promptstore.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "文件上传", description = "文件上传相关接口")
public class FileUploadController {

    private final FileUploadService fileUploadService;

    /**
     * 上传图片
     */
    @PostMapping("/image")
    @Operation(summary = "上传图片", description = "上传图片文件到阿里云OSS")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadImage(
            @Parameter(description = "图片文件", required = true) @RequestParam("file") MultipartFile file,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }

            String fileUrl = fileUploadService.uploadImage(file, userId);
            
            Map<String, String> result = new HashMap<>();
            result.put("url", fileUrl);
            result.put("filename", file.getOriginalFilename());
            result.put("size", String.valueOf(file.getSize()));
            
            log.info("图片上传成功: userId={}, filename={}, url={}", 
                    userId, file.getOriginalFilename(), fileUrl);
            
            return ResponseEntity.ok(ApiResponse.success(result, "图片上传成功"));
            
        } catch (Exception e) {
            log.error("图片上传失败: filename={}", file.getOriginalFilename(), e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除图片
     */
    @DeleteMapping("/image")
    @Operation(summary = "删除图片", description = "删除已上传的图片文件")
    public ResponseEntity<ApiResponse<String>> deleteImage(
            @Parameter(description = "图片URL", required = true) @RequestParam("url") String imageUrl,
            Authentication authentication) {
        
        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }

            fileUploadService.deleteFile(imageUrl);
            
            log.info("图片删除成功: userId={}, url={}", userId, imageUrl);
            
            return ResponseEntity.ok(ApiResponse.success("图片删除成功"));
            
        } catch (Exception e) {
            log.error("图片删除失败: url={}", imageUrl, e);
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 生成带签名的图片访问URL
     */
    @GetMapping("/image/signed-url")
    @Operation(summary = "生成签名URL", description = "为OSS图片生成带签名的访问URL")
    public ResponseEntity<ApiResponse<String>> generateSignedUrl(
            @Parameter(description = "图片URL", required = true)
            @RequestParam String url,
            @Parameter(description = "过期时间（分钟）", example = "60")
            @RequestParam(defaultValue = "60") int expireMinutes,
            Authentication authentication) {

        try {
            Long userId = getCurrentUserId(authentication);
            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.unauthorized("请先登录"));
            }

            if (!org.springframework.util.StringUtils.hasText(url)) {
                return ResponseEntity.ok(ApiResponse.error("图片URL不能为空"));
            }

            // 验证过期时间范围（1分钟到24小时）
            if (expireMinutes < 1 || expireMinutes > 1440) {
                return ResponseEntity.ok(ApiResponse.error("过期时间必须在1-1440分钟之间"));
            }

            // 生成签名URL
            String signedUrl = fileUploadService.generatePresignedUrlFromFullUrl(url, expireMinutes);

            if (signedUrl == null) {
                return ResponseEntity.ok(ApiResponse.error("生成签名URL失败"));
            }

            return ResponseEntity.ok(ApiResponse.success(signedUrl));

        } catch (Exception e) {
            log.error("生成签名URL失败: url={}", url, e);
            return ResponseEntity.ok(ApiResponse.error("生成签名URL失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            log.warn("无法解析用户ID: {}", authentication.getName());
            return null;
        }
    }
}
