package com.promptstore.task;

import com.promptstore.service.EmailVerificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 邮箱验证码清理定时任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EmailVerificationCleanupTask {

    private final EmailVerificationService emailVerificationService;

    /**
     * 每小时清理一次过期的验证码
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredVerifications() {
        try {
            log.debug("开始清理过期的邮箱验证码");
            emailVerificationService.cleanupExpiredVerifications();
            log.debug("过期邮箱验证码清理完成");
        } catch (Exception e) {
            log.error("清理过期邮箱验证码失败", e);
        }
    }
}
