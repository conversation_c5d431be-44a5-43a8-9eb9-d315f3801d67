package com.promptstore.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 邮箱注册请求
 */
@Data
@Schema(description = "邮箱注册请求")
public class EmailRegisterRequest {

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5_-]+$", message = "用户名只能包含字母、数字、中文、下划线和短横线")
    @Schema(description = "用户名", example = "张三")
    private String username;

    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    @Schema(description = "验证码", example = "123456")
    private String code;

    @Size(max = 200, message = "个人简介不能超过200个字符")
    @Schema(description = "个人简介", example = "这是我的个人简介")
    private String bio;
}
