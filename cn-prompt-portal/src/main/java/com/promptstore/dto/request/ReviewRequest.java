package com.promptstore.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 评价请求DTO
 */
@Data
public class ReviewRequest {

    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分不能小于1星")
    @Max(value = 5, message = "评分不能大于5星")
    private Integer rating;

    @Size(max = 1000, message = "评价内容不能超过1000个字符")
    private String content;
}
