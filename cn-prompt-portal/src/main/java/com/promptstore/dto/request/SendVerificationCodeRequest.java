package com.promptstore.dto.request;

import com.promptstore.enums.VerificationType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发送验证码请求
 */
@Data
@Schema(description = "发送验证码请求")
public class SendVerificationCodeRequest {

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @NotNull(message = "验证类型不能为空")
    @Schema(description = "验证类型", example = "REGISTER")
    private VerificationType type;
}
