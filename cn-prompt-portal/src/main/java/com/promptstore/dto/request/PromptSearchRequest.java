package com.promptstore.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提示词搜索请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromptSearchRequest {

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * AI模型
     */
    private String aiModel;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 是否免费
     */
    private Boolean isFree;

    /**
     * 是否精选
     */
    private Boolean isFeatured;

    /**
     * 标签
     */
    private String tag;

    /**
     * 创作者ID
     */
    private Long creatorId;

    /**
     * 排序方式
     */
    @Builder.Default
    private String sortBy = "newest";

    /**
     * 页码
     */
    @Builder.Default
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Builder.Default
    private Integer size = 20;
}
