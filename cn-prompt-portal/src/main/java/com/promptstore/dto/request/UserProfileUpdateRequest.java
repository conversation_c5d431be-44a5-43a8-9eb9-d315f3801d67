package com.promptstore.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户信息更新请求DTO
 */
@Data
public class UserProfileUpdateRequest {

    @Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Size(max = 200, message = "个人简介不能超过200个字符")
    private String bio;

    @Size(max = 500, message = "头像URL不能超过500个字符")
    private String avatar;

    @Size(max = 50, message = "真实姓名不能超过50个字符")
    private String realName;

    @Size(max = 20, message = "手机号不能超过20个字符")
    private String phone;
}
