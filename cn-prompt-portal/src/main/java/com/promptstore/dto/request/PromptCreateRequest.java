package com.promptstore.dto.request;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建提示词请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromptCreateRequest {

    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;

    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    private String description;

    @NotBlank(message = "提示词内容不能为空")
    @Size(max = 10000, message = "内容长度不能超过10000个字符")
    private String content;

    @NotBlank(message = "AI模型不能为空")
    @Size(max = 50, message = "AI模型长度不能超过50个字符")
    private String aiModel;

    @Size(max = 50, message = "模型版本长度不能超过50个字符")
    private String modelVersion;

    @NotNull(message = "分类不能为空")
    private Long categoryId;

    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.00", message = "价格不能为负数")
    @DecimalMax(value = "9999.99", message = "价格不能超过9999.99")
    private BigDecimal price;

    @DecimalMin(value = "0.01", message = "原价必须大于0")
    @DecimalMax(value = "9999.99", message = "原价不能超过9999.99")
    private BigDecimal originalPrice;

    @Size(max = 10, message = "预览图片不能超过10张")
    private List<String> previewImages;

    @Size(max = 5, message = "示例输出不能超过5个")
    private List<String> exampleOutputs;

    @Size(max = 2000, message = "使用说明长度不能超过2000个字符")
    private String usageInstructions;

    @Size(max = 20, message = "标签不能超过20个")
    private List<String> tags;

    @Builder.Default
    private Boolean isFree = false;

    @Schema(description = "图片URL", example = "https://example.com/image.jpg")
    @Size(max = 500, message = "图片URL长度不能超过500个字符")
    private String imageUrl;
}
