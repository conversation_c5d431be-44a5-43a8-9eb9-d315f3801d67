package com.promptstore.dto.request;

import com.promptstore.enums.PaymentMethod;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 创建订单请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCreateRequest {

    @NotEmpty(message = "提示词列表不能为空")
    private List<Long> promptIds;

    @NotNull(message = "支付方式不能为空")
    private PaymentMethod paymentMethod;

    private String remark;
}
