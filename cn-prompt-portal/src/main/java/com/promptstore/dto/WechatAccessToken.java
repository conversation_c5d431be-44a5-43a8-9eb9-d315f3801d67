package com.promptstore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信访问令牌DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatAccessToken {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 微信OpenID
     */
    private String openId;

    /**
     * 授权作用域
     */
    private String scope;
}
