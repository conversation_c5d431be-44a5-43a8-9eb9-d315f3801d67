package com.promptstore.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.promptstore.enums.UserRole;
import com.promptstore.enums.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserResponse {

    private Long id;
    private String username;
    private String email;
    private String phone;
    private String avatarUrl;
    private String bio;
    private UserRole role;
    private UserStatus status;
    private Boolean emailVerified;
    private Boolean phoneVerified;
    private BigDecimal totalEarnings;
    private BigDecimal totalSpent;
    private Integer followerCount;
    private Integer followingCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLoginAt;

    /**
     * 创建基础用户信息响应（不包含敏感信息）
     */
    public static UserResponse basic(Long id, String username, String avatarUrl, UserRole role) {
        return UserResponse.builder()
                .id(id)
                .username(username)
                .avatarUrl(avatarUrl)
                .role(role)
                .build();
    }

    /**
     * 创建公开用户信息响应
     */
    public static UserResponse publicInfo(Long id, String username, String avatarUrl, String bio, 
                                        UserRole role, Integer followerCount, Integer followingCount,
                                        LocalDateTime createdAt) {
        return UserResponse.builder()
                .id(id)
                .username(username)
                .avatarUrl(avatarUrl)
                .bio(bio)
                .role(role)
                .followerCount(followerCount)
                .followingCount(followingCount)
                .createdAt(createdAt)
                .build();
    }
}
