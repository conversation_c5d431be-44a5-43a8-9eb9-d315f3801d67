package com.promptstore.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信二维码响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatQRCodeResponse {

    /**
     * 二维码URL
     */
    private String qrCodeUrl;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 过期时间（秒）
     */
    @Builder.Default
    private Long expiresIn = 600L; // 10分钟
}
