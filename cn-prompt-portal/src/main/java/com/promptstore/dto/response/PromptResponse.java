package com.promptstore.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.promptstore.entity.Prompt;
import com.promptstore.enums.PromptStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 提示词响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PromptResponse {

    private Long id;
    private String title;
    private String description;
    private String content;
    private String aiModel;
    private String modelVersion;
    private Long categoryId;
    private String categoryName;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private List<String> previewImages;
    private List<String> exampleOutputs;
    private String usageInstructions;
    private List<String> tags;
    private PromptStatus status;
    private Integer viewCount;
    private Integer downloadCount;
    private Integer likeCount;
    private BigDecimal ratingAvg;
    private Integer ratingCount;
    private Boolean isFeatured;
    private Boolean isFree;
    private String imageUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime publishedAt;

    // 创作者信息
    private UserResponse creator;

    // 是否已购买
    private Boolean isPurchased;

    // 是否已收藏
    private Boolean isLiked;
    private Boolean isFavorited;

    /**
     * 从实体转换为响应DTO
     */
    public static PromptResponse from(Prompt prompt) {
        return PromptResponse.builder()
                .id(prompt.getId())
                .title(prompt.getTitle())
                .description(prompt.getDescription())
                .content(prompt.getContent())
                .aiModel(prompt.getAiModel())
                .modelVersion(prompt.getModelVersion())
                .categoryId(prompt.getCategoryId())
                .price(prompt.getPrice())
                .originalPrice(prompt.getOriginalPrice())
                .status(prompt.getStatus())
                .viewCount(prompt.getViewCount())
                .downloadCount(prompt.getDownloadCount())
                .likeCount(prompt.getLikeCount())
                .ratingAvg(prompt.getRatingAvg())
                .ratingCount(prompt.getRatingCount())
                .isFeatured(prompt.getIsFeatured())
                .isFree(prompt.getIsFree())
                .imageUrl(prompt.getImageUrl())
                .createdAt(prompt.getCreatedAt())
                .updatedAt(prompt.getUpdatedAt())
                .publishedAt(prompt.getPublishedAt())
                .build();
    }

    /**
     * 创建基础信息响应（不包含内容）
     */
    public static PromptResponse basic(Prompt prompt) {
        return PromptResponse.builder()
                .id(prompt.getId())
                .title(prompt.getTitle())
                .description(prompt.getDescription())
                .aiModel(prompt.getAiModel())
                .modelVersion(prompt.getModelVersion())
                .categoryId(prompt.getCategoryId())
                .price(prompt.getPrice())
                .originalPrice(prompt.getOriginalPrice())
                .viewCount(prompt.getViewCount())
                .downloadCount(prompt.getDownloadCount())
                .likeCount(prompt.getLikeCount())
                .ratingAvg(prompt.getRatingAvg())
                .ratingCount(prompt.getRatingCount())
                .isFeatured(prompt.getIsFeatured())
                .isFree(prompt.getIsFree())
                .imageUrl(prompt.getImageUrl())
                .createdAt(prompt.getCreatedAt())
                .publishedAt(prompt.getPublishedAt())
                .build();
    }
}
