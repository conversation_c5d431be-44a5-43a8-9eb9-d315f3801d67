package com.promptstore.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 评价统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReviewStatistics {

    private Long totalCount;           // 总评价数
    private BigDecimal averageRating;  // 平均评分
    private Map<Integer, Long> ratingDistribution; // 各星级分布

    /**
     * 获取格式化的平均评分（保留1位小数）
     */
    public String getFormattedAverageRating() {
        if (averageRating == null) {
            return "0.0";
        }
        return String.format("%.1f", averageRating);
    }

    /**
     * 获取星级百分比
     */
    public double getRatingPercentage(int rating) {
        if (totalCount == 0 || ratingDistribution == null) {
            return 0.0;
        }
        Long count = ratingDistribution.getOrDefault(rating, 0L);
        return (count.doubleValue() / totalCount.doubleValue()) * 100;
    }
}
