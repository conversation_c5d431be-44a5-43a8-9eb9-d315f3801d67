package com.promptstore.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.promptstore.enums.OrderStatus;
import com.promptstore.enums.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderResponse {

    private Long id;
    private String orderNo;
    private Long userId;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private BigDecimal finalAmount;
    private OrderStatus status;
    private PaymentMethod paymentMethod;
    private String paymentId;
    private LocalDateTime paidAt;
    private LocalDateTime cancelledAt;
    private LocalDateTime refundedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // 订单项列表
    private List<OrderItemResponse> items;

    // 用户信息
    private UserResponse user;

    /**
     * 订单项响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrderItemResponse {
        private Long id;
        private Long promptId;
        private String promptTitle;
        private BigDecimal unitPrice;
        private Integer quantity;
        private BigDecimal totalPrice;
        private Long creatorId;
        private String creatorName;
        private BigDecimal commissionRate;
        private BigDecimal creatorEarnings;
        private BigDecimal platformEarnings;
        private LocalDateTime createdAt;

        // 提示词信息
        private PromptResponse prompt;
    }
}
