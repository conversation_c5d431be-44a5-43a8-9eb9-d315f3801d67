package com.promptstore.dto.response;

import com.promptstore.entity.User;
import com.promptstore.enums.UserRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户信息响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileResponse {

    private Long id;
    private String username;
    private String email;
    private String bio;
    private String avatar;
    private String realName;
    private String phone;
    private UserRole role;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // 统计信息
    private Long promptCount;      // 提示词数量
    private Long favoriteCount;    // 收藏数量
    private Long reviewCount;      // 评价数量

    /**
     * 从实体转换为响应DTO
     */
    public static UserProfileResponse from(User user) {
        return UserProfileResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .bio(user.getBio())
                .avatar(user.getAvatar())
                .realName(user.getRealName())
                .phone(user.getPhone())
                .role(user.getRole())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }

    /**
     * 从实体转换为响应DTO（包含统计信息）
     */
    public static UserProfileResponse from(User user, Long promptCount, Long favoriteCount, Long reviewCount) {
        UserProfileResponse response = from(user);
        response.setPromptCount(promptCount);
        response.setFavoriteCount(favoriteCount);
        response.setReviewCount(reviewCount);
        return response;
    }
}
