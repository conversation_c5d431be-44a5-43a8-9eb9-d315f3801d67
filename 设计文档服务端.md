# 中文AI提示词交易平台 - 服务端技术设计文档

## 1. 技术栈选择

### 核心框架
- **Spring Boot 3.2.x** - 核心框架，提供自动配置和快速开发
- **Spring Security 6.x** - 安全认证和授权框架
- **Spring Data JPA** - 数据访问层抽象
- **Spring Web** - RESTful API开发
- **Spring Validation** - 数据验证
- **Spring Cache** - 缓存抽象层
- **Spring Task** - 异步任务处理

### 数据存储
- **MySQL 8.0** - 主数据库，存储业务数据
- **Redis 7.x** - 缓存、会话管理
- **MinIO/阿里云OSS** - 对象存储，图片和文件存储

### 监控和日志
- **Spring Boot Actuator** - 应用监控和健康检查
- **Logback** - 日志框架

### 第三方集成
- **微信开放平台SDK** - 微信扫码登录
- **支付宝SDK** - 支付宝支付集成
- **微信支付SDK** - 微信支付集成
- **阿里云短信服务** - 短信验证码
- **腾讯云内容审核** - 内容安全审核

### 开发工具
- **Maven** - 项目构建和依赖管理
- **MapStruct** - 对象映射
- **Swagger/OpenAPI 3** - API文档生成
- **JUnit 5** - 单元测试
- **Testcontainers** - 集成测试

## 2. 项目结构

```
prompt-store/cn-prompt-portal/
├── src/
│   ├── main/
│   │   ├── java/com/promptstore/
│   │   │   ├── PromptStoreApplication.java    # 启动类
│   │   │   ├── config/                        # 配置类
│   │   │   │   ├── SecurityConfig.java        # 安全配置
│   │   │   │   ├── RedisConfig.java           # Redis配置
│   │   │   │   ├── WechatConfig.java          # 微信配置
│   │   │   │   ├── PaymentConfig.java         # 支付配置
│   │   │   │   ├── CorsConfig.java            # 跨域配置
│   │   │   │   ├── SwaggerConfig.java         # API文档配置
│   │   │   │   └── AsyncConfig.java           # 异步配置
│   │   │   ├── controller/                    # 控制器层
│   │   │   │   ├── AuthController.java        # 认证控制器
│   │   │   │   ├── UserController.java        # 用户管理
│   │   │   │   ├── PromptController.java      # 提示词管理
│   │   │   │   ├── CategoryController.java    # 分类管理
│   │   │   │   ├── OrderController.java       # 订单管理
│   │   │   │   ├── PaymentController.java     # 支付控制器

│   │   │   │   ├── CommentController.java     # 评论管理
│   │   │   │   ├── FileController.java        # 文件上传
│   │   │   │   └── AdminController.java       # 管理员功能
│   │   │   ├── entity/                        # 实体类
│   │   │   │   ├── User.java                  # 用户实体
│   │   │   │   ├── Prompt.java                # 提示词实体
│   │   │   │   ├── Category.java              # 分类实体
│   │   │   │   ├── Order.java                 # 订单实体
│   │   │   │   ├── OrderItem.java             # 订单项实体
│   │   │   │   ├── Payment.java               # 支付实体
│   │   │   │   ├── Comment.java               # 评论实体
│   │   │   │   ├── Rating.java                # 评分实体
│   │   │   │   ├── Follow.java                # 关注关系
│   │   │   │   ├── Collection.java            # 收藏实体
│   │   │   │   ├── Tag.java                   # 标签实体
│   │   │   │   └── AuditLog.java              # 审计日志
│   │   │   ├── repository/                    # 数据访问层
│   │   │   │   ├── UserRepository.java
│   │   │   │   ├── PromptRepository.java
│   │   │   │   ├── CategoryRepository.java
│   │   │   │   ├── OrderRepository.java
│   │   │   │   ├── PaymentRepository.java
│   │   │   │   ├── CommentRepository.java
│   │   │   │   ├── RatingRepository.java
│   │   │   │   ├── FollowRepository.java
│   │   │   │   ├── CollectionRepository.java
│   │   │   │   └── TagRepository.java
│   │   │   ├── service/                       # 业务逻辑层
│   │   │   │   ├── AuthService.java           # 认证服务
│   │   │   │   ├── WechatService.java         # 微信服务
│   │   │   │   ├── UserService.java           # 用户服务
│   │   │   │   ├── PromptService.java         # 提示词服务
│   │   │   │   ├── CategoryService.java       # 分类服务
│   │   │   │   ├── OrderService.java          # 订单服务
│   │   │   │   ├── PaymentService.java        # 支付服务
│   │   │   │   ├── CommentService.java        # 评论服务
│   │   │   │   ├── FileService.java           # 文件服务
│   │   │   │   ├── NotificationService.java   # 通知服务
│   │   │   │   ├── EmailService.java          # 邮件服务
│   │   │   │   ├── SmsService.java            # 短信服务
│   │   │   │   ├── AuditService.java          # 审核服务
│   │   │   │   └── StatisticsService.java     # 统计服务
│   │   │   ├── dto/                           # 数据传输对象
│   │   │   │   ├── request/                   # 请求DTO
│   │   │   │   │   ├── LoginRequest.java
│   │   │   │   │   ├── RegisterRequest.java
│   │   │   │   │   ├── PromptCreateRequest.java
│   │   │   │   │   ├── PromptUpdateRequest.java
│   │   │   │   │   ├── OrderCreateRequest.java
│   │   │   │   │   ├── PaymentRequest.java
│   │   │   │   │   ├── CommentRequest.java
│   │   │   │   │   └── PromptSearchRequest.java
│   │   │   │   ├── response/                  # 响应DTO
│   │   │   │   │   ├── LoginResponse.java
│   │   │   │   │   ├── UserResponse.java
│   │   │   │   │   ├── PromptResponse.java
│   │   │   │   │   ├── PromptDetailResponse.java
│   │   │   │   │   ├── OrderResponse.java
│   │   │   │   │   ├── PaymentResponse.java
│   │   │   │   │   ├── PromptSearchResponse.java
│   │   │   │   │   ├── StatisticsResponse.java
│   │   │   │   │   └── ApiResponse.java
│   │   │   │   └── common/                    # 通用DTO
│   │   │   │       ├── PageRequest.java
│   │   │   │       ├── PageResponse.java
│   │   │   │       └── BaseDTO.java
│   │   │   ├── security/                      # 安全相关
│   │   │   │   ├── JwtAuthenticationFilter.java
│   │   │   │   ├── JwtTokenProvider.java
│   │   │   │   ├── UserDetailsImpl.java
│   │   │   │   ├── UserDetailsServiceImpl.java
│   │   │   │   ├── AuthenticationEntryPoint.java
│   │   │   │   └── AccessDeniedHandler.java
│   │   │   ├── exception/                     # 异常处理
│   │   │   │   ├── GlobalExceptionHandler.java
│   │   │   │   ├── BusinessException.java
│   │   │   │   ├── ResourceNotFoundException.java
│   │   │   │   ├── UnauthorizedException.java
│   │   │   │   └── ValidationException.java
│   │   │   ├── util/                          # 工具类
│   │   │   │   ├── JwtUtil.java               # JWT工具
│   │   │   │   ├── PasswordUtil.java          # 密码工具
│   │   │   │   ├── FileUtil.java              # 文件工具
│   │   │   │   ├── DateUtil.java              # 日期工具
│   │   │   │   ├── ValidationUtil.java        # 验证工具
│   │   │   │   ├── RedisUtil.java             # Redis工具
│   │   │   │   └── HttpUtil.java              # HTTP工具
│   │   │   ├── constant/                      # 常量定义
│   │   │   │   ├── ApiConstants.java          # API常量
│   │   │   │   ├── CacheConstants.java        # 缓存常量
│   │   │   │   ├── SecurityConstants.java     # 安全常量
│   │   │   │   └── BusinessConstants.java     # 业务常量
│   │   │   ├── enums/                         # 枚举类
│   │   │   │   ├── UserRole.java              # 用户角色
│   │   │   │   ├── PromptStatus.java          # 提示词状态
│   │   │   │   ├── OrderStatus.java           # 订单状态
│   │   │   │   ├── PaymentStatus.java         # 支付状态
│   │   │   │   ├── PaymentMethod.java         # 支付方式
│   │   │   │   └── AuditStatus.java           # 审核状态
│   │   │   ├── aspect/                        # 切面编程
│   │   │   │   ├── LoggingAspect.java         # 日志切面
│   │   │   │   ├── CacheAspect.java           # 缓存切面
│   │   │   │   ├── RateLimitAspect.java       # 限流切面
│   │   │   │   └── AuditAspect.java           # 审计切面
│   │   │   └── task/                          # 定时任务
│   │   │       ├── StatisticsTask.java        # 统计任务
│   │   │       ├── CleanupTask.java           # 清理任务
│   │   │       └── BackupTask.java            # 备份任务
│   │   └── resources/
│   │       ├── application.yml                # 主配置文件
│   │       ├── application-dev.yml            # 开发环境配置
│   │       ├── application-test.yml           # 测试环境配置
│   │       ├── application-prod.yml           # 生产环境配置
│   │       ├── logback-spring.xml             # 日志配置
│   │       ├── db/migration/                  # 数据库迁移脚本
│   │       │   ├── V1__Create_user_table.sql
│   │       │   ├── V2__Create_prompt_table.sql
│   │       │   ├── V3__Create_order_table.sql
│   │       │   └── ...
│   │       ├── static/                        # 静态资源
│   │       └── templates/                     # 模板文件
│   │           ├── email/                     # 邮件模板
│   │           └── sms/                       # 短信模板
│   └── test/
│       ├── java/com/promptstore/
│       │   ├── controller/                    # 控制器测试
│       │   ├── service/                       # 服务测试
│       │   ├── repository/                    # 数据访问测试
│       │   ├── integration/                   # 集成测试
│       │   └── util/                          # 工具类测试
│       └── resources/
│           ├── application-test.yml           # 测试配置
│           └── test-data.sql                  # 测试数据
├── docker/                                    # Docker配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.prod.yml
├── docs/                                      # 文档
│   ├── api.md                                 # API文档
│   ├── database.md                            # 数据库设计
│   ├── deployment.md                          # 部署文档
│   └── architecture.md                        # 架构文档
├── scripts/                                   # 脚本文件
│   ├── build.sh                               # 构建脚本
│   ├── deploy.sh                              # 部署脚本
│   └── backup.sh                              # 备份脚本
├── pom.xml                                    # Maven配置
├── .gitignore
└── README.md
```

## 3. 数据库设计

### 3.1 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255),
    wechat_open_id VARCHAR(100) UNIQUE,
    wechat_union_id VARCHAR(100),
    avatar_url VARCHAR(500),
    bio TEXT,
    role ENUM('USER', 'CREATOR', 'ADMIN') DEFAULT 'USER',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    follower_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_wechat_open_id (wechat_open_id),
    INDEX idx_wechat_union_id (wechat_union_id),
    INDEX idx_role (role),
    INDEX idx_status (status)
);
```

#### 提示词表 (prompts)
```sql
CREATE TABLE prompts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    ai_model VARCHAR(50) NOT NULL,
    category_id BIGINT,
    price DECIMAL(8,2) NOT NULL,
    original_price DECIMAL(8,2),
    preview_images JSON,
    example_outputs JSON,
    usage_instructions TEXT,
    tags JSON,
    status ENUM('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED') DEFAULT 'DRAFT',
    view_count INT DEFAULT 0,
    download_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    rating_avg DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    INDEX idx_user_id (user_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_ai_model (ai_model),
    INDEX idx_price (price),
    INDEX idx_created_at (created_at),
    INDEX idx_rating_avg (rating_avg),
    FULLTEXT idx_search (title, description, content)
);
```

#### 分类表 (categories)
```sql
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id BIGINT,
    icon_url VARCHAR(500),
    sort_order INT DEFAULT 0,
    prompt_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_sort_order (sort_order)
);
```

#### 订单表 (orders)
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    status ENUM('PENDING', 'PAID', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING',
    payment_method ENUM('ALIPAY', 'WECHAT', 'BANK_CARD') NOT NULL,
    payment_id VARCHAR(100),
    paid_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    refunded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### 订单项表 (order_items)
```sql
CREATE TABLE order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    prompt_id BIGINT NOT NULL,
    prompt_title VARCHAR(200) NOT NULL,
    unit_price DECIMAL(8,2) NOT NULL,
    quantity INT DEFAULT 1,
    total_price DECIMAL(8,2) NOT NULL,
    creator_id BIGINT NOT NULL,
    commission_rate DECIMAL(5,4) DEFAULT 0.7000,
    creator_earnings DECIMAL(8,2) NOT NULL,
    platform_earnings DECIMAL(8,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (prompt_id) REFERENCES prompts(id),
    FOREIGN KEY (creator_id) REFERENCES users(id),
    INDEX idx_order_id (order_id),
    INDEX idx_prompt_id (prompt_id),
    INDEX idx_creator_id (creator_id)
);
```

### 3.2 辅助表结构

#### 评论表 (comments)
```sql
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    prompt_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    parent_id BIGINT,
    content TEXT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    like_count INT DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prompt_id) REFERENCES prompts(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES comments(id),
    INDEX idx_prompt_id (prompt_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at)
);
```

#### 收藏表 (collections)
```sql
CREATE TABLE collections (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    prompt_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (prompt_id) REFERENCES prompts(id),
    UNIQUE KEY uk_user_prompt (user_id, prompt_id),
    INDEX idx_user_id (user_id),
    INDEX idx_prompt_id (prompt_id)
);
```

#### 关注表 (follows)
```sql
CREATE TABLE follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL,
    following_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id),
    FOREIGN KEY (following_id) REFERENCES users(id),
    UNIQUE KEY uk_follower_following (follower_id, following_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_following_id (following_id)
);
```

## 4. API设计

### 4.1 认证相关API

#### 微信扫码登录
```java
@PostMapping("/api/auth/wechat/login")
public ResponseEntity<ApiResponse<LoginResponse>> wechatLogin(
    @RequestParam String code) {
    // 实现微信扫码登录逻辑
    LoginResponse response = authService.wechatLogin(code);
    return ResponseEntity.ok(ApiResponse.success(response));
}
```

#### 获取微信登录二维码
```java
@GetMapping("/api/auth/wechat/qrcode")
public ResponseEntity<ApiResponse<WechatQRCodeResponse>> getWechatQRCode() {
    // 生成微信登录二维码URL
    String qrCodeUrl = String.format(
        "https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect",
        wechatAppId,
        URLEncoder.encode(redirectUri, "UTF-8"),
        generateState()
    );

    WechatQRCodeResponse response = WechatQRCodeResponse.builder()
        .qrCodeUrl(qrCodeUrl)
        .state(state)
        .build();

    return ResponseEntity.ok(ApiResponse.success(response));
}
```

#### 刷新Token
```java
@PostMapping("/api/auth/refresh")
public ResponseEntity<ApiResponse<TokenResponse>> refreshToken(
    @RequestHeader("Authorization") String refreshToken) {
    // 实现Token刷新逻辑
}
```

### 4.2 提示词相关API

#### 获取提示词列表
```java
@GetMapping("/api/prompts")
public ResponseEntity<ApiResponse<PageResponse<PromptResponse>>> getPrompts(
    @RequestParam(defaultValue = "1") int page,
    @RequestParam(defaultValue = "20") int size,
    @RequestParam(required = false) String category,
    @RequestParam(required = false) String aiModel,
    @RequestParam(required = false) String sortBy,
    @RequestParam(required = false) BigDecimal minPrice,
    @RequestParam(required = false) BigDecimal maxPrice) {
    // 实现提示词列表查询
}
```

#### 获取提示词详情
```java
@GetMapping("/api/prompts/{id}")
public ResponseEntity<ApiResponse<PromptDetailResponse>> getPromptDetail(
    @PathVariable Long id) {
    // 实现提示词详情查询
}
```

#### 创建提示词
```java
@PostMapping("/api/prompts")
@PreAuthorize("hasRole('CREATOR')")
public ResponseEntity<ApiResponse<PromptResponse>> createPrompt(
    @Valid @RequestBody PromptCreateRequest request,
    Authentication authentication) {
    // 实现提示词创建
}
```

### 4.3 订单相关API

#### 创建订单
```java
@PostMapping("/api/orders")
@PreAuthorize("hasRole('USER')")
public ResponseEntity<ApiResponse<OrderResponse>> createOrder(
    @Valid @RequestBody OrderCreateRequest request,
    Authentication authentication) {
    // 实现订单创建
}
```

#### 支付订单
```java
@PostMapping("/api/orders/{orderId}/pay")
@PreAuthorize("hasRole('USER')")
public ResponseEntity<ApiResponse<PaymentResponse>> payOrder(
    @PathVariable Long orderId,
    @Valid @RequestBody PaymentRequest request,
    Authentication authentication) {
    // 实现订单支付
}
```

## 5. 业务逻辑设计

### 5.1 用户认证服务
```java
@Service
@Transactional
public class AuthService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WechatService wechatService;

    public LoginResponse wechatLogin(String code) {
        // 1. 通过code获取微信用户信息
        WechatUserInfo wechatUserInfo = wechatService.getUserInfo(code);

        // 2. 查找或创建用户
        User user = userRepository.findByWechatOpenId(wechatUserInfo.getOpenId())
            .orElseGet(() -> createUserFromWechat(wechatUserInfo));

        // 3. 生成JWT Token
        String accessToken = tokenProvider.generateAccessToken(user);
        String refreshToken = tokenProvider.generateRefreshToken(user);

        // 4. 存储刷新Token到Redis
        redisTemplate.opsForValue().set(
            "refresh_token:" + user.getId(),
            refreshToken,
            Duration.ofDays(30)
        );

        // 5. 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);

        return LoginResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .user(UserResponse.from(user))
            .build();
    }

    private User createUserFromWechat(WechatUserInfo wechatUserInfo) {
        User user = User.builder()
            .username(generateUsername(wechatUserInfo.getNickname()))
            .wechatOpenId(wechatUserInfo.getOpenId())
            .wechatUnionId(wechatUserInfo.getUnionId())
            .avatarUrl(wechatUserInfo.getAvatarUrl())
            .role(UserRole.USER)
            .status(UserStatus.ACTIVE)
            .build();

        return userRepository.save(user);
    }

    private String generateUsername(String nickname) {
        // 生成唯一用户名逻辑
        String baseUsername = nickname.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
        if (baseUsername.length() > 20) {
            baseUsername = baseUsername.substring(0, 20);
        }

        String username = baseUsername;
        int suffix = 1;
        while (userRepository.existsByUsername(username)) {
            username = baseUsername + suffix++;
        }

        return username;
    }

    public void logout(Long userId) {
        // 删除Redis中的刷新Token
        redisTemplate.delete("refresh_token:" + userId);

        // 将访问Token加入黑名单（可选）
        // ...
    }
}
```

### 5.2 提示词服务
```java
@Service
@Transactional
public class PromptService {

    @Autowired
    private PromptRepository promptRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private AuditService auditService;

    @Cacheable(value = "prompts", key = "#id")
    public PromptDetailResponse getPromptDetail(Long id) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));

        // 增加浏览次数
        promptRepository.incrementViewCount(id);

        return PromptDetailResponse.from(prompt);
    }

    public PromptResponse createPrompt(PromptCreateRequest request, Long userId) {
        // 1. 验证用户权限
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        if (!user.getRole().equals(UserRole.CREATOR)) {
            throw new UnauthorizedException("只有创作者可以发布提示词");
        }

        // 2. 创建提示词实体
        Prompt prompt = Prompt.builder()
            .userId(userId)
            .title(request.getTitle())
            .description(request.getDescription())
            .content(request.getContent())
            .aiModel(request.getAiModel())
            .categoryId(request.getCategoryId())
            .price(request.getPrice())
            .tags(request.getTags())
            .status(PromptStatus.PENDING)
            .build();

        // 3. 处理预览图片
        if (request.getPreviewImages() != null) {
            List<String> imageUrls = fileService.uploadImages(request.getPreviewImages());
            prompt.setPreviewImages(imageUrls);
        }

        // 4. 保存到数据库
        prompt = promptRepository.save(prompt);

        // 5. 提交审核
        auditService.submitForReview(prompt.getId());

        return PromptResponse.from(prompt);
    }

    @CacheEvict(value = "prompts", key = "#id")
    public void deletePrompt(Long id, Long userId) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("提示词不存在"));

        if (!prompt.getUserId().equals(userId)) {
            throw new UnauthorizedException("无权删除此提示词");
        }

        prompt.setStatus(PromptStatus.DELETED);
        promptRepository.save(prompt);
    }

    public PageResponse<PromptResponse> searchPrompts(PromptSearchRequest request) {
        // 构建查询条件
        Specification<Prompt> spec = Specification.where(null);

        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            spec = spec.and((root, query, cb) ->
                cb.or(
                    cb.like(root.get("title"), "%" + request.getKeyword() + "%"),
                    cb.like(root.get("description"), "%" + request.getKeyword() + "%")
                )
            );
        }

        // 分类过滤
        if (request.getCategoryId() != null) {
            spec = spec.and((root, query, cb) ->
                cb.equal(root.get("categoryId"), request.getCategoryId())
            );
        }

        // AI模型过滤
        if (StringUtils.hasText(request.getAiModel())) {
            spec = spec.and((root, query, cb) ->
                cb.equal(root.get("aiModel"), request.getAiModel())
            );
        }

        // 价格范围过滤
        if (request.getMinPrice() != null) {
            spec = spec.and((root, query, cb) ->
                cb.greaterThanOrEqualTo(root.get("price"), request.getMinPrice())
            );
        }
        if (request.getMaxPrice() != null) {
            spec = spec.and((root, query, cb) ->
                cb.lessThanOrEqualTo(root.get("price"), request.getMaxPrice())
            );
        }

        // 只查询已发布的提示词
        spec = spec.and((root, query, cb) ->
            cb.equal(root.get("status"), PromptStatus.APPROVED)
        );

        // 排序
        Sort sort = buildSort(request.getSortBy());

        // 分页查询
        Pageable pageable = PageRequest.of(
            request.getPage() - 1,
            request.getSize(),
            sort
        );

        Page<Prompt> promptPage = promptRepository.findAll(spec, pageable);

        List<PromptResponse> prompts = promptPage.getContent().stream()
            .map(PromptResponse::from)
            .collect(Collectors.toList());

        return PageResponse.<PromptResponse>builder()
            .data(prompts)
            .total(promptPage.getTotalElements())
            .page(request.getPage())
            .size(request.getSize())
            .totalPages(promptPage.getTotalPages())
            .build();
    }

    private Sort buildSort(String sortBy) {
        switch (sortBy) {
            case "price_asc":
                return Sort.by(Sort.Direction.ASC, "price");
            case "price_desc":
                return Sort.by(Sort.Direction.DESC, "price");
            case "rating":
                return Sort.by(Sort.Direction.DESC, "ratingAvg");
            case "popular":
                return Sort.by(Sort.Direction.DESC, "downloadCount");
            case "newest":
            default:
                return Sort.by(Sort.Direction.DESC, "createdAt");
        }
    }
}
```

### 5.3 订单服务
```java
@Service
@Transactional
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private PromptRepository promptRepository;

    @Autowired
    private PaymentService paymentService;



    public OrderResponse createOrder(OrderCreateRequest request, Long userId) {
        // 1. 验证提示词是否存在且可购买
        List<Prompt> prompts = promptRepository.findAllById(request.getPromptIds());
        if (prompts.size() != request.getPromptIds().size()) {
            throw new BusinessException("部分提示词不存在");
        }

        // 2. 检查是否已购买
        List<Long> purchasedPromptIds = orderRepository
            .findPurchasedPromptIds(userId, request.getPromptIds());
        if (!purchasedPromptIds.isEmpty()) {
            throw new BusinessException("部分提示词已购买");
        }

        // 3. 计算订单金额
        BigDecimal totalAmount = prompts.stream()
            .map(Prompt::getPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 4. 创建订单
        Order order = Order.builder()
            .orderNo(generateOrderNo())
            .userId(userId)
            .totalAmount(totalAmount)
            .finalAmount(totalAmount)
            .status(OrderStatus.PENDING)
            .paymentMethod(request.getPaymentMethod())
            .build();

        order = orderRepository.save(order);

        // 5. 创建订单项
        List<OrderItem> orderItems = prompts.stream()
            .map(prompt -> OrderItem.builder()
                .orderId(order.getId())
                .promptId(prompt.getId())
                .promptTitle(prompt.getTitle())
                .unitPrice(prompt.getPrice())
                .quantity(1)
                .totalPrice(prompt.getPrice())
                .creatorId(prompt.getUserId())
                .commissionRate(new BigDecimal("0.7"))
                .creatorEarnings(prompt.getPrice().multiply(new BigDecimal("0.7")))
                .platformEarnings(prompt.getPrice().multiply(new BigDecimal("0.3")))
                .build())
            .collect(Collectors.toList());

        orderItemRepository.saveAll(orderItems);

        return OrderResponse.from(order, orderItems);
    }

    public PaymentResponse payOrder(Long orderId, PaymentRequest request, Long userId) {
        // 1. 验证订单
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        if (!order.getUserId().equals(userId)) {
            throw new UnauthorizedException("无权支付此订单");
        }

        if (!order.getStatus().equals(OrderStatus.PENDING)) {
            throw new BusinessException("订单状态不正确");
        }

        // 2. 调用支付服务
        PaymentResponse paymentResponse = paymentService.createPayment(order, request);

        // 3. 更新订单支付信息
        order.setPaymentId(paymentResponse.getPaymentId());
        orderRepository.save(order);

        return paymentResponse;
    }

    @EventListener
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        // 1. 更新订单状态
        Order order = orderRepository.findById(event.getOrderId())
            .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

        order.setStatus(OrderStatus.PAID);
        order.setPaidAt(LocalDateTime.now());
        orderRepository.save(order);

        // 2. 更新提示词下载次数
        List<OrderItem> orderItems = orderItemRepository.findByOrderId(order.getId());
        orderItems.forEach(item -> {
            promptRepository.incrementDownloadCount(item.getPromptId());
        });

        // 3. 更新用户统计
        userRepository.incrementTotalSpent(order.getUserId(), order.getFinalAmount());

        // 4. 更新创作者收益
        Map<Long, BigDecimal> creatorEarnings = orderItems.stream()
            .collect(Collectors.groupingBy(
                OrderItem::getCreatorId,
                Collectors.mapping(OrderItem::getCreatorEarnings,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
            ));

        creatorEarnings.forEach((creatorId, earnings) -> {
            userRepository.incrementTotalEarnings(creatorId, earnings);
        });

        // 5. 发送通知（可以通过异步任务或直接调用）
        notificationService.sendOrderPaidNotification(order);
    }

    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() +
               String.format("%04d", new Random().nextInt(10000));
    }
}
```

### 5.4 支付服务
```java
@Service
public class PaymentService {

    @Autowired
    private AlipayClient alipayClient;

    @Autowired
    private WechatPayClient wechatPayClient;

    @Value("${app.payment.notify-url}")
    private String notifyUrl;

    @Value("${app.payment.return-url}")
    private String returnUrl;

    public PaymentResponse createPayment(Order order, PaymentRequest request) {
        switch (request.getPaymentMethod()) {
            case ALIPAY:
                return createAlipayPayment(order, request);
            case WECHAT:
                return createWechatPayment(order, request);
            default:
                throw new BusinessException("不支持的支付方式");
        }
    }

    private PaymentResponse createAlipayPayment(Order order, PaymentRequest request) {
        try {
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            alipayRequest.setNotifyUrl(notifyUrl + "/alipay");
            alipayRequest.setReturnUrl(returnUrl);

            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(order.getOrderNo());
            model.setTotalAmount(order.getFinalAmount().toString());
            model.setSubject("AI提示词购买");
            model.setBody("订单号：" + order.getOrderNo());
            model.setProductCode("FAST_INSTANT_TRADE_PAY");

            alipayRequest.setBizModel(model);

            AlipayTradePagePayResponse response = alipayClient.pageExecute(alipayRequest);

            return PaymentResponse.builder()
                .paymentId(order.getOrderNo())
                .paymentUrl(response.getBody())
                .qrCode(null)
                .build();

        } catch (AlipayApiException e) {
            throw new BusinessException("支付宝支付创建失败：" + e.getMessage());
        }
    }

    private PaymentResponse createWechatPayment(Order order, PaymentRequest request) {
        // 微信支付实现
        // ...
        return null;
    }

    @PostMapping("/webhook/alipay")
    public String handleAlipayNotify(HttpServletRequest request) {
        try {
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();

            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = String.join(",", values);
                params.put(name, valueStr);
            }

            boolean signVerified = AlipaySignature.rsaCheckV1(
                params,
                alipayPublicKey,
                "UTF-8",
                "RSA2"
            );

            if (signVerified) {
                String tradeStatus = params.get("trade_status");
                String outTradeNo = params.get("out_trade_no");

                if ("TRADE_SUCCESS".equals(tradeStatus) ||
                    "TRADE_FINISHED".equals(tradeStatus)) {

                    // 发布支付成功事件
                    Order order = orderRepository.findByOrderNo(outTradeNo)
                        .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));

                    applicationEventPublisher.publishEvent(
                        PaymentSuccessEvent.builder()
                            .orderId(order.getId())
                            .paymentId(params.get("trade_no"))
                            .amount(new BigDecimal(params.get("total_amount")))
                            .build()
                    );
                }

                return "success";
            } else {
                return "failure";
            }

        } catch (Exception e) {
            log.error("支付宝回调处理失败", e);
            return "failure";
        }
    }
}
```

## 6. 微信登录服务设计

### 6.1 微信配置
```java
@Configuration
public class WechatConfig {

    @Value("${app.wechat.app-id}")
    private String appId;

    @Value("${app.wechat.app-secret}")
    private String appSecret;

    @Bean
    public WechatService wechatService() {
        return new WechatService(appId, appSecret);
    }
}
```

### 6.2 微信用户信息DTO
```java
@Data
@Builder
public class WechatUserInfo {
    private String openId;
    private String unionId;
    private String nickname;
    private String avatarUrl;
    private Integer sex;
    private String country;
    private String province;
    private String city;
}

@Data
@Builder
public class WechatAccessToken {
    private String accessToken;
    private Integer expiresIn;
    private String refreshToken;
    private String openId;
    private String scope;
}
```

### 6.3 微信服务实现
```java
@Service
@Slf4j
public class WechatService {

    private final String appId;
    private final String appSecret;
    private final RestTemplate restTemplate;

    public WechatService(String appId, String appSecret) {
        this.appId = appId;
        this.appSecret = appSecret;
        this.restTemplate = new RestTemplate();
    }

    public WechatUserInfo getUserInfo(String code) {
        // 1. 通过code获取access_token
        WechatAccessToken accessToken = getAccessToken(code);

        // 2. 通过access_token获取用户信息
        return getUserInfoByToken(accessToken.getAccessToken(), accessToken.getOpenId());
    }

    private WechatAccessToken getAccessToken(String code) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
            appId, appSecret, code
        );

        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response.getBody());

            if (jsonNode.has("errcode")) {
                throw new BusinessException("获取微信access_token失败：" + jsonNode.get("errmsg").asText());
            }

            return WechatAccessToken.builder()
                .accessToken(jsonNode.get("access_token").asText())
                .expiresIn(jsonNode.get("expires_in").asInt())
                .refreshToken(jsonNode.get("refresh_token").asText())
                .openId(jsonNode.get("openid").asText())
                .scope(jsonNode.get("scope").asText())
                .build();

        } catch (Exception e) {
            log.error("获取微信access_token失败", e);
            throw new BusinessException("微信登录失败");
        }
    }

    private WechatUserInfo getUserInfoByToken(String accessToken, String openId) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
            accessToken, openId
        );

        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response.getBody());

            if (jsonNode.has("errcode")) {
                throw new BusinessException("获取微信用户信息失败：" + jsonNode.get("errmsg").asText());
            }

            return WechatUserInfo.builder()
                .openId(jsonNode.get("openid").asText())
                .unionId(jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : null)
                .nickname(jsonNode.get("nickname").asText())
                .avatarUrl(jsonNode.get("headimgurl").asText())
                .sex(jsonNode.get("sex").asInt())
                .country(jsonNode.get("country").asText())
                .province(jsonNode.get("province").asText())
                .city(jsonNode.get("city").asText())
                .build();

        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            throw new BusinessException("获取用户信息失败");
        }
    }
}
```

## 7. 缓存策略

### 7.1 Redis配置
```java
@Configuration
@EnableCaching
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer =
            new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LazyLoadingAspect.validateAndGetBasicType(),
            ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new Jackson2JsonRedisSerializer<>(Object.class)));

        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}
```

### 7.2 缓存策略定义
```java
public class CacheConstants {
    // 提示词相关缓存
    public static final String PROMPT_DETAIL = "prompt:detail";
    public static final String PROMPT_LIST = "prompt:list";
    public static final String PROMPT_SEARCH = "prompt:search";

    // 用户相关缓存
    public static final String USER_PROFILE = "user:profile";
    public static final String USER_PROMPTS = "user:prompts";

    // 分类相关缓存
    public static final String CATEGORIES = "categories";
    public static final String CATEGORY_PROMPTS = "category:prompts";

    // 统计相关缓存
    public static final String STATISTICS = "statistics";
    public static final String HOT_PROMPTS = "hot:prompts";

    // 缓存过期时间（秒）
    public static final int PROMPT_CACHE_TTL = 1800; // 30分钟
    public static final int USER_CACHE_TTL = 3600;   // 1小时
    public static final int CATEGORY_CACHE_TTL = 7200; // 2小时
    public static final int STATISTICS_CACHE_TTL = 300; // 5分钟
}
```

## 8. 安全设计

### 8.1 JWT Token配置
```java
@Component
public class JwtTokenProvider {

    @Value("${app.jwt.secret}")
    private String jwtSecret;

    @Value("${app.jwt.access-token-expiration}")
    private int accessTokenExpiration;

    @Value("${app.jwt.refresh-token-expiration}")
    private int refreshTokenExpiration;

    public String generateAccessToken(User user) {
        Date expiryDate = new Date(System.currentTimeMillis() + accessTokenExpiration * 1000L);

        return Jwts.builder()
            .setSubject(user.getId().toString())
            .setIssuedAt(new Date())
            .setExpiration(expiryDate)
            .claim("username", user.getUsername())
            .claim("role", user.getRole().name())
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }

    public String generateRefreshToken(User user) {
        Date expiryDate = new Date(System.currentTimeMillis() + refreshTokenExpiration * 1000L);

        return Jwts.builder()
            .setSubject(user.getId().toString())
            .setIssuedAt(new Date())
            .setExpiration(expiryDate)
            .claim("type", "refresh")
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }

    public Long getUserIdFromToken(String token) {
        Claims claims = Jwts.parser()
            .setSigningKey(jwtSecret)
            .parseClaimsJws(token)
            .getBody();

        return Long.parseLong(claims.getSubject());
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
}
```

### 8.2 安全配置
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtAccessDeniedHandler jwtAccessDeniedHandler;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .exceptionHandling()
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .accessDeniedHandler(jwtAccessDeniedHandler)
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/prompts/search").permitAll()
                .requestMatchers("/api/prompts/{id}").permitAll()
                .requestMatchers("/api/categories").permitAll()
                .requestMatchers("/webhook/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()

                // 需要认证的接口
                .requestMatchers("/api/prompts").hasRole("CREATOR")
                .requestMatchers("/api/orders/**").hasRole("USER")
                .requestMatchers("/api/admin/**").hasRole("ADMIN")

                // 其他接口需要认证
                .anyRequest().authenticated()
            );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### 8.3 限流配置
```java
@Component
@Aspect
public class RateLimitAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint point, RateLimit rateLimit) throws Throwable {
        String key = generateKey(point, rateLimit);
        String luaScript = buildLuaScript();

        Long count = (Long) redisTemplate.execute(
            new DefaultRedisScript<>(luaScript, Long.class),
            Collections.singletonList(key),
            rateLimit.count(),
            rateLimit.time()
        );

        if (count != null && count <= rateLimit.count()) {
            return point.proceed();
        } else {
            throw new BusinessException("请求过于频繁，请稍后再试");
        }
    }

    private String generateKey(ProceedingJoinPoint point, RateLimit rateLimit) {
        StringBuilder key = new StringBuilder("rate_limit:");

        if (rateLimit.key().isEmpty()) {
            key.append(point.getSignature().toShortString());
        } else {
            key.append(rateLimit.key());
        }

        // 添加用户ID或IP地址
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated()) {
            key.append(":").append(auth.getName());
        } else {
            // 获取IP地址
            HttpServletRequest request = ((ServletRequestAttributes)
                RequestContextHolder.currentRequestAttributes()).getRequest();
            key.append(":").append(getClientIpAddress(request));
        }

        return key.toString();
    }

    private String buildLuaScript() {
        return """
            local key = KEYS[1]
            local count = tonumber(ARGV[1])
            local time = tonumber(ARGV[2])
            local current = redis.call('get', key)
            if current == false then
                redis.call('set', key, 1)
                redis.call('expire', key, time)
                return 1
            else
                local num = tonumber(current)
                if num < count then
                    redis.call('incr', key)
                    return num + 1
                else
                    return count + 1
                end
            end
            """;
    }
}
```

## 9. 监控和日志

### 9.1 应用监控配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 9.2 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 10. 部署配置

### 10.1 Docker配置
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/prompt-store-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 10.2 Docker Compose配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=a123456789A
      - MYSQL_DATABASE=cn-prompt
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 10.3 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: ************************************************************************************************************
    username: root
    password: a123456789A
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: 127.0.0.1
    port: 6379
    # 无密码
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey123456789}
    access-token-expiration: 3600
    refresh-token-expiration: 2592000

  wechat:
    app-id: ${WECHAT_APP_ID}
    app-secret: ${WECHAT_APP_SECRET}

  payment:
    alipay:
      app-id: ${ALIPAY_APP_ID}
      private-key: ${ALIPAY_PRIVATE_KEY}
      public-key: ${ALIPAY_PUBLIC_KEY}
    wechat:
      app-id: ${WECHAT_PAY_APP_ID}
      mch-id: ${WECHAT_MCH_ID}
      api-key: ${WECHAT_API_KEY}

  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: 10MB

logging:
  level:
    com.promptstore: INFO
    org.springframework.security: DEBUG
  file:
    name: logs/application.log
```

这份技术设计文档涵盖了中文AI提示词交易平台的完整服务端架构，包括：

1. **完整的技术栈选择**：Spring Boot生态系统
2. **详细的项目结构**：清晰的分层架构
3. **数据库设计**：完整的表结构设计，支持微信登录
4. **API设计**：RESTful API规范
5. **核心业务逻辑**：微信认证、提示词管理、订单处理
6. **搜索功能**：基于MySQL的模糊查询搜索
7. **缓存策略**：Redis缓存优化
8. **安全设计**：JWT认证、权限控制、限流
9. **微信集成**：微信扫码登录功能
10. **部署配置**：Docker容器化部署

## 技术架构特点

### 简化设计
- **去除Elasticsearch**：使用MySQL模糊查询实现搜索功能
- **去除消息队列**：简化异步处理，直接调用服务
- **去除ELK**：使用简单的文件日志
- **微信登录**：只支持微信扫码登录，简化认证流程

### 数据库配置
- **MySQL地址**：localhost:3306/cn-prompt
- **账号**：root
- **密码**：a123456789A
- **Redis地址**：127.0.0.1:6379（无密码）

### 核心功能
- **微信扫码登录**：集成微信开放平台
- **提示词交易**：完整的买卖流程
- **支付集成**：支付宝、微信支付
- **内容管理**：审核、分类、统计

这个简化版设计更适合初期快速开发和部署，后续可以根据业务需求逐步扩展功能。
```
```