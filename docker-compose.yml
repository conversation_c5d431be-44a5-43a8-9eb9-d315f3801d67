version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: cn-prompt-mysql
    environment:
      MYSQL_ROOT_PASSWORD: a123456789A
      MYSQL_DATABASE: cn-prompt
      MY<PERSON>QL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    networks:
      - prompt-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: cn-prompt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - prompt-network

  # 后端服务
  backend:
    build:
      context: ./cn-prompt-portal
      dockerfile: Dockerfile
    container_name: cn-prompt-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ********************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: a123456789A
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - prompt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./cn-prompt-frontend
      dockerfile: Dockerfile
    container_name: cn-prompt-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8080/api
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - prompt-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  prompt-network:
    driver: bridge
