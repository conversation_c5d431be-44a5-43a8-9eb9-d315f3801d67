# 中文AI提示词交易平台

一个专业的中文AI提示词交易平台，提供高质量的AI提示词买卖服务。

## 项目结构

```
prompt-store/
├── cn-prompt-portal/          # 后端服务 (Spring Boot)
│   ├── src/main/java/com/promptstore/
│   │   ├── PromptStoreApplication.java
│   │   ├── config/            # 配置类
│   │   ├── controller/        # 控制器
│   │   ├── entity/           # 实体类
│   │   ├── repository/       # 数据访问层
│   │   ├── service/          # 业务逻辑层
│   │   ├── dto/              # 数据传输对象
│   │   ├── security/         # 安全相关
│   │   ├── exception/        # 异常处理
│   │   └── enums/            # 枚举类
│   ├── src/main/resources/
│   │   ├── application.yml   # 配置文件
│   │   └── db/migration/     # 数据库迁移脚本
│   └── pom.xml               # Maven配置
├── cn-prompt-frontend/        # 前端应用 (Next.js)
│   ├── src/
│   │   ├── app/              # Next.js App Router
│   │   ├── components/       # React组件
│   │   ├── lib/              # 工具函数
│   │   ├── hooks/            # 自定义Hook
│   │   ├── store/            # 状态管理
│   │   ├── types/            # TypeScript类型
│   │   └── styles/           # 样式文件
│   ├── package.json          # 依赖配置
│   ├── next.config.js        # Next.js配置
│   ├── tailwind.config.js    # Tailwind配置
│   └── tsconfig.json         # TypeScript配置
├── 设计文档前端.md            # 前端技术设计文档
├── 设计文档服务端.md          # 服务端技术设计文档
└── README.md                 # 项目说明
```

## 技术栈

### 后端
- **Spring Boot 3.2.x** - 核心框架
- **Spring Security 6.x** - 安全认证
- **Spring Data JPA** - 数据访问
- **MySQL 8.0** - 数据库
- **Redis 7.x** - 缓存
- **JWT** - 令牌认证
- **Maven** - 项目构建

### 前端
- **Next.js 14** - React框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Shadcn/ui** - 组件库
- **Zustand** - 状态管理
- **React Query** - 数据获取
- **React Hook Form** - 表单处理

## 核心功能

### 用户功能
- 微信扫码登录
- 浏览和搜索提示词
- 购买和下载提示词
- 收藏和评价
- 个人中心管理

### 创作者功能
- 发布和管理提示词
- 销售数据统计
- 收益管理
- 创作者认证

### 管理功能
- 内容审核
- 用户管理
- 数据统计
- 系统配置

## 快速开始

### 环境要求
- Java 17+
- Node.js 18+
- MySQL 8.0+
- Redis 7.x+

### 后端启动

1. 配置数据库
```sql
CREATE DATABASE `cn-prompt` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件
```yaml
# cn-prompt-portal/src/main/resources/application.yml
spring:
  datasource:
    url: *************************************
    username: root
    password: a123456789A
  redis:
    host: 127.0.0.1
    port: 6379
```

3. 启动应用
```bash
cd cn-prompt-portal
mvn spring-boot:run
```

### 前端启动

1. 安装依赖
```bash
cd cn-prompt-frontend
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

3. 访问应用
打开浏览器访问 http://localhost:3000

## API文档

后端启动后，可以访问以下地址查看API文档：
- Swagger UI: http://localhost:8080/api/swagger-ui.html
- API Docs: http://localhost:8080/api/v3/api-docs

## 主要接口

### 认证相关
- `GET /api/auth/wechat/qrcode` - 获取微信登录二维码
- `POST /api/auth/wechat/login` - 微信扫码登录
- `POST /api/auth/refresh` - 刷新访问令牌
- `POST /api/auth/logout` - 用户登出

### 提示词相关
- `GET /api/prompts` - 获取提示词列表
- `GET /api/prompts/{id}` - 获取提示词详情
- `POST /api/prompts` - 创建提示词（需要创作者权限）
- `PUT /api/prompts/{id}` - 更新提示词
- `DELETE /api/prompts/{id}` - 删除提示词

### 用户相关
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新用户信息
- `GET /api/users/{id}` - 获取用户公开信息

## 开发说明

### 数据库配置
- 地址：localhost:3306/cn-prompt
- 用户名：root
- 密码：a123456789A

### Redis配置
- 地址：127.0.0.1:6379
- 无密码

### 微信登录配置
需要在配置文件中设置微信开放平台的AppID和AppSecret：
```yaml
app:
  wechat:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret
```

## 部署说明

### Docker部署
项目包含Docker配置文件，可以使用Docker进行部署：

```bash
# 构建镜像
docker build -t cn-prompt-portal ./cn-prompt-portal
docker build -t cn-prompt-frontend ./cn-prompt-frontend

# 使用docker-compose启动
docker-compose up -d
```

### 生产环境配置
生产环境需要配置以下环境变量：
- 数据库连接信息
- Redis连接信息
- JWT密钥
- 微信登录配置
- 支付配置
- 文件存储配置

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

- 项目地址：https://github.com/your-org/cn-prompt-store
- 问题反馈：https://github.com/your-org/cn-prompt-store/issues
- 邮箱：<EMAIL>
