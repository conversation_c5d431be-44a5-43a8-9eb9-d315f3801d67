// 模拟前端登录并设置localStorage
const loginData = {
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTM0NTk3NjQsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiVVNFUiIsInR5cGUiOiJhY2Nlc3MifQ.Ls0WRVpCXAEJynBR0uGMbMDCWa_JU3vXZPew_J4O7CU",
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTYwNDgxNjQsInR5cGUiOiJyZWZyZXNoIn0.kDXGRbFyzzUsiLODr36fkAahO0WjeWccRaAGMve3xUw",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "user": {
      "id": 6,
      "username": "testuser",
      "email": "<EMAIL>",
      "bio": "这是测试用户",
      "role": "USER",
      "status": "ACTIVE",
      "emailVerified": true,
      "phoneVerified": false,
      "totalEarnings": 0,
      "totalSpent": 0,
      "followerCount": 0,
      "followingCount": 0,
      "createdAt": "2025-07-25T22:37:06",
      "updatedAt": "2025-07-25T23:01:49",
      "lastLoginAt": "2025-07-25T23:09:24.083469"
    }
  },
  "timestamp": 1753456164112
};

console.log('🚀 开始模拟前端登录...');

// 1. 设置token到localStorage (模拟apiClient.setToken)
const tokenInfo = {
  accessToken: loginData.data.accessToken,
  refreshToken: loginData.data.refreshToken,
  expiresAt: Date.now() + loginData.data.expiresIn * 1000,
};

localStorage.setItem('token', JSON.stringify(tokenInfo));
console.log('✅ Token已保存到localStorage');

// 2. 设置Zustand store (模拟认证store的状态)
const authStore = {
  state: {
    user: loginData.data.user,
    isAuthenticated: true,
    isLoading: false,
    error: null,
  },
  version: 0,
};

localStorage.setItem('auth-store', JSON.stringify(authStore));
console.log('✅ Auth store已保存到localStorage');

// 3. 验证设置是否成功
console.log('\n📋 验证localStorage内容:');
console.log('Token:', localStorage.getItem('token'));
console.log('Auth Store:', localStorage.getItem('auth-store'));

console.log('\n🎉 模拟登录完成！请刷新页面查看效果。');

// 4. 提供测试链接
console.log('\n🔗 测试链接:');
console.log('- 首页: http://localhost:3000/');
console.log('- 个人中心: http://localhost:3000/profile');
console.log('- 调试页面: http://localhost:3000/debug-auth');
console.log('- 测试登录: http://localhost:3000/test-login');
