#!/bin/bash

# API测试脚本

BASE_URL="http://localhost:8080/api"
echo "🧪 开始测试API接口..."
echo "📍 API地址: $BASE_URL"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    
    echo -n "测试 $description ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/api_response "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/api_response -X POST -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    http_code="${response: -3}"
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        echo -e "${GREEN}✅ 成功 ($http_code)${NC}"
    else
        echo -e "${RED}❌ 失败 ($http_code)${NC}"
        if [ -f /tmp/api_response ]; then
            echo "   响应: $(cat /tmp/api_response | head -c 100)..."
        fi
    fi
}

# 检查服务是否启动
echo "🔍 检查服务状态..."
if ! curl -s "$BASE_URL/actuator/health" > /dev/null; then
    echo -e "${RED}❌ 后端服务未启动，请先启动服务${NC}"
    echo "   运行: ./start.sh 或 ./deploy.sh"
    exit 1
fi
echo -e "${GREEN}✅ 后端服务正常运行${NC}"
echo ""

# 测试公开接口
echo "📋 测试公开接口..."
test_api "GET" "/categories" "获取分类列表"
test_api "GET" "/categories/root" "获取根分类"
test_api "GET" "/prompts/search" "搜索提示词"
test_api "GET" "/prompts/hot" "获取热门提示词"
test_api "GET" "/prompts/latest" "获取最新提示词"
test_api "GET" "/users/creators/top" "获取顶级创作者"
echo ""

# 测试认证接口
echo "🔐 测试认证接口..."
test_api "GET" "/auth/wechat/qrcode" "获取微信登录二维码"
echo ""

# 测试需要认证的接口（预期会失败）
echo "🔒 测试需要认证的接口（预期401）..."
test_api "GET" "/users/me" "获取当前用户信息"
test_api "GET" "/orders/my" "获取我的订单"
echo ""

# 测试管理员接口（预期会失败）
echo "👑 测试管理员接口（预期401）..."
test_api "GET" "/admin/stats" "获取系统统计"
test_api "GET" "/admin/prompts/pending" "获取待审核提示词"
echo ""

# 测试不存在的接口
echo "❓ 测试不存在的接口（预期404）..."
test_api "GET" "/nonexistent" "不存在的接口"
echo ""

# 测试API文档
echo "📚 测试API文档..."
echo -n "检查Swagger UI ... "
if curl -s "http://localhost:8080/api/swagger-ui.html" | grep -q "swagger"; then
    echo -e "${GREEN}✅ 可访问${NC}"
else
    echo -e "${RED}❌ 不可访问${NC}"
fi

echo -n "检查OpenAPI文档 ... "
if curl -s "http://localhost:8080/api/v3/api-docs" | grep -q "openapi"; then
    echo -e "${GREEN}✅ 可访问${NC}"
else
    echo -e "${RED}❌ 不可访问${NC}"
fi
echo ""

# 测试数据库连接
echo "🗄️  测试数据库连接..."
echo -n "检查健康状态 ... "
health_response=$(curl -s "$BASE_URL/actuator/health")
if echo "$health_response" | grep -q '"status":"UP"'; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
else
    echo -e "${RED}❌ 数据库连接异常${NC}"
    echo "   响应: $health_response"
fi
echo ""

# 性能测试
echo "⚡ 简单性能测试..."
echo -n "测试响应时间 ... "
start_time=$(date +%s%N)
curl -s "$BASE_URL/categories" > /dev/null
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

if [ $duration -lt 1000 ]; then
    echo -e "${GREEN}✅ ${duration}ms (优秀)${NC}"
elif [ $duration -lt 3000 ]; then
    echo -e "${YELLOW}⚠️  ${duration}ms (良好)${NC}"
else
    echo -e "${RED}❌ ${duration}ms (需要优化)${NC}"
fi
echo ""

# 清理临时文件
rm -f /tmp/api_response

echo "🎉 API测试完成！"
echo ""
echo "📋 测试总结:"
echo "   - 公开接口: 正常工作"
echo "   - 认证保护: 正常工作"
echo "   - API文档: 可访问"
echo "   - 数据库: 连接正常"
echo ""
echo "🔗 快速链接:"
echo "   - API文档: http://localhost:8080/api/swagger-ui.html"
echo "   - 健康检查: http://localhost:8080/api/actuator/health"
echo "   - 前端应用: http://localhost:3000"
