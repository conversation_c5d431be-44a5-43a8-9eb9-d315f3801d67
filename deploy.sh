#!/bin/bash

# 中文AI提示词交易平台部署脚本

echo "🚀 开始部署中文AI提示词交易平台..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker-compose down --rmi all --volumes --remove-orphans
fi

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查后端健康状态
echo "🏥 检查后端健康状态..."
for i in {1..30}; do
    if curl -f http://localhost:8080/api/actuator/health &> /dev/null; then
        echo "✅ 后端服务启动成功"
        break
    fi
    echo "⏳ 等待后端服务启动... ($i/30)"
    sleep 2
done

# 检查前端服务
echo "🌐 检查前端服务..."
for i in {1..30}; do
    if curl -f http://localhost:3000 &> /dev/null; then
        echo "✅ 前端服务启动成功"
        break
    fi
    echo "⏳ 等待前端服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 部署完成！"
echo ""
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端地址: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/api/swagger-ui.html"
echo "🗄️  数据库: localhost:3306 (用户名: root, 密码: a123456789A)"
echo "🔴 Redis: localhost:6379"
echo ""
echo "📋 查看日志: docker-compose logs -f [service_name]"
echo "🛑 停止服务: docker-compose down"
echo "🔄 重启服务: docker-compose restart [service_name]"
