# 中文AI提示词交易平台 - 前端技术设计文档

## 1. 技术栈选择

### 核心框架
- **Next.js 14** - React全栈框架，支持SSR/SSG，SEO友好
- **TypeScript** - 类型安全，提升开发效率和代码质量
- **Tailwind CSS** - 原子化CSS框架，快速构建响应式UI
- **Shadcn/ui** - 高质量React组件库，基于Radix UI
- **React Hook Form** - 高性能表单处理库
- **Zustand** - 轻量级状态管理
- **React Query (TanStack Query)** - 服务端状态管理和缓存

### UI/UX增强
- **Framer Motion** - 动画库，提升用户体验
- **React Hot Toast** - 消息提示组件
- **Lucide React** - 图标库
- **Next Themes** - 主题切换（支持暗黑模式）
- **React Intersection Observer** - 懒加载和无限滚动

### 开发工具
- **ESLint + Prettier** - 代码规范和格式化
- **Husky + lint-staged** - Git钩子和代码检查
- **Storybook** - 组件开发和文档
- **Jest + Testing Library** - 单元测试

## 2. 项目结构

```
prompt-store/cn-prompt-frontend/
├── app/                          # Next.js 14 App Router
│   ├── (auth)/                   # 认证相关页面组
│   │   ├── login/
│   │   ├── register/
│   │   ├── forgot-password/
│   │   └── layout.tsx
│   ├── (dashboard)/              # 用户仪表板
│   │   ├── profile/
│   │   ├── my-prompts/
│   │   ├── purchases/
│   │   ├── earnings/
│   │   ├── analytics/
│   │   └── layout.tsx
│   ├── (marketplace)/            # 市场页面组
│   │   ├── browse/
│   │   ├── category/[slug]/
│   │   ├── search/
│   │   └── layout.tsx
│   ├── prompt/[id]/              # 提示词详情页
│   ├── user/[username]/          # 用户主页
│   ├── creator/[id]/             # 创作者页面
│   ├── api/                      # API路由
│   │   ├── auth/
│   │   ├── upload/
│   │   └── webhook/
│   ├── globals.css
│   ├── layout.tsx                # 根布局
│   ├── loading.tsx
│   ├── error.tsx
│   ├── not-found.tsx
│   └── page.tsx                  # 首页
├── components/                   # 可复用组件
│   ├── ui/                       # 基础UI组件 (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   ├── dropdown-menu.tsx
│   │   ├── tabs.tsx
│   │   ├── badge.tsx
│   │   ├── avatar.tsx
│   │   ├── pagination.tsx
│   │   └── ...
│   ├── layout/                   # 布局组件
│   │   ├── header.tsx
│   │   ├── footer.tsx
│   │   ├── sidebar.tsx
│   │   └── navigation.tsx
│   ├── forms/                    # 表单组件
│   │   ├── login-form.tsx
│   │   ├── register-form.tsx
│   │   ├── prompt-upload-form.tsx
│   │   ├── profile-form.tsx
│   │   └── payment-form.tsx
│   ├── prompt/                   # 提示词相关组件
│   │   ├── prompt-card.tsx
│   │   ├── prompt-grid.tsx
│   │   ├── prompt-detail.tsx
│   │   ├── prompt-preview.tsx
│   │   ├── prompt-rating.tsx
│   │   └── prompt-comments.tsx
│   ├── user/                     # 用户相关组件
│   │   ├── user-avatar.tsx
│   │   ├── user-profile.tsx
│   │   ├── user-stats.tsx
│   │   └── follow-button.tsx
│   ├── search/                   # 搜索相关组件
│   │   ├── search-bar.tsx
│   │   ├── search-filters.tsx
│   │   ├── search-results.tsx
│   │   └── category-filter.tsx
│   ├── payment/                  # 支付相关组件
│   │   ├── payment-modal.tsx
│   │   ├── price-display.tsx
│   │   └── purchase-button.tsx
│   └── common/                   # 通用组件
│       ├── loading-spinner.tsx
│       ├── error-boundary.tsx
│       ├── image-upload.tsx
│       ├── rich-text-editor.tsx
│       ├── infinite-scroll.tsx
│       └── theme-toggle.tsx
├── lib/                          # 工具函数和配置
│   ├── utils.ts                  # 通用工具函数
│   ├── validations.ts            # 表单验证规则
│   ├── constants.ts              # 常量定义
│   ├── auth.ts                   # 认证相关工具
│   ├── api.ts                    # API客户端配置
│   ├── upload.ts                 # 文件上传工具
│   ├── payment.ts                # 支付相关工具
│   ├── format.ts                 # 格式化工具
│   └── db.ts                     # 数据库连接（如使用Prisma）
├── hooks/                        # 自定义React Hooks
│   ├── use-auth.ts               # 认证状态管理
│   ├── use-prompts.ts            # 提示词数据获取
│   ├── use-search.ts             # 搜索功能
│   ├── use-upload.ts             # 文件上传
│   ├── use-payment.ts            # 支付处理
│   ├── use-infinite-scroll.ts    # 无限滚动
│   ├── use-debounce.ts           # 防抖处理
│   └── use-local-storage.ts      # 本地存储
├── store/                        # 状态管理
│   ├── auth-store.ts             # 用户认证状态
│   ├── cart-store.ts             # 购物车状态
│   ├── search-store.ts           # 搜索状态
│   └── ui-store.ts               # UI状态（主题、侧边栏等）
├── types/                        # TypeScript类型定义
│   ├── auth.ts                   # 认证相关类型
│   ├── prompt.ts                 # 提示词相关类型
│   ├── user.ts                   # 用户相关类型
│   ├── payment.ts                # 支付相关类型
│   ├── api.ts                    # API响应类型
│   └── common.ts                 # 通用类型
├── styles/                       # 样式文件
│   ├── globals.css               # 全局样式
│   └── components.css            # 组件样式
├── public/                       # 静态资源
│   ├── images/
│   │   ├── logo.svg
│   │   ├── placeholder.png
│   │   └── icons/
│   ├── favicon.ico
│   └── manifest.json
├── docs/                         # 文档
│   ├── components.md
│   ├── api.md
│   └── deployment.md
├── tests/                        # 测试文件
│   ├── __mocks__/
│   ├── components/
│   ├── hooks/
│   └── utils/
├── .env.local                    # 环境变量
├── .env.example                  # 环境变量示例
├── next.config.js                # Next.js配置
├── tailwind.config.js            # Tailwind配置
├── tsconfig.json                 # TypeScript配置
├── package.json
└── README.md
```

## 3. 核心功能模块设计

### 3.1 用户认证系统
- **注册/登录**：支持邮箱、手机号、第三方登录（微信、QQ、GitHub）
- **身份验证**：JWT Token + Refresh Token机制
- **权限管理**：普通用户、创作者、管理员角色
- **个人资料**：头像上传、个人简介、社交链接

### 3.2 提示词市场
- **分类浏览**：按AI模型、用途、风格分类
- **搜索功能**：关键词搜索、标签过滤、价格筛选
- **排序选项**：最新、最热、评分、价格
- **推荐算法**：基于用户行为的个性化推荐

### 3.3 提示词详情页
- **预览展示**：提示词效果图、使用示例
- **详细信息**：描述、标签、适用模型、使用说明
- **评价系统**：星级评分、用户评论、使用反馈
- **购买流程**：立即购买、加入购物车、收藏

### 3.4 创作者中心
- **提示词上传**：富文本编辑器、图片上传、标签设置
- **销售管理**：价格设置、促销活动、库存管理
- **数据分析**：销售统计、用户反馈、收入报表
- **创作者认证**：身份验证、作品集展示

### 3.5 支付系统
- **支付方式**：支付宝、微信支付、银行卡
- **购物车**：批量购买、优惠券使用
- **订单管理**：订单历史、退款申请、发票开具
- **分成机制**：平台抽成、创作者收益分配

### 3.6 用户仪表板
- **我的购买**：已购买提示词、下载历史
- **我的收藏**：收藏的提示词、关注的创作者
- **我的销售**：销售数据、收入统计（创作者）
- **账户设置**：个人信息、安全设置、通知偏好

## 4. 技术实现细节

### 4.1 状态管理策略
```typescript
// 使用Zustand进行轻量级状态管理
interface AuthStore {
  user: User | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  updateProfile: (data: ProfileData) => Promise<void>;
}

interface CartStore {
  items: CartItem[];
  addItem: (prompt: Prompt) => void;
  removeItem: (promptId: string) => void;
  clearCart: () => void;
  getTotalPrice: () => number;
}
```

### 4.2 API集成
```typescript
// API客户端配置
class ApiClient {
  private baseURL: string;
  private token: string | null;

  async get<T>(endpoint: string): Promise<T>;
  async post<T>(endpoint: string, data: any): Promise<T>;
  async put<T>(endpoint: string, data: any): Promise<T>;
  async delete<T>(endpoint: string): Promise<T>;
}

// React Query集成
const usePrompts = (filters: PromptFilters) => {
  return useQuery({
    queryKey: ['prompts', filters],
    queryFn: () => apiClient.get<PromptsResponse>('/prompts', { params: filters }),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};
```

### 4.3 文件上传处理
```typescript
// 支持多种文件类型上传
interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  uploadPath: string;
}

const useFileUpload = (config: UploadConfig) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const upload = async (file: File) => {
    // 文件验证、压缩、上传逻辑
  };

  return { upload, uploading, progress };
};
```

### 4.4 搜索功能实现
```typescript
// 搜索状态管理
interface SearchState {
  query: string;
  filters: SearchFilters;
  results: Prompt[];
  loading: boolean;
  hasMore: boolean;
}

// 防抖搜索Hook
const useSearch = () => {
  const [searchState, setSearchState] = useState<SearchState>();
  const debouncedQuery = useDebounce(searchState.query, 300);

  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery);
    }
  }, [debouncedQuery]);
};
```

## 5. 性能优化策略

### 5.1 代码分割和懒加载
- **路由级别分割**：每个页面组件独立打包
- **组件级别分割**：大型组件按需加载
- **第三方库分割**：vendor chunk独立缓存

### 5.2 图片优化
- **Next.js Image组件**：自动优化、懒加载、响应式
- **WebP格式支持**：现代浏览器优先使用WebP
- **CDN集成**：静态资源CDN加速

### 5.3 缓存策略
- **浏览器缓存**：静态资源长期缓存
- **API缓存**：React Query缓存策略
- **本地存储**：用户偏好、购物车状态

### 5.4 SEO优化
- **服务端渲染**：首屏内容SSR
- **元数据管理**：动态生成meta标签
- **结构化数据**：JSON-LD格式的结构化数据
- **站点地图**：自动生成sitemap.xml

## 6. 响应式设计

### 6.1 断点设计
```css
/* Tailwind CSS断点 */
sm: 640px   /* 手机横屏 */
md: 768px   /* 平板 */
lg: 1024px  /* 小型桌面 */
xl: 1280px  /* 桌面 */
2xl: 1536px /* 大屏桌面 */
```

### 6.2 移动端优化
- **触摸友好**：按钮大小、间距优化
- **手势支持**：滑动、缩放等手势操作
- **性能优化**：减少动画、优化渲染

## 7. 安全考虑

### 7.1 前端安全
- **XSS防护**：输入验证、输出转义
- **CSRF防护**：Token验证
- **内容安全策略**：CSP头部配置
- **敏感信息保护**：避免在前端存储敏感数据

### 7.2 数据验证
- **表单验证**：前端+后端双重验证
- **文件上传验证**：类型、大小、内容检查
- **API参数验证**：TypeScript类型检查

## 8. 国际化支持

### 8.1 多语言支持
- **next-intl**：国际化框架
- **语言切换**：中文、英文支持
- **本地化内容**：日期、数字、货币格式

### 8.2 文化适配
- **支付方式**：支付宝、微信支付优先
- **社交登录**：微信、QQ登录
- **内容审核**：符合中国法规要求

## 9. 开发和部署

### 9.1 开发环境
- **热重载**：开发时实时更新
- **类型检查**：TypeScript严格模式
- **代码规范**：ESLint + Prettier
- **Git工作流**：feature分支 + PR审核

### 9.2 构建和部署
- **构建优化**：Tree shaking、代码压缩
- **环境配置**：开发、测试、生产环境
- **CI/CD**：自动化测试、构建、部署
- **监控告警**：错误监控、性能监控

## 10. 测试策略

### 10.1 测试类型
- **单元测试**：组件、工具函数测试
- **集成测试**：API集成、用户流程测试
- **E2E测试**：关键业务流程端到端测试
- **性能测试**：页面加载、交互响应时间

### 10.2 测试工具
- **Jest**：单元测试框架
- **Testing Library**：组件测试
- **Playwright**：E2E测试
- **Lighthouse**：性能测试