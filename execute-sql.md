# 数据库初始化说明

## 📋 SQL文件修改完成

我已经将 `init.sql` 文件中的所有 `TIMESTAMP` 类型修改为 `DATETIME` 类型，主要修改包括：

### 修改内容
1. **用户表 (users)**: `created_at`, `updated_at`, `last_login_at`
2. **分类表 (categories)**: `created_at`, `updated_at`  
3. **提示词表 (prompts)**: `created_at`, `updated_at`, `published_at`
4. **订单表 (orders)**: `created_at`, `updated_at`, `paid_at`, `cancelled_at`, `refunded_at`
5. **订单项表 (order_items)**: `created_at`

### 新增表结构
- ✅ 添加了完整的订单表 (orders)
- ✅ 添加了订单项表 (order_items)
- ✅ 添加了相关索引和外键约束

## 🚀 执行方式

### 方式一：使用脚本执行（推荐）
```bash
./setup-database.sh
```

### 方式二：手动执行
```bash
# 连接到MySQL
mysql -u root -p

# 执行SQL文件
source init.sql;

# 或者
mysql -u root -p < init.sql
```

### 方式三：使用MySQL客户端
1. 打开MySQL Workbench或其他客户端
2. 连接到数据库
3. 打开 `init.sql` 文件
4. 执行整个脚本

## 📊 数据库结构

### 核心表结构
```
cn-prompt (数据库)
├── users (用户表)
├── categories (分类表)  
├── prompts (提示词表)
├── orders (订单表)
└── order_items (订单项表)
```

### 测试数据
- ✅ 6个分类数据
- ✅ 4个测试用户（包含管理员和创作者）
- ✅ 6个测试提示词数据

## 🔧 配置更新

执行SQL后，请确保后端配置文件中的数据库连接信息正确：

### application.yml
```yaml
spring:
  datasource:
    url: ************************************************************************************************************
    username: root
    password: [您的密码]
```

### application-docker.yml
```yaml
spring:
  datasource:
    url: ********************************************************************************************************
    username: root
    password: [您的密码]
```

## ✅ 验证步骤

执行SQL后，可以通过以下命令验证：

```sql
-- 检查数据库
SHOW DATABASES LIKE 'cn-prompt';

-- 检查表结构
USE `cn-prompt`;
SHOW TABLES;

-- 检查数据
SELECT COUNT(*) FROM categories;
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM prompts;

-- 查看表结构
DESCRIBE users;
DESCRIBE prompts;
```

## 🎯 预期结果

执行成功后应该看到：
- ✅ 数据库 `cn-prompt` 创建成功
- ✅ 5个数据表创建成功
- ✅ 测试数据插入成功
- ✅ 所有时间字段使用 DATETIME 类型

执行完成后，就可以启动后端服务了！
