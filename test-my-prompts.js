// 测试"我的提示词"功能
console.log('🧪 开始测试"我的提示词"功能...\n');

// 1. 设置登录状态
console.log('🔐 1. 设置登录状态...');
const tokenInfo = {
  accessToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU3NjIxLCJleHAiOjE3NTM0NjEyMjEsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiQ1JFQVRPUiIsInR5cGUiOiJhY2Nlc3MifQ.FV7yZkSATa5GKZDtw_QcsSrcEu0hbbyIMMLJlsXQfRM',
  refreshToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU3NjIxLCJleHAiOjE3NTYwNDk2MjEsInR5cGUiOiJyZWZyZXNoIn0.kDXGRbFyzzUsiLODr36fkAahO0WjeWccRaAGMve3xUw',
  expiresAt: Date.now() + 3600 * 1000,
};

const authStore = {
  state: {
    user: {
      id: 6,
      username: 'testuser',
      email: '<EMAIL>',
      role: 'CREATOR',
      bio: '这是测试用户',
      status: 'ACTIVE'
    },
    isAuthenticated: true,
    isLoading: false,
    error: null,
  },
  version: 0,
};

// 模拟localStorage
global.localStorage = {
  setItem: (key, value) => console.log(`📝 设置 ${key}: ${value.substring(0, 50)}...`),
  getItem: (key) => {
    if (key === 'token') return JSON.stringify(tokenInfo);
    if (key === 'auth-store') return JSON.stringify(authStore);
    return null;
  }
};

console.log('✅ 登录状态设置完成\n');

// 2. 测试API调用
console.log('🌐 2. 测试API调用...');

async function testUserPromptsAPI() {
  try {
    const fetch = require('node-fetch');
    
    const response = await fetch('http://localhost:8080/api/prompts/user/6', {
      headers: {
        'Authorization': `Bearer ${tokenInfo.accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ API调用成功！');
    console.log(`📊 获取到 ${data.data.length} 个提示词`);
    
    // 统计信息
    const stats = {
      total: data.data.length,
      approved: data.data.filter(p => p.status === 'APPROVED').length,
      pending: data.data.filter(p => p.status === 'PENDING').length,
      draft: data.data.filter(p => p.status === 'DRAFT').length,
      rejected: data.data.filter(p => p.status === 'REJECTED').length,
      free: data.data.filter(p => p.isFree).length,
      paid: data.data.filter(p => !p.isFree).length,
    };
    
    console.log('\n📈 统计信息:');
    console.log(`   总数: ${stats.total}`);
    console.log(`   已发布: ${stats.approved}`);
    console.log(`   审核中: ${stats.pending}`);
    console.log(`   草稿: ${stats.draft}`);
    console.log(`   已拒绝: ${stats.rejected}`);
    console.log(`   免费: ${stats.free}`);
    console.log(`   付费: ${stats.paid}`);
    
    console.log('\n📋 提示词列表:');
    data.data.forEach((prompt, index) => {
      console.log(`   ${index + 1}. ${prompt.title}`);
      console.log(`      状态: ${prompt.status}`);
      console.log(`      价格: ${prompt.isFree ? '免费' : '¥' + prompt.price}`);
      console.log(`      浏览: ${prompt.viewCount}, 下载: ${prompt.downloadCount}, 点赞: ${prompt.likeCount}`);
      console.log(`      创建时间: ${new Date(prompt.createdAt).toLocaleString()}`);
      console.log('');
    });
    
    return data;
    
  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    return null;
  }
}

// 3. 测试前端页面访问
async function testFrontendPages() {
  console.log('\n🌐 3. 测试前端页面访问...');
  
  const pages = [
    'http://localhost:3000/profile',
    'http://localhost:3000/dashboard'
  ];
  
  for (const url of pages) {
    try {
      const fetch = require('node-fetch');
      const response = await fetch(url);
      
      if (response.ok) {
        console.log(`✅ ${url} - 访问成功 (${response.status})`);
      } else {
        console.log(`⚠️ ${url} - 访问异常 (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${url} - 访问失败: ${error.message}`);
    }
  }
}

// 执行测试
async function runTests() {
  const apiData = await testUserPromptsAPI();
  await testFrontendPages();
  
  console.log('\n🎯 测试总结:');
  if (apiData) {
    console.log('✅ 后端API正常工作');
    console.log('✅ 数据获取成功');
    console.log('✅ 前端页面应该能正常显示提示词列表');
  } else {
    console.log('❌ 后端API调用失败');
  }
  
  console.log('\n💡 下一步:');
  console.log('1. 在浏览器中访问 http://localhost:3000/profile');
  console.log('2. 在控制台执行以下代码设置登录状态:');
  console.log(`
// 设置token
localStorage.setItem('token', '${JSON.stringify(tokenInfo)}');

// 设置认证store  
localStorage.setItem('auth-store', '${JSON.stringify(authStore)}');

// 刷新页面
location.reload();
  `);
  console.log('3. 点击"我的提示词"标签页查看效果');
}

// 检查是否安装了node-fetch
try {
  require('node-fetch');
  runTests();
} catch (error) {
  console.log('❌ 需要安装node-fetch: npm install node-fetch');
  console.log('或者直接在浏览器中测试:');
  console.log('1. 访问 http://localhost:3000/profile');
  console.log('2. 在浏览器控制台设置登录状态');
  console.log('3. 查看"我的提示词"标签页');
}
