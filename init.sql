-- 初始化数据库脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `cn-prompt` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `cn-prompt`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `username` VARCHAR(50) UNIQUE NOT NULL,
    `email` VARCHAR(100) UNIQUE,
    `phone` VARCHAR(20),
    `password_hash` VARCHAR(255),
    `wechat_open_id` VARCHAR(100) UNIQUE,
    `wechat_union_id` VARCHAR(100),
    `avatar_url` VARCHAR(500),
    `bio` TEXT,
    `role` ENUM('USER', 'CREATOR', 'ADMIN') DEFAULT 'USER',
    `status` ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE',
    `email_verified` <PERSON><PERSON><PERSON><PERSON>N DEFAULT FALSE,
    `phone_verified` <PERSON><PERSON><PERSON>EA<PERSON> DEFAULT FALSE,
    `total_earnings` DECIMAL(10,2) DEFAULT 0.00,
    `total_spent` DECIMAL(10,2) DEFAULT 0.00,
    `follower_count` INT DEFAULT 0,
    `following_count` INT DEFAULT 0,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `last_login_at` DATETIME,
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_wechat_open_id` (`wechat_open_id`),
    INDEX `idx_wechat_union_id` (`wechat_union_id`),
    INDEX `idx_role` (`role`),
    INDEX `idx_status` (`status`)
);

-- 创建分类表
CREATE TABLE IF NOT EXISTS `categories` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `slug` VARCHAR(100) UNIQUE NOT NULL,
    `description` TEXT,
    `parent_id` BIGINT,
    `icon_url` VARCHAR(500),
    `sort_order` INT DEFAULT 0,
    `prompt_count` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_id`) REFERENCES `categories`(`id`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_slug` (`slug`),
    INDEX `idx_sort_order` (`sort_order`)
);

-- 创建提示词表
CREATE TABLE IF NOT EXISTS `prompts` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `title` VARCHAR(200) NOT NULL,
    `description` TEXT,
    `content` TEXT NOT NULL,
    `ai_model` VARCHAR(50) NOT NULL,
    `category_id` BIGINT,
    `price` DECIMAL(8,2) NOT NULL,
    `original_price` DECIMAL(8,2),
    `preview_images` JSON,
    `example_outputs` JSON,
    `usage_instructions` TEXT,
    `tags` JSON,
    `status` ENUM('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED') DEFAULT 'DRAFT',
    `view_count` INT DEFAULT 0,
    `download_count` INT DEFAULT 0,
    `like_count` INT DEFAULT 0,
    `rating_avg` DECIMAL(3,2) DEFAULT 0.00,
    `rating_count` INT DEFAULT 0,
    `is_featured` BOOLEAN DEFAULT FALSE,
    `is_free` BOOLEAN DEFAULT FALSE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `published_at` DATETIME,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_ai_model` (`ai_model`),
    INDEX `idx_price` (`price`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_rating_avg` (`rating_avg`),
    FULLTEXT `idx_search` (`title`, `description`)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS `orders` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `order_no` VARCHAR(32) UNIQUE NOT NULL,
    `user_id` BIGINT NOT NULL,
    `total_amount` DECIMAL(10,2) NOT NULL,
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00,
    `final_amount` DECIMAL(10,2) NOT NULL,
    `status` ENUM('PENDING', 'PAID', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING',
    `payment_method` ENUM('ALIPAY', 'WECHAT', 'BANK_CARD') NOT NULL,
    `payment_id` VARCHAR(100),
    `paid_at` DATETIME,
    `cancelled_at` DATETIME,
    `refunded_at` DATETIME,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
    INDEX `idx_order_no` (`order_no`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
);

-- 创建订单项表
CREATE TABLE IF NOT EXISTS `order_items` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `order_id` BIGINT NOT NULL,
    `prompt_id` BIGINT NOT NULL,
    `prompt_title` VARCHAR(200) NOT NULL,
    `unit_price` DECIMAL(8,2) NOT NULL,
    `quantity` INT DEFAULT 1,
    `total_price` DECIMAL(8,2) NOT NULL,
    `creator_id` BIGINT NOT NULL,
    `commission_rate` DECIMAL(5,4) DEFAULT 0.7000,
    `creator_earnings` DECIMAL(8,2) NOT NULL,
    `platform_earnings` DECIMAL(8,2) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
    FOREIGN KEY (`prompt_id`) REFERENCES `prompts`(`id`),
    FOREIGN KEY (`creator_id`) REFERENCES `users`(`id`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_prompt_id` (`prompt_id`),
    INDEX `idx_creator_id` (`creator_id`)
);

-- 插入初始分类数据
INSERT IGNORE INTO `categories` (`name`, `slug`, `description`, `sort_order`, `prompt_count`) VALUES
('文案写作', 'writing', '营销文案、文章写作、创意内容', 1, 120),
('AI绘画', 'art', '绘画风格、场景描述、艺术创作', 2, 80),
('代码编程', 'coding', '代码生成、调试、文档编写', 3, 60),
('对话聊天', 'chat', '角色扮演、对话模拟、客服助手', 4, 90),
('创意设计', 'design', '设计理念、色彩搭配、创意灵感', 5, 50),
('效率工具', 'productivity', '工作助手、数据分析、自动化', 6, 70);

-- 创建管理员用户
INSERT IGNORE INTO `users` (`username`, `email`, `role`, `status`) VALUES
('admin', '<EMAIL>', 'ADMIN', 'ACTIVE');

-- 创建测试创作者用户
INSERT IGNORE INTO `users` (`username`, `email`, `role`, `status`, `avatar_url`, `bio`, `total_earnings`, `follower_count`) VALUES
('creator1', '<EMAIL>', 'CREATOR', 'ACTIVE', 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator1', '专业AI提示词创作者，擅长文案写作和创意设计', 2580.50, 156),
('creator2', '<EMAIL>', 'CREATOR', 'ACTIVE', 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator2', 'AI绘画专家，Midjourney和Stable Diffusion资深用户', 1920.30, 89),
('creator3', '<EMAIL>', 'CREATOR', 'ACTIVE', 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator3', '程序员转型AI创作者，专注代码相关提示词', 1456.80, 67);

-- 插入测试提示词数据
INSERT IGNORE INTO `prompts` (`user_id`, `title`, `description`, `content`, `ai_model`, `category_id`, `price`, `original_price`, `status`, `view_count`, `download_count`, `like_count`, `rating_avg`, `rating_count`, `is_featured`, `is_free`, `published_at`) VALUES
(2, '专业营销文案生成器', '帮助您快速生成高转化率的营销文案，适用于各种产品和服务的推广', '你是一位资深的营销文案专家，请为以下产品/服务创作一份吸引人的营销文案：\n\n产品/服务：[在此输入产品或服务名称]\n目标受众：[在此描述目标客户群体]\n核心卖点：[在此列出主要优势]\n\n请创作包含以下元素的文案：\n1. 吸引眼球的标题\n2. 痛点分析\n3. 解决方案介绍\n4. 产品优势展示\n5. 行动号召\n\n文案风格要求：简洁有力、情感共鸣、逻辑清晰', 'ChatGPT', 1, 29.90, 39.90, 'APPROVED', 1250, 89, 156, 4.8, 23, TRUE, FALSE, NOW()),

(3, 'Midjourney风格化提示词模板', '专业的Midjourney提示词模板，帮助您创作出风格独特的AI艺术作品', '/imagine prompt: [主体描述], [风格描述], [构图描述], [光线描述], [色彩描述], [材质描述], [情感氛围], [技术参数] --ar [宽高比] --v 6 --style raw\n\n使用说明：\n1. 主体描述：详细描述画面主要内容\n2. 风格描述：如"油画风格"、"赛博朋克"、"极简主义"等\n3. 构图描述：如"特写"、"全景"、"鸟瞰"等\n4. 光线描述：如"柔和光线"、"戏剧性光影"、"黄金时刻"等\n5. 色彩描述：主要色调和配色方案\n6. 材质描述：表面质感和材料特性\n7. 情感氛围：想要传达的情绪和感觉\n8. 技术参数：相机设置、镜头类型等', 'Midjourney', 2, 19.90, NULL, 'APPROVED', 890, 67, 98, 4.9, 18, TRUE, FALSE, NOW()),

(4, '代码注释自动生成', '智能生成清晰、专业的代码注释，提高代码可读性和维护性', '你是一位经验丰富的软件工程师，请为以下代码生成详细的注释：\n\n```[编程语言]\n[在此粘贴需要注释的代码]\n```\n\n注释要求：\n1. 为每个函数添加功能说明\n2. 解释复杂的算法逻辑\n3. 标注重要的变量用途\n4. 添加参数和返回值说明\n5. 包含使用示例（如适用）\n\n注释风格：\n- 使用标准的文档注释格式\n- 语言简洁明了\n- 重点突出关键逻辑\n- 便于后续维护', 'ChatGPT', 3, 25.00, NULL, 'APPROVED', 650, 45, 78, 4.7, 15, FALSE, FALSE, NOW()),

(2, '角色扮演对话模板', '创建生动有趣的角色扮演对话，适用于客服、教育、娱乐等场景', '你现在要扮演一个[角色名称]，具有以下特征：\n\n角色背景：[详细描述角色的背景故事]\n性格特点：[列出主要性格特征]\n专业领域：[角色的专业知识范围]\n说话风格：[描述说话方式和语言特点]\n\n对话规则：\n1. 始终保持角色设定\n2. 用符合角色身份的语言风格回答\n3. 结合角色的专业知识提供帮助\n4. 保持友好和专业的态度\n5. 适当加入角色特有的口头禅或表达方式\n\n现在请开始扮演这个角色，并回答用户的问题：[用户问题]', 'ChatGPT', 4, 15.90, 19.90, 'APPROVED', 780, 56, 89, 4.6, 12, FALSE, FALSE, NOW()),

(3, '免费Logo设计提示词', '免费的Logo设计提示词模板，帮助您快速生成创意Logo概念', 'Design a modern and minimalist logo for [公司/品牌名称]:\n\nBrand Description: [品牌描述]\nIndustry: [行业类型]\nTarget Audience: [目标受众]\nBrand Values: [品牌价值观]\n\nDesign Requirements:\n- Style: [现代简约/经典优雅/活力动感/专业严谨]\n- Colors: [主要颜色偏好]\n- Elements: [希望包含的元素]\n- Mood: [想要传达的情感]\n\nTechnical Specs:\n- Vector format\n- Scalable design\n- Works in both color and monochrome\n- Suitable for digital and print media', 'DALL-E', 5, 0.00, NULL, 'APPROVED', 1200, 234, 189, 4.5, 45, FALSE, TRUE, NOW()),

(4, '数据分析报告生成器', '自动生成专业的数据分析报告，提高工作效率', '你是一位资深的数据分析师，请根据以下数据生成一份专业的分析报告：\n\n数据概况：\n[在此输入数据基本信息]\n\n分析目标：\n[在此描述分析目的和问题]\n\n请生成包含以下部分的报告：\n\n1. 执行摘要\n   - 关键发现\n   - 主要结论\n   - 建议行动\n\n2. 数据概述\n   - 数据来源\n   - 数据质量\n   - 分析方法\n\n3. 详细分析\n   - 趋势分析\n   - 对比分析\n   - 异常识别\n\n4. 结论与建议\n   - 核心洞察\n   - 实施建议\n   - 风险提示', 'ChatGPT', 6, 35.00, NULL, 'APPROVED', 420, 28, 45, 4.4, 8, FALSE, FALSE, NOW());
