// 测试前端邮箱登录流程
const puppeteer = require('puppeteer');

async function testFrontendLogin() {
  console.log('🧪 开始测试前端邮箱登录流程...\n');

  const browser = await puppeteer.launch({ 
    headless: false, 
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  try {
    const page = await browser.newPage();
    
    // 1. 访问调试页面
    console.log('📱 1. 访问调试页面...');
    await page.goto('http://localhost:3000/debug-auth');
    await page.waitForSelector('.test-button');
    
    // 2. 检查初始状态
    console.log('🔍 2. 检查初始登录状态...');
    const initialState = await page.evaluate(() => {
      const authStatus = document.querySelector('.space-y-2 .flex:first-child span:last-child').textContent;
      const userInfo = document.querySelector('.space-y-2 .flex:nth-child(2) span:last-child').textContent;
      return { authStatus, userInfo };
    });
    console.log('初始状态:', initialState);
    
    // 3. 点击模拟登录按钮
    console.log('\n🔐 3. 点击模拟登录...');
    await page.click('button:nth-child(2)'); // 模拟登录按钮
    
    // 等待页面刷新
    await page.waitForNavigation();
    
    // 4. 检查登录后状态
    console.log('✅ 4. 检查登录后状态...');
    const loginState = await page.evaluate(() => {
      const authStatus = document.querySelector('.space-y-2 .flex:first-child span:last-child').textContent;
      const userInfo = document.querySelector('.space-y-2 .flex:nth-child(2) span:last-child').textContent;
      const token = document.querySelector('.space-y-2 .flex:nth-child(3) span:last-child').textContent;
      const authStore = document.querySelector('.space-y-2 .flex:nth-child(4) span:last-child').textContent;
      return { authStatus, userInfo, token, authStore };
    });
    console.log('登录后状态:', loginState);
    
    // 5. 访问个人中心页面
    console.log('\n👤 5. 访问个人中心页面...');
    await page.goto('http://localhost:3000/profile');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
    
    // 检查个人中心是否显示用户信息
    const profileContent = await page.evaluate(() => {
      const title = document.querySelector('h1, h2, h3')?.textContent || '';
      const userCard = document.querySelector('.card')?.textContent || '';
      return { title, userCard: userCard.substring(0, 200) + '...' };
    });
    console.log('个人中心内容:', profileContent);
    
    // 6. 访问首页检查Header
    console.log('\n🏠 6. 访问首页检查Header...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(2000);
    
    const headerContent = await page.evaluate(() => {
      const loginButton = document.querySelector('a[href*="login"]')?.textContent || '';
      const userMenu = document.querySelector('button[aria-haspopup="menu"]')?.textContent || '';
      const userAvatar = document.querySelector('.rounded-full')?.className || '';
      return { loginButton, userMenu, userAvatar };
    });
    console.log('Header状态:', headerContent);
    
    console.log('\n🎉 前端登录流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 保持浏览器打开以便查看结果
    console.log('\n💡 浏览器将保持打开状态，请手动关闭...');
    // await browser.close();
  }
}

// 检查是否安装了puppeteer
try {
  require('puppeteer');
  testFrontendLogin();
} catch (error) {
  console.log('❌ 需要安装puppeteer: npm install puppeteer');
  console.log('或者手动测试：');
  console.log('1. 访问 http://localhost:3000/debug-auth');
  console.log('2. 点击"模拟登录"按钮');
  console.log('3. 访问 http://localhost:3000/profile');
  console.log('4. 访问 http://localhost:3000/ 检查Header');
}
