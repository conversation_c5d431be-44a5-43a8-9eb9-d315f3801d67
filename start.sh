#!/bin/bash

# 中文AI提示词交易平台启动脚本

echo "🚀 启动中文AI提示词交易平台..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装，请先安装Java 17+"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 检查MySQL
if ! command -v mysql &> /dev/null; then
    echo "⚠️  MySQL未安装，请确保MySQL 8.0+已安装并运行"
fi

# 检查Redis
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️  Redis未安装，请确保Redis 7.x+已安装并运行"
fi

echo "📦 安装前端依赖..."
cd cn-prompt-frontend
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "🔧 启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

cd ..

echo "🔧 启动后端服务..."
cd cn-prompt-portal

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven未安装，请先安装Maven"
    kill $FRONTEND_PID
    exit 1
fi

mvn spring-boot:run &
BACKEND_PID=$!

cd ..

echo "✅ 服务启动完成！"
echo ""
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端地址: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/api/swagger-ui.html"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '🛑 正在停止服务...'; kill $FRONTEND_PID $BACKEND_PID; exit" INT
wait
