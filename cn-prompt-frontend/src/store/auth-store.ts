import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthState, LoginResponse, WechatQRCodeResponse } from '@/types/auth';
import { apiClient } from '@/lib/api';
import { toast } from 'react-hot-toast';

interface AuthStore extends AuthState {
  // Actions
  login: (code: string, state?: string) => Promise<void>;
  emailLogin: (email: string, code: string) => Promise<void>;
  emailRegister: (email: string, username: string, code: string, bio?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  getWechatQRCode: () => Promise<WechatQRCodeResponse>;
  updateUser: (user: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (code: string, state?: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.post<LoginResponse>('/auth/wechat/login', {
            code,
            state,
          });

          // 设置令牌
          apiClient.setToken({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            expiresAt: Date.now() + response.expiresIn * 1000,
          });

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success('登录成功');
        } catch (error: any) {
          const errorMessage = error.message || '登录失败';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          throw error;
        }
      },

      emailLogin: async (email: string, code: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.post<LoginResponse>('/auth/email/login', {
            email,
            code,
          });

          // 设置令牌
          apiClient.setToken({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            expiresAt: Date.now() + response.expiresIn * 1000,
          });

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success('登录成功');
        } catch (error: any) {
          const errorMessage = error.message || '登录失败';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          throw error;
        }
      },

      emailRegister: async (email: string, username: string, code: string, bio?: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.post<LoginResponse>('/auth/email/register', {
            email,
            username,
            code,
            bio,
          });

          // 设置令牌
          apiClient.setToken({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            expiresAt: Date.now() + response.expiresIn * 1000,
          });

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success('注册成功');
        } catch (error: any) {
          const errorMessage = error.message || '注册失败';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          // 调用登出接口
          await apiClient.post('/auth/logout');
        } catch (error) {
          console.error('Logout API call failed:', error);
        } finally {
          // 清除本地状态和令牌
          apiClient.clearToken();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          toast.success('已退出登录');
        }
      },

      refreshToken: async () => {
        const tokenInfo = apiClient.getToken();
        if (!tokenInfo?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await apiClient.post<LoginResponse>('/auth/refresh', {}, {
            headers: {
              Authorization: `Bearer ${tokenInfo.refreshToken}`,
            },
          });

          // 更新令牌
          apiClient.setToken({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            expiresAt: Date.now() + response.expiresIn * 1000,
          });

          set({
            user: response.user,
            isAuthenticated: true,
            error: null,
          });
        } catch (error: any) {
          // 刷新失败，清除状态
          apiClient.clearToken();
          set({
            user: null,
            isAuthenticated: false,
            error: error.message || '令牌刷新失败',
          });
          throw error;
        }
      },

      getWechatQRCode: async () => {
        try {
          const response = await apiClient.get<WechatQRCodeResponse>('/auth/wechat/qrcode');
          return response;
        } catch (error: any) {
          const errorMessage = error.message || '获取二维码失败';
          set({ error: errorMessage });
          toast.error(errorMessage);
          throw error;
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({
            user: { ...user, ...userData },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 重新水化后检查令牌是否有效
        console.log('🔄 Auth store rehydrating, state:', state);

        if (state?.isAuthenticated) {
          const tokenInfo = apiClient.getToken();
          console.log('📱 Token from apiClient:', tokenInfo);

          if (!tokenInfo || tokenInfo.expiresAt <= Date.now()) {
            console.log('❌ Token invalid or expired, clearing state');
            // 令牌无效，清除状态
            state.user = null;
            state.isAuthenticated = false;
            apiClient.clearToken();
          } else {
            console.log('✅ Token valid, will fetch user info after rehydration');
            // 令牌有效，在rehydration完成后获取用户信息
            setTimeout(() => {
              apiClient.get<User>('/auth/me')
                .then((user) => {
                  console.log('👤 User info fetched after rehydration:', user);
                  useAuthStore.setState({
                    user,
                    isAuthenticated: true,
                  });
                })
                .catch((error) => {
                  console.error('❌ Failed to fetch user info after rehydration:', error);
                  apiClient.clearToken();
                  useAuthStore.setState({
                    user: null,
                    isAuthenticated: false,
                  });
                });
            }, 100);
          }
        }
      },
    }
  )
);

// 初始化时检查认证状态
if (typeof window !== 'undefined') {
  console.log('🚀 Initializing auth store...');
  const tokenInfo = apiClient.getToken();
  console.log('📱 Initial token check:', tokenInfo);

  if (tokenInfo && tokenInfo.expiresAt > Date.now()) {
    console.log('✅ Token valid, fetching user info...');
    // 令牌有效，尝试获取用户信息
    apiClient.get('/auth/me')
      .then((user: User) => {
        console.log('👤 Initial user info fetched:', user);
        useAuthStore.setState({
          user,
          isAuthenticated: true,
        });
      })
      .catch((error) => {
        console.error('❌ Initial user info fetch failed:', error);
        // 获取用户信息失败，清除状态
        apiClient.clearToken();
        useAuthStore.setState({
          user: null,
          isAuthenticated: false,
        });
      });
  } else {
    console.log('❌ No valid token found during initialization');
  }
}
