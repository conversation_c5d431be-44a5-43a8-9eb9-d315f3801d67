export default function TestStylesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
          样式测试页面
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 卡片 1 */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">基础样式</h2>
            <p className="text-gray-600 mb-4">
              这是一个测试卡片，用来验证 Tailwind CSS 是否正常工作。
            </p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
              测试按钮
            </button>
          </div>
          
          {/* 卡片 2 */}
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg shadow-lg p-6 text-white">
            <h2 className="text-xl font-semibold mb-4">渐变背景</h2>
            <p className="mb-4">
              这个卡片有渐变背景，测试颜色和渐变功能。
            </p>
            <button className="bg-white text-purple-600 px-4 py-2 rounded-md hover:bg-gray-100 transition-colors">
              白色按钮
            </button>
          </div>
          
          {/* 卡片 3 */}
          <div className="bg-green-50 border-2 border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-green-800 mb-4">边框样式</h2>
            <p className="text-green-700 mb-4">
              这个卡片测试边框和颜色变体。
            </p>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors">
              绿色按钮
            </button>
          </div>
        </div>
        
        {/* 响应式测试 */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">响应式测试</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
              <div
                key={num}
                className="bg-indigo-100 p-4 rounded-md text-center text-indigo-800 font-medium"
              >
                项目 {num}
              </div>
            ))}
          </div>
        </div>
        
        {/* 动画测试 */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">动画测试</h2>
          <div className="flex flex-wrap gap-4">
            <div className="w-16 h-16 bg-red-500 rounded-full animate-bounce"></div>
            <div className="w-16 h-16 bg-blue-500 rounded-full animate-pulse"></div>
            <div className="w-16 h-16 bg-green-500 rounded-full animate-spin"></div>
            <div className="w-16 h-16 bg-yellow-500 rounded-full animate-ping"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
