'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuthStore } from '@/store/auth-store';

interface PromptAudit {
  id: number;
  title: string;
  description: string;
  content: string;
  auditStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  auditReason?: string;
  auditTime?: string;
  createdAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
  category: {
    id: number;
    name: string;
  };
}

interface AuditStatistics {
  totalCount: number;
  pendingCount: number;
  approvedCount: number;
  rejectedCount: number;
}

export default function AdminAuditPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [prompts, setPrompts] = useState<PromptAudit[]>([]);
  const [statistics, setStatistics] = useState<AuditStatistics | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // 筛选条件
  const [filters, setFilters] = useState({
    status: 'ALL',
    keyword: '',
    startDate: '',
    endDate: ''
  });

  // 审核操作状态
  const [auditingId, setAuditingId] = useState<number | null>(null);
  const [auditAction, setAuditAction] = useState<'APPROVE' | 'REJECT' | null>(null);
  const [auditReason, setAuditReason] = useState('');
  const [showAuditModal, setShowAuditModal] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    if (user?.role !== 'ADMIN') {
      toast.error('无权访问管理员页面');
      router.push('/');
      return;
    }

    loadAuditData();
    loadStatistics();
  }, [isAuthenticated, user, currentPage, filters]);

  const loadAuditData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const tokenData = JSON.parse(token);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: '10',
        ...(filters.status !== 'ALL' && { status: filters.status }),
        ...(filters.keyword && { keyword: filters.keyword }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate })
      });

      const response = await fetch(`http://localhost:8080/api/admin/audit/prompts?${params}`, {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('获取审核数据失败');

      const result = await response.json();
      if (result.code === 200) {
        setPrompts(result.data.content || []);
        setTotalPages(result.data.totalPages || 1);
      }
    } catch (error: any) {
      console.error('加载审核数据失败:', error);
      toast.error(error.message || '加载审核数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const tokenData = JSON.parse(token);
      const response = await fetch('http://localhost:8080/api/admin/audit/statistics', {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('获取统计数据失败');

      const result = await response.json();
      if (result.code === 200) {
        setStatistics(result.data);
      }
    } catch (error: any) {
      console.error('加载统计数据失败:', error);
    }
  };

  const handleAudit = async (promptId: number, action: 'APPROVE' | 'REJECT', reason?: string) => {
    setAuditingId(promptId);
    
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/admin/audit/prompts/${promptId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          reason: reason || ''
        })
      });

      if (!response.ok) throw new Error('审核操作失败');

      const result = await response.json();
      if (result.code === 200) {
        toast.success(action === 'APPROVE' ? '审核通过' : '审核拒绝');
        loadAuditData();
        loadStatistics();
        setShowAuditModal(false);
        setAuditReason('');
      } else {
        throw new Error(result.message || '审核操作失败');
      }
    } catch (error: any) {
      console.error('审核操作失败:', error);
      toast.error(error.message || '审核操作失败');
    } finally {
      setAuditingId(null);
    }
  };

  const openAuditModal = (promptId: number, action: 'APPROVE' | 'REJECT') => {
    setAuditingId(promptId);
    setAuditAction(action);
    setShowAuditModal(true);
    setAuditReason('');
  };

  const confirmAudit = () => {
    if (auditingId && auditAction) {
      if (auditAction === 'REJECT' && !auditReason.trim()) {
        toast.error('拒绝审核必须填写原因');
        return;
      }
      handleAudit(auditingId, auditAction, auditReason);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'APPROVED': return 'text-green-600 bg-green-100';
      case 'REJECTED': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return '待审核';
      case 'APPROVED': return '已通过';
      case 'REJECTED': return '已拒绝';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">提示词审核管理</h1>
          <p className="mt-2 text-gray-600">审核用户提交的提示词内容</p>
        </div>

        {/* 统计卡片 */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">总</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总数</p>
                  <p className="text-2xl font-semibold text-gray-900">{statistics.totalCount}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">待</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">待审核</p>
                  <p className="text-2xl font-semibold text-gray-900">{statistics.pendingCount}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">过</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">已通过</p>
                  <p className="text-2xl font-semibold text-gray-900">{statistics.approvedCount}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">拒</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">已拒绝</p>
                  <p className="text-2xl font-semibold text-gray-900">{statistics.rejectedCount}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 筛选条件 */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">状态筛选</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="ALL">全部状态</option>
                <option value="PENDING">待审核</option>
                <option value="APPROVED">已通过</option>
                <option value="REJECTED">已拒绝</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">关键词搜索</label>
              <input
                type="text"
                value={filters.keyword}
                onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
                placeholder="搜索标题或描述"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => {
                setCurrentPage(1);
                loadAuditData();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              搜索
            </button>
          </div>
        </div>

        {/* 提示词列表 */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">提示词列表</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    提示词信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    作者
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    分类
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    提交时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {prompts.map((prompt) => (
                  <tr key={prompt.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="max-w-xs">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {prompt.title}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {prompt.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          {prompt.user.avatar ? (
                            <img className="h-8 w-8 rounded-full" src={prompt.user.avatar} alt="" />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm text-gray-600">
                                {prompt.user.username.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {prompt.user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{prompt.category.name}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(prompt.auditStatus)}`}>
                        {getStatusText(prompt.auditStatus)}
                      </span>
                      {prompt.auditReason && (
                        <div className="text-xs text-gray-500 mt-1">
                          原因: {prompt.auditReason}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(prompt.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {prompt.auditStatus === 'PENDING' && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => openAuditModal(prompt.id, 'APPROVE')}
                            disabled={auditingId === prompt.id}
                            className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          >
                            通过
                          </button>
                          <button
                            onClick={() => openAuditModal(prompt.id, 'REJECT')}
                            disabled={auditingId === prompt.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          >
                            拒绝
                          </button>
                        </div>
                      )}
                      <button
                        onClick={() => router.push(`/prompts/${prompt.id}`)}
                        className="text-blue-600 hover:text-blue-900 ml-2"
                      >
                        查看详情
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* 分页 */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                第 {currentPage} 页，共 {totalPages} 页
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 审核确认模态框 */}
      {showAuditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                确认{auditAction === 'APPROVE' ? '通过' : '拒绝'}审核
              </h3>
              
              {auditAction === 'REJECT' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    拒绝原因 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={auditReason}
                    onChange={(e) => setAuditReason(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入拒绝原因..."
                  />
                </div>
              )}
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowAuditModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={confirmAudit}
                  disabled={auditAction === 'REJECT' && !auditReason.trim()}
                  className={`px-4 py-2 rounded-md text-sm font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed ${
                    auditAction === 'APPROVE' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  确认{auditAction === 'APPROVE' ? '通过' : '拒绝'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
