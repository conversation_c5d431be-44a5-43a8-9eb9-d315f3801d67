'use client';

import { useState } from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/use-auth';
import { 
  Users, 
  FileText, 
  ShoppingBag, 
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Settings
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';

export default function AdminPage() {
  const {  requireAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  // 检查管理员权限
  if (!requireAdmin()) {
    return null;
  }

  const tabs = [
    { id: 'dashboard', name: '仪表盘', icon: TrendingUp },
    { id: 'prompts', name: '提示词管理', icon: FileText },
    { id: 'users', name: '用户管理', icon: Users },
    { id: 'orders', name: '订单管理', icon: ShoppingBag },
    { id: 'settings', name: '系统设置', icon: Settings },
  ];

  // 模拟统计数据
  const stats = [
    {
      name: '总用户数',
      value: '12,345',
      change: '+12%',
      changeType: 'positive',
      icon: Users,
      color: 'text-blue-600',
      bg: 'bg-blue-100',
    },
    {
      name: '总提示词',
      value: '8,901',
      change: '+8%',
      changeType: 'positive',
      icon: FileText,
      color: 'text-green-600',
      bg: 'bg-green-100',
    },
    {
      name: '待审核',
      value: '23',
      change: '-5%',
      changeType: 'negative',
      icon: Clock,
      color: 'text-yellow-600',
      bg: 'bg-yellow-100',
    },
    {
      name: '总收入',
      value: formatPrice(156789),
      change: '+15%',
      changeType: 'positive',
      icon: TrendingUp,
      color: 'text-purple-600',
      bg: 'bg-purple-100',
    },
  ];

  // 模拟待审核提示词
  const pendingPrompts = [
    {
      id: 1,
      title: '专业营销文案生成器',
      creator: '张创作者',
      category: '文案写作',
      price: 29.9,
      createdAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 2,
      title: 'AI绘画风格指导',
      creator: '李艺术家',
      category: 'AI绘画',
      price: 19.9,
      createdAt: '2024-01-15T09:15:00Z',
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container-custom py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">管理后台</h1>
          <p className="text-muted-foreground">
            系统管理和数据监控
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-4">
          {/* 侧边栏导航 */}
          <div className="lg:col-span-1">
            <div className="bg-card border rounded-lg p-6">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="lg:col-span-3">
            {activeTab === 'dashboard' && (
              <div className="space-y-8">
                {/* 统计卡片 */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {stats.map((stat, index) => {
                    const Icon = stat.icon;
                    return (
                      <div key={index} className="bg-card border rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">{stat.name}</p>
                            <p className="text-2xl font-bold">{stat.value}</p>
                            <p className={`text-xs ${
                              stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {stat.change} 较上月
                            </p>
                          </div>
                          <div className={`p-3 rounded-lg ${stat.bg}`}>
                            <Icon className={`h-6 w-6 ${stat.color}`} />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* 待审核提示词 */}
                <div className="bg-card border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">待审核提示词</h2>
                    <Button variant="outline" size="sm">
                      查看全部
                    </Button>
                  </div>
                  
                  <div className="space-y-4">
                    {pendingPrompts.map((prompt) => (
                      <div key={prompt.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <h3 className="font-medium">{prompt.title}</h3>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                            <span>创作者: {prompt.creator}</span>
                            <span>分类: {prompt.category}</span>
                            <span>价格: {formatPrice(prompt.price)}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            查看
                          </Button>
                          <Button size="sm" variant="default">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            通过
                          </Button>
                          <Button size="sm" variant="destructive">
                            <XCircle className="h-4 w-4 mr-1" />
                            拒绝
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 最近活动 */}
                <div className="bg-card border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-6">最近活动</h2>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">审核通过提示词</p>
                        <p className="text-sm text-muted-foreground">专业营销文案生成器</p>
                      </div>
                      <span className="text-sm text-muted-foreground">2小时前</span>
                    </div>
                    
                    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">新用户注册</p>
                        <p className="text-sm text-muted-foreground">用户123456</p>
                      </div>
                      <span className="text-sm text-muted-foreground">3小时前</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'prompts' && (
              <div className="bg-card border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold">提示词管理</h2>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">筛选</Button>
                    <Button variant="outline" size="sm">导出</Button>
                  </div>
                </div>
                
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">提示词管理功能开发中...</p>
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="bg-card border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold">用户管理</h2>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">筛选</Button>
                    <Button variant="outline" size="sm">导出</Button>
                  </div>
                </div>
                
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">用户管理功能开发中...</p>
                </div>
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="bg-card border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold">订单管理</h2>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">筛选</Button>
                    <Button variant="outline" size="sm">导出</Button>
                  </div>
                </div>
                
                <div className="text-center py-12">
                  <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">订单管理功能开发中...</p>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="bg-card border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-6">系统设置</h2>
                
                <div className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="p-4 border rounded-lg">
                      <h3 className="font-medium mb-2">网站配置</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        网站基本信息和SEO设置
                      </p>
                      <Button variant="outline" size="sm">配置</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <h3 className="font-medium mb-2">支付配置</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        支付宝和微信支付配置
                      </p>
                      <Button variant="outline" size="sm">配置</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <h3 className="font-medium mb-2">邮件配置</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        SMTP邮件服务配置
                      </p>
                      <Button variant="outline" size="sm">配置</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <h3 className="font-medium mb-2">存储配置</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        文件存储和CDN配置
                      </p>
                      <Button variant="outline" size="sm">配置</Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
