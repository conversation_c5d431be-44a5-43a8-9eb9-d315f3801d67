'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { usePrompt } from '@/hooks/use-prompts';
import { useAuth } from '@/hooks/use-auth';
import { formatPrice, formatRelativeTime, copyToClipboard } from '@/lib/utils';
import { purchasePrompt, OrderApi } from '@/lib/order-api';
import { SecureImage } from '@/components/ui/secure-image';
import {
  Heart,
  Download,
  Eye,
  Star,
  ShoppingCart,
  Copy,
  Share2,
  User,
  Calendar,
  Tag,
  Zap
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

export default function PromptDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated, requireAuth } = useAuth();
  
  const promptId = Number(params.id);
  const { data: prompt, isLoading, error } = usePrompt(promptId);
  
  const [isLiked, setIsLiked] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [isPurchased, setIsPurchased] = useState(false);
  const [checkingPurchase, setCheckingPurchase] = useState(false);

  useEffect(() => {
    if (prompt) {
      setIsLiked(prompt.isLiked || false);
    }
  }, [prompt]);

  // 检查购买状态
  useEffect(() => {
    if (prompt && isAuthenticated && !prompt.isFree) {
      checkPurchaseStatus();
    }
  }, [prompt, isAuthenticated]);

  const checkPurchaseStatus = async () => {
    try {
      setCheckingPurchase(true);
      const purchased = await OrderApi.checkPurchase(promptId);
      setIsPurchased(purchased);
    } catch (error) {
      console.error('检查购买状态失败:', error);
    } finally {
      setCheckingPurchase(false);
    }
  };

  const handleLike = async () => {
    if (!requireAuth()) return;
    
    try {
      // TODO: 实现点赞API
      setIsLiked(!isLiked);
      toast.success(isLiked ? '取消收藏' : '收藏成功');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handlePurchase = async () => {
    if (!requireAuth()) return;

    try {
      setIsPurchasing(true);

      // 只处理付费提示词的购买
      if (!prompt?.isFree) {
        // 付费提示词创建订单并跳转支付
        const paymentUrl = await purchasePrompt(Number(promptId), 'ALIPAY');

        // 跳转到支付页面
        window.location.href = paymentUrl;
      }
    } catch (error: any) {
      console.error('购买失败:', error);
      toast.error(error.message || '购买失败，请重试');
    } finally {
      setIsPurchasing(false);
    }
  };

  const handleCopyContent = async () => {
    if (!prompt?.content) return;
    
    const success = await copyToClipboard(prompt.content);
    if (success) {
      toast.success('内容已复制到剪贴板');
    } else {
      toast.error('复制失败');
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: prompt?.title,
          text: prompt?.description,
          url: window.location.href,
        });
      } catch (error) {
        // 用户取消分享
      }
    } else {
      // 降级到复制链接
      const success = await copyToClipboard(window.location.href);
      if (success) {
        toast.success('链接已复制到剪贴板');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center py-20">
          <LoadingSpinner size="lg" />
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !prompt) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container-custom py-20 text-center">
          <h1 className="text-2xl font-bold mb-4">提示词不存在</h1>
          <p className="text-muted-foreground mb-8">
            您访问的提示词可能已被删除或不存在
          </p>
          <Button onClick={() => router.push('/browse')}>
            浏览其他提示词
          </Button>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container-custom py-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-foreground">首页</Link>
          <span>/</span>
          <Link href="/browse" className="hover:text-foreground">浏览提示词</Link>
          <span>/</span>
          {prompt.categoryName && (
            <>
              <Link 
                href={`/browse?categoryId=${prompt.categoryId}`}
                className="hover:text-foreground"
              >
                {prompt.categoryName}
              </Link>
              <span>/</span>
            </>
          )}
          <span className="text-foreground">{prompt.title}</span>
        </nav>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 标题和基本信息 */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h1 className="text-3xl font-bold">{prompt.title}</h1>
                    {prompt.isFeatured && (
                      <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                        精选
                      </span>
                    )}
                    {prompt.isFree && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        免费
                      </span>
                    )}
                  </div>
                  
                  <p className="text-muted-foreground text-lg mb-4">
                    {prompt.description}
                  </p>

                  {/* 统计信息 */}
                  <div className="flex items-center gap-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{prompt.viewCount} 浏览</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>{prompt.downloadCount} 下载</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="h-4 w-4" />
                      <span>{prompt.likeCount} 收藏</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-current text-yellow-400" />
                      <span>{prompt.ratingAvg.toFixed(1)} ({prompt.ratingCount} 评价)</span>
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleLike}
                    className={isLiked ? 'text-red-500' : ''}
                  >
                    <Heart className={`h-5 w-5 ${isLiked ? 'fill-current' : ''}`} />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={handleShare}>
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* 预览图片 */}
            {prompt.previewImages && prompt.previewImages.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">预览图片</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  {prompt.previewImages.map((image, index) => (
                    <div key={index} className="aspect-video bg-muted rounded-lg overflow-hidden">
                      <SecureImage
                        src={image}
                        alt={`预览图 ${index + 1}`}
                        className="w-full h-full object-cover"
                        expireMinutes={120}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 提示词内容 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">提示词内容</h2>
                {(prompt.isFree || isPurchased) && (
                  <Button variant="outline" size="sm" onClick={handleCopyContent}>
                    <Copy className="h-4 w-4 mr-2" />
                    复制内容
                  </Button>
                )}
              </div>
              
              <div className="bg-muted rounded-lg p-6">
                {prompt.isFree ? (
                  // 免费提示词直接显示内容
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {prompt.content}
                  </pre>
                ) : isPurchased ? (
                  // 已购买的付费提示词显示内容
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {prompt.content}
                  </pre>
                ) : (
                  // 未购买的付费提示词显示购买提示
                  <div className="text-center py-8">
                    <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">
                      购买后即可查看完整的提示词内容
                    </p>
                    {checkingPurchase ? (
                      <Button disabled>
                        检查购买状态...
                      </Button>
                    ) : (
                      <Button onClick={handlePurchase} loading={isPurchasing}>
                        购买 {formatPrice(prompt.price)}
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 使用说明 */}
            {prompt.usageInstructions && (
              <div>
                <h2 className="text-xl font-semibold mb-4">使用说明</h2>
                <div className="bg-card border rounded-lg p-6">
                  <pre className="whitespace-pre-wrap text-sm">
                    {prompt.usageInstructions}
                  </pre>
                </div>
              </div>
            )}

            {/* 示例输出 */}
            {prompt.exampleOutputs && prompt.exampleOutputs.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">示例输出</h2>
                <div className="space-y-4">
                  {prompt.exampleOutputs.map((output, index) => (
                    <div key={index} className="bg-card border rounded-lg p-6">
                      <h3 className="font-medium mb-2">示例 {index + 1}</h3>
                      <pre className="whitespace-pre-wrap text-sm text-muted-foreground">
                        {output}
                      </pre>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 购买卡片 */}
            <div className="bg-card border rounded-lg p-6 sticky top-8">
              <div className="text-center mb-6">
                {prompt.isFree ? (
                  <div className="text-2xl font-bold text-green-600 mb-2">免费</div>
                ) : (
                  <div>
                    <div className="text-3xl font-bold text-primary mb-2">
                      {formatPrice(prompt.price)}
                    </div>
                    {prompt.originalPrice && prompt.originalPrice > prompt.price && (
                      <div className="text-sm text-muted-foreground line-through">
                        原价 {formatPrice(prompt.originalPrice)}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {prompt.isFree ? (
                // 免费提示词不显示按钮，显示免费标识
                <div className="text-center py-4">
                  <div className="text-lg font-semibold text-green-600 mb-2">
                    ✨ 免费使用
                  </div>
                  <div className="text-sm text-muted-foreground">
                    此提示词完全免费，可直接查看和使用
                  </div>
                </div>
              ) : checkingPurchase ? (
                <Button className="w-full" disabled>
                  检查购买状态...
                </Button>
              ) : isPurchased ? (
                <Button className="w-full" disabled>
                  已购买
                </Button>
              ) : (
                <Button
                  className="w-full"
                  onClick={handlePurchase}
                  loading={isPurchasing}
                >
                  立即购买
                </Button>
              )}

              {!prompt.isFree && (
                <div className="mt-4 text-xs text-muted-foreground text-center">
                  购买后可永久使用，支持商业用途
                </div>
              )}
            </div>

            {/* 创作者信息 */}
            {prompt.creator && (
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold mb-4">创作者</h3>
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                    {prompt.creator.avatarUrl ? (
                      <SecureImage
                        src={prompt.creator.avatarUrl}
                        alt={prompt.creator.username}
                        className="w-full h-full rounded-full object-cover"
                        expireMinutes={240}
                      />
                    ) : (
                      <User className="h-6 w-6 text-white" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{prompt.creator.username}</div>
                    <div className="text-sm text-muted-foreground">
                      {prompt.creator.followerCount} 关注者
                    </div>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/creator/${prompt.creator.id}`}>
                    查看主页
                  </Link>
                </Button>
              </div>
            )}

            {/* 基本信息 */}
            <div className="bg-card border rounded-lg p-6">
              <h3 className="font-semibold mb-4">基本信息</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">AI模型:</span>
                  <span>{prompt.aiModel}</span>
                </div>
                
                {prompt.categoryName && (
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">分类:</span>
                    <Link 
                      href={`/browse?categoryId=${prompt.categoryId}`}
                      className="text-primary hover:underline"
                    >
                      {prompt.categoryName}
                    </Link>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">发布时间:</span>
                  <span>{formatRelativeTime(prompt.createdAt)}</span>
                </div>
              </div>
            </div>

            {/* 标签 */}
            {prompt.tags && prompt.tags.length > 0 && (
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold mb-4">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {prompt.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-muted text-muted-foreground px-2 py-1 rounded text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
