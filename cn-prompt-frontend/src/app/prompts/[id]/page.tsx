'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/use-auth';
import { FavoriteButton } from '@/components/prompt/favorite-button';
import { EditButton } from '@/components/prompt/edit-button';
import { ReviewSection } from '@/components/prompt/review-section';
import { 
  ArrowLeft, 
  User, 
  Calendar, 
  Tag, 
  DollarSign, 
  Eye, 
  Heart, 
  Star,
  Copy,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

interface PromptDetail {
  id: number;
  title: string;
  description: string;
  content: string;
  price: number;
  viewCount: number;
  favoriteCount: number;
  averageRating: number;
  reviewCount: number;
  auditStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  auditReason?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
  category: {
    id: number;
    name: string;
  };
}

export default function PromptDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [prompt, setPrompt] = useState<PromptDetail | null>(null);
  const [copied, setCopied] = useState(false);

  const promptId = parseInt(params.id as string);

  useEffect(() => {
    if (promptId) {
      loadPromptDetail();
    }
  }, [promptId]);

  const loadPromptDetail = async () => {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 如果已登录，添加认证头
      if (isAuthenticated) {
        const token = localStorage.getItem('token');
        if (token) {
          const tokenData = JSON.parse(token);
          headers['Authorization'] = `Bearer ${tokenData.accessToken}`;
        }
      }

      const response = await fetch(`http://localhost:8080/api/prompts/${promptId}`, {
        headers
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('提示词不存在');
        }
        throw new Error('获取提示词详情失败');
      }

      const result = await response.json();
      if (result.code === 200) {
        setPrompt(result.data);
      } else {
        throw new Error(result.message || '获取提示词详情失败');
      }
    } catch (error: any) {
      console.error('加载提示词详情失败:', error);
      toast.error(error.message || '加载提示词详情失败');
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  const copyContent = async () => {
    if (!prompt) return;

    try {
      await navigator.clipboard.writeText(prompt.content);
      setCopied(true);
      toast.success('内容已复制到剪贴板');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">
          {rating.toFixed(1)} ({prompt?.reviewCount || 0})
        </span>
      </div>
    );
  };

  const getStatusBadge = (status: string, reason?: string) => {
    switch (status) {
      case 'PENDING':
        return (
          <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
            <Clock className="w-4 h-4" />
            <span>审核中</span>
          </div>
        );
      case 'APPROVED':
        return (
          <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
            <CheckCircle className="w-4 h-4" />
            <span>已通过</span>
          </div>
        );
      case 'REJECTED':
        return (
          <div className="flex flex-col">
            <div className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm">
              <XCircle className="w-4 h-4" />
              <span>已拒绝</span>
            </div>
            {reason && (
              <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                拒绝原因: {reason}
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">提示词不存在</p>
          <button 
            onClick={() => router.push('/')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>返回</span>
          </button>
        </div>

        {/* 主要内容 */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* 头部信息 */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{prompt.title}</h1>
                <p className="text-gray-600 mb-4">{prompt.description}</p>
                
                {/* 审核状态 */}
                <div className="mb-4">
                  {getStatusBadge(prompt.auditStatus, prompt.auditReason)}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <FavoriteButton promptId={prompt.id} showText />
                <EditButton 
                  promptId={prompt.id} 
                  authorId={prompt.user.id}
                  auditStatus={prompt.auditStatus}
                  showText 
                />
              </div>
            </div>

            {/* 作者信息 */}
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center space-x-2">
                {prompt.user.avatar ? (
                  <img
                    src={prompt.user.avatar}
                    alt={prompt.user.username}
                    className="w-8 h-8 rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <User className="w-4 h-4 text-gray-600" />
                  </div>
                )}
                <span className="font-medium text-gray-900">{prompt.user.username}</span>
              </div>
              
              <div className="flex items-center space-x-1 text-gray-500">
                <Calendar className="w-4 h-4" />
                <span className="text-sm">{new Date(prompt.createdAt).toLocaleDateString()}</span>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{prompt.viewCount} 浏览</span>
              </div>
              <div className="flex items-center space-x-1">
                <Heart className="w-4 h-4" />
                <span>{prompt.favoriteCount} 收藏</span>
              </div>
              <div className="flex items-center space-x-1">
                <Tag className="w-4 h-4" />
                <span>{prompt.category.name}</span>
              </div>
            </div>

            {/* 评分 */}
            <div className="mt-4">
              {renderStars(prompt.averageRating)}
            </div>

            {/* 价格 */}
            <div className="mt-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="text-2xl font-bold text-green-600">
                  {prompt.price === 0 ? '免费' : `¥${prompt.price}`}
                </span>
              </div>
            </div>
          </div>

          {/* 提示词内容 */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">提示词内容</h2>
              <button
                onClick={copyContent}
                className="flex items-center space-x-2 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <Copy className="w-4 h-4" />
                <span className="text-sm">{copied ? '已复制' : '复制'}</span>
              </button>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                {prompt.content}
              </pre>
            </div>
          </div>
        </div>

        {/* 评价区域 */}
        <div className="mt-8">
          <ReviewSection promptId={prompt.id} />
        </div>
      </div>
    </div>
  );
}
