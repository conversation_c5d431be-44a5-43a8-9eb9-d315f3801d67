'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/use-auth';
import { ArrowLeft, Save, AlertTriangle } from 'lucide-react';

interface Category {
  id: number;
  name: string;
}

interface PromptEditData {
  id: number;
  title: string;
  description: string;
  content: string;
  price: number;
  categoryId: number;
  auditStatus: string;
  canEdit: boolean;
}

export default function PromptEditPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, requireAuth } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [prompt, setPrompt] = useState<PromptEditData | null>(null);
  
  // 表单数据
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    price: 0,
    categoryId: 0
  });

  const promptId = params.id as string;

  useEffect(() => {
    if (!requireAuth()) return;
    loadPromptData();
    loadCategories();
  }, [promptId]);

  const loadPromptData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/prompts/${promptId}/edit`, {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('您没有权限编辑此提示词');
        }
        throw new Error('获取提示词信息失败');
      }

      const result = await response.json();
      if (result.code === 200) {
        const data = result.data;
        setPrompt(data);
        setFormData({
          title: data.title,
          description: data.description,
          content: data.content,
          price: data.price,
          categoryId: data.categoryId
        });

        if (!data.canEdit) {
          toast.error('您没有权限编辑此提示词');
          router.push(`/prompts/${promptId}`);
          return;
        }
      } else {
        throw new Error(result.message || '获取提示词信息失败');
      }
    } catch (error: any) {
      console.error('加载提示词数据失败:', error);
      toast.error(error.message || '加载提示词数据失败');
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/categories');
      if (!response.ok) throw new Error('获取分类失败');

      const result = await response.json();
      if (result.code === 200) {
        setCategories(result.data || []);
      }
    } catch (error: any) {
      console.error('加载分类失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('请输入提示词标题');
      return;
    }
    
    if (!formData.description.trim()) {
      toast.error('请输入提示词描述');
      return;
    }
    
    if (!formData.content.trim()) {
      toast.error('请输入提示词内容');
      return;
    }
    
    if (formData.categoryId === 0) {
      toast.error('请选择分类');
      return;
    }

    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/prompts/${promptId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) throw new Error('更新失败');

      const result = await response.json();
      if (result.code === 200) {
        toast.success('提示词更新成功，将重新进入审核');
        router.push(`/prompts/${promptId}`);
      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error: any) {
      console.error('更新提示词失败:', error);
      toast.error(error.message || '更新失败');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">提示词不存在或无权访问</p>
          <button 
            onClick={() => router.push('/')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>返回</span>
            </button>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900">编辑提示词</h1>
          <p className="mt-2 text-gray-600">修改您的提示词内容</p>
        </div>

        {/* 重新审核提示 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">重要提示</h3>
              <p className="text-sm text-yellow-700 mt-1">
                修改提示词后，将重新进入审核流程。在审核通过之前，用户将无法看到更新后的内容。
              </p>
            </div>
          </div>
        </div>

        {/* 编辑表单 */}
        <div className="bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* 标题 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                标题 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入提示词标题"
                maxLength={100}
              />
              <div className="text-right text-sm text-gray-500 mt-1">
                {formData.title.length}/100
              </div>
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                描述 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入提示词描述"
                maxLength={500}
              />
              <div className="text-right text-sm text-gray-500 mt-1">
                {formData.description.length}/500
              </div>
            </div>

            {/* 内容 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                提示词内容 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono"
                placeholder="请输入提示词内容"
                maxLength={5000}
              />
              <div className="text-right text-sm text-gray-500 mt-1">
                {formData.content.length}/5000
              </div>
            </div>

            {/* 分类 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分类 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.categoryId}
                onChange={(e) => handleInputChange('categoryId', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={0}>请选择分类</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 价格 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                价格 (元)
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
              <p className="text-sm text-gray-500 mt-1">
                设置为 0 表示免费提示词
              </p>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-4 h-4" />
                <span>{saving ? '保存中...' : '保存修改'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
