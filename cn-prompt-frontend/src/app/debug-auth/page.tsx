'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function DebugAuthPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [localStorageData, setLocalStorageData] = useState<any>({});
  const [apiResponse, setApiResponse] = useState<any>(null);

  useEffect(() => {
    // 读取localStorage中的所有认证相关数据
    if (typeof window !== 'undefined') {
      const data = {
        token: localStorage.getItem('token'),
        accessToken: localStorage.getItem('accessToken'),
        refreshToken: localStorage.getItem('refreshToken'),
        user: localStorage.getItem('user'),
        authStore: localStorage.getItem('auth-store'),
      };
      setLocalStorageData(data);
    }
  }, []);

  const testApiCall = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const tokenInfo = JSON.parse(token);
        const response = await fetch('http://localhost:8080/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${tokenInfo.accessToken}`,
          },
        });
        const result = await response.json();
        setApiResponse(result);
      } else {
        setApiResponse({ error: 'No token found' });
      }
    } catch (error) {
      setApiResponse({ error: error.message });
    }
  };

  const simulateLogin = () => {
    // 使用真实的登录响应数据
    const testToken = {
      accessToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU1NzA4LCJleHAiOjE3NTM0NTkzMDgsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiVVNFUiIsInR5cGUiOiJhY2Nlc3MifQ.Q1vCjQXORUPrPUQbuRi7GBTdaQClGa8zcd7THb5n6Q0',
      refreshToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU1NzA4LCJleHAiOjE3NTYwNDc3MDgsInR5cGUiOiJyZWZyZXNoIn0.zQciSjScOTLyDFJJSqLj0umu525L6HfEs7WjoYJkYs0',
      expiresAt: Date.now() + 3600 * 1000,
    };

    const testUser = {
      id: 6,
      username: 'testuser',
      email: '<EMAIL>',
      bio: '这是测试用户',
      role: 'USER',
      status: 'ACTIVE',
      emailVerified: true,
      phoneVerified: false,
      totalEarnings: 0,
      totalSpent: 0,
      followerCount: 0,
      followingCount: 0,
      createdAt: '2025-07-25T22:37:06',
      updatedAt: '2025-07-25T22:48:36',
      lastLoginAt: '2025-07-25T23:01:48.685804'
    };

    localStorage.setItem('token', JSON.stringify(testToken));
    
    // 更新Zustand store
    const authStore = {
      state: {
        user: testUser,
        isAuthenticated: true,
      },
      version: 0,
    };
    localStorage.setItem('auth-store', JSON.stringify(authStore));
    
    // 刷新页面以重新加载状态
    window.location.reload();
  };

  const clearAuth = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('auth-store');
    window.location.reload();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>认证状态调试</CardTitle>
            <CardDescription>检查当前的认证状态和数据</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">useAuth Hook 状态:</h3>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify({ user, isAuthenticated, isLoading }, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">localStorage 数据:</h3>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify(localStorageData, null, 2)}
              </pre>
            </div>

            {apiResponse && (
              <div>
                <h3 className="font-semibold mb-2">API 响应:</h3>
                <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(apiResponse, null, 2)}
                </pre>
              </div>
            )}

            <div className="flex gap-4">
              <Button onClick={testApiCall}>测试 API 调用</Button>
              <Button onClick={simulateLogin} variant="outline">模拟登录</Button>
              <Button onClick={clearAuth} variant="destructive">清除认证</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>问题诊断</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${isAuthenticated ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>认证状态: {isAuthenticated ? '已登录' : '未登录'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${user ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>用户信息: {user ? '已加载' : '未加载'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${localStorageData.token ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>Token: {localStorageData.token ? '存在' : '不存在'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${localStorageData.authStore ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>Auth Store: {localStorageData.authStore ? '存在' : '不存在'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
