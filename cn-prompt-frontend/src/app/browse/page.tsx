'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { PromptCard } from '@/components/prompt/prompt-card';
import { SearchFilters } from '@/components/prompt/search-filters';
import { usePrompts } from '@/hooks/use-prompts';
import { Search, Filter, Grid, List } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useDebounce } from 'use-debounce';

export default function BrowsePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // 搜索状态
  const [keyword, setKeyword] = useState(searchParams.get('keyword') || '');
  const [categoryId, setCategoryId] = useState<number | null>(
    searchParams.get('categoryId') ? Number(searchParams.get('categoryId')) : null
  );
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  
  // 防抖搜索
  const [debouncedKeyword] = useDebounce(keyword, 500);
  
  // 获取提示词数据
  const {
    data: promptsData,
    isLoading,
    error,
    refetch
  } = usePrompts({
    keyword: debouncedKeyword,
    categoryId,
    sortBy,
    page: 1,
    size: 20
  });

  // 更新URL参数
  useEffect(() => {
    const params = new URLSearchParams();
    if (debouncedKeyword) params.set('keyword', debouncedKeyword);
    if (categoryId) params.set('categoryId', categoryId.toString());
    if (sortBy !== 'newest') params.set('sortBy', sortBy);
    
    const newUrl = `/browse${params.toString() ? `?${params.toString()}` : ''}`;
    router.replace(newUrl, { scroll: false });
  }, [debouncedKeyword, categoryId, sortBy, router]);

  const handleSearch = (value: string) => {
    setKeyword(value);
  };

  const handleFilterChange = (filters: any) => {
    setCategoryId(filters.categoryId);
    setSortBy(filters.sortBy);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container-custom py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">浏览提示词</h1>
          <p className="text-muted-foreground">
            发现高质量的AI提示词，提升您的创作效率
          </p>
        </div>

        {/* 搜索和过滤器 */}
        <div className="mb-8 space-y-4">
          {/* 搜索栏 */}
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索提示词..."
                value={keyword}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              筛选
            </Button>
          </div>

          {/* 过滤器 */}
          {showFilters && (
            <div className="border rounded-lg p-4 bg-card">
              <SearchFilters
                initialFilters={{
                  categoryId,
                  sortBy
                }}
                onChange={handleFilterChange}
              />
            </div>
          )}
        </div>

        {/* 工具栏 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">
              {promptsData?.total ? `共找到 ${promptsData.total} 个提示词` : ''}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {/* 视图切换 */}
            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground mb-4">加载失败，请重试</p>
            <Button onClick={() => refetch()}>重新加载</Button>
          </div>
        ) : !promptsData?.data?.length ? (
          <div className="text-center py-12">
            <div className="mb-4">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">没有找到相关提示词</h3>
              <p className="text-muted-foreground">
                尝试调整搜索关键词或筛选条件
              </p>
            </div>
            <Button variant="outline" onClick={() => {
              setKeyword('');
              setCategoryId(null);
              setSortBy('newest');
            }}>
              清除筛选条件
            </Button>
          </div>
        ) : (
          <>
            {/* 提示词列表 */}
            <div className={
              viewMode === 'grid' 
                ? 'grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                : 'space-y-4'
            }>
              {promptsData.data.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  prompt={prompt}
                  viewMode={viewMode}
                />
              ))}
            </div>

            {/* 分页 */}
            {promptsData.totalPages > 1 && (
              <div className="mt-12 flex justify-center">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    disabled={promptsData.page <= 1}
                  >
                    上一页
                  </Button>
                  
                  <span className="px-4 py-2 text-sm">
                    第 {promptsData.page} 页，共 {promptsData.totalPages} 页
                  </span>
                  
                  <Button
                    variant="outline"
                    disabled={!promptsData.hasNext}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </main>
      
      <Footer />
    </div>
  );
}
