'use client';

import { useState } from 'react';
import { PromptApi } from '@/lib/prompt-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-hot-toast';

export default function TestCreatePage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testCreatePrompt = async () => {
    try {
      setIsSubmitting(true);
      setResult(null);

      const testData = {
        title: '测试提示词 - ' + new Date().toLocaleTimeString(),
        description: '这是一个通过前端API创建的测试提示词',
        content: '请帮我写一篇关于{主题}的文章，要求：\n1. 字数在800-1000字之间\n2. 结构清晰，逻辑性强\n3. 语言生动有趣',
        aiModel: 'ChatGPT',
        categoryId: 1,
        price: 9.99,
        originalPrice: 19.99,
        previewImages: [],
        exampleOutputs: ['这是一个示例输出'],
        usageInstructions: '将{主题}替换为您想要的文章主题',
        tags: ['写作', '文章', '测试'],
        isFree: false,
      };

      console.log('Creating prompt with data:', testData);
      const createdPrompt = await PromptApi.create(testData);
      console.log('Prompt created successfully:', createdPrompt);
      
      setResult({
        type: 'success',
        action: 'create',
        data: createdPrompt
      });

      toast.success('提示词创建成功！');

      // 尝试发布
      try {
        console.log('Publishing prompt:', createdPrompt.id);
        await PromptApi.publish(createdPrompt.id);
        console.log('Prompt published successfully');
        
        setResult(prev => ({
          ...prev,
          published: true
        }));

        toast.success('提示词发布成功！');
      } catch (publishError) {
        console.error('发布失败:', publishError);
        setResult(prev => ({
          ...prev,
          publishError: publishError.message
        }));
        toast.error('发布失败: ' + publishError.message);
      }

    } catch (error: any) {
      console.error('创建失败:', error);
      setResult({
        type: 'error',
        action: 'create',
        error: error.message
      });
      toast.error('创建失败: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const testGetUserPrompts = async () => {
    try {
      setIsSubmitting(true);
      setResult(null);

      console.log('Getting user prompts for user 6...');
      const userPrompts = await PromptApi.getUserPrompts(6);
      console.log('User prompts:', userPrompts);
      
      setResult({
        type: 'success',
        action: 'getUserPrompts',
        data: userPrompts
      });

      toast.success('获取用户提示词成功！');

    } catch (error: any) {
      console.error('获取失败:', error);
      setResult({
        type: 'error',
        action: 'getUserPrompts',
        error: error.message
      });
      toast.error('获取失败: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const testSearchPrompts = async () => {
    try {
      setIsSubmitting(true);
      setResult(null);

      console.log('Searching prompts...');
      const searchResults = await PromptApi.search({
        keyword: '测试',
        page: 1,
        size: 10
      });
      console.log('Search results:', searchResults);
      
      setResult({
        type: 'success',
        action: 'search',
        data: searchResults
      });

      toast.success('搜索提示词成功！');

    } catch (error: any) {
      console.error('搜索失败:', error);
      setResult({
        type: 'error',
        action: 'search',
        error: error.message
      });
      toast.error('搜索失败: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>提示词API测试</CardTitle>
          <CardDescription>测试前端提示词API调用功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 测试按钮 */}
          <div className="grid gap-4 md:grid-cols-3">
            <Button
              onClick={testCreatePrompt}
              disabled={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? '创建中...' : '测试创建提示词'}
            </Button>
            
            <Button
              onClick={testGetUserPrompts}
              disabled={isSubmitting}
              variant="outline"
              className="w-full"
            >
              {isSubmitting ? '获取中...' : '获取用户提示词'}
            </Button>

            <Button
              onClick={testSearchPrompts}
              disabled={isSubmitting}
              variant="secondary"
              className="w-full"
            >
              {isSubmitting ? '搜索中...' : '搜索提示词'}
            </Button>
          </div>

          {/* 结果显示 */}
          {result && (
            <div className="mt-6">
              <h3 className="font-semibold mb-2">
                {result.action === 'create' && '创建结果:'}
                {result.action === 'getUserPrompts' && '用户提示词:'}
                {result.action === 'search' && '搜索结果:'}
              </h3>
              
              <div className={`p-4 rounded-md ${
                result.type === 'success' 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {result.type === 'success' ? (
                  <div>
                    <p className="text-green-700 font-medium mb-2">✅ 操作成功</p>
                    {result.published && (
                      <p className="text-green-600 text-sm mb-2">✅ 发布成功</p>
                    )}
                    {result.publishError && (
                      <p className="text-orange-600 text-sm mb-2">⚠️ 发布失败: {result.publishError}</p>
                    )}
                    <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-96">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div>
                    <p className="text-red-700 font-medium mb-2">❌ 操作失败</p>
                    <p className="text-red-600 text-sm">{result.error}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 说明 */}
          <div className="text-sm text-muted-foreground">
            <h4 className="font-medium mb-2">测试说明:</h4>
            <ul className="space-y-1">
              <li>• <strong>创建提示词</strong>: 测试创建新提示词并自动发布</li>
              <li>• <strong>获取用户提示词</strong>: 获取用户ID=6的所有提示词</li>
              <li>• <strong>搜索提示词</strong>: 搜索包含"测试"关键词的提示词</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
