import { Suspense } from 'react';
import { Hero } from '@/components/sections/hero';
import { FeaturedPrompts } from '@/components/sections/featured-prompts';
import { Categories } from '@/components/sections/categories';
import { HowItWorks } from '@/components/sections/how-it-works';
import { Stats } from '@/components/sections/stats';
import { Testimonials } from '@/components/sections/testimonials';
import { CTA } from '@/components/sections/cta';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        {/* 英雄区域 */}
        <Hero />
        
        {/* 统计数据 */}
        <Suspense fallback={<LoadingSpinner />}>
          <Stats />
        </Suspense>
        
        {/* 精选提示词 */}
        <Suspense fallback={<LoadingSpinner />}>
          <FeaturedPrompts />
        </Suspense>
        
        {/* 分类展示 */}
        <Suspense fallback={<LoadingSpinner />}>
          <Categories />
        </Suspense>
        
        {/* 如何使用 */}
        <HowItWorks />
        
        {/* 用户评价 */}
        <Suspense fallback={<LoadingSpinner />}>
          <Testimonials />
        </Suspense>
        
        {/* 行动号召 */}
        <CTA />
      </main>
      
      <Footer />
    </div>
  );
}
