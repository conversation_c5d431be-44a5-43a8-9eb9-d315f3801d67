'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { authApi, VerificationType } from '@/lib/auth-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestLoginPage() {
  const { user, isAuthenticated, emailLogin, logout } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const sendCode = async () => {
    try {
      setLoading(true);
      setMessage('');
      await authApi.sendVerificationCode({
        email,
        type: VerificationType.LOGIN
      });
      setMessage('验证码发送成功，请查看后端日志获取验证码');
    } catch (error: any) {
      setMessage(`发送失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    try {
      setLoading(true);
      setMessage('');
      await emailLogin(email, code);
      setMessage('登录成功！');
    } catch (error: any) {
      setMessage(`登录失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setMessage('登出成功！');
    } catch (error: any) {
      setMessage(`登出失败: ${error.message}`);
    }
  };

  const testDirectApiCall = async () => {
    try {
      setLoading(true);
      setMessage('');
      
      // 直接调用API测试
      const response = await fetch('http://localhost:8080/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          code
        }),
      });

      const result = await response.json();
      
      if (result.code === 200) {
        // 手动设置localStorage来测试
        const tokenInfo = {
          accessToken: result.data.accessToken,
          refreshToken: result.data.refreshToken,
          expiresAt: Date.now() + result.data.expiresIn * 1000,
        };
        
        localStorage.setItem('token', JSON.stringify(tokenInfo));
        
        // 手动设置Zustand store
        const authStore = {
          state: {
            user: result.data.user,
            isAuthenticated: true,
          },
          version: 0,
        };
        localStorage.setItem('auth-store', JSON.stringify(authStore));
        
        setMessage('直接API调用成功！请刷新页面查看状态变化');
      } else {
        setMessage(`API调用失败: ${result.message}`);
      }
    } catch (error: any) {
      setMessage(`API调用失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>邮箱登录测试</CardTitle>
          <CardDescription>测试前端邮箱登录功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前状态 */}
          <div className="p-4 bg-muted rounded-md">
            <h3 className="font-semibold mb-2">当前状态:</h3>
            <p>登录状态: {isAuthenticated ? '已登录' : '未登录'}</p>
            {user && (
              <div className="mt-2">
                <p>用户ID: {user.id}</p>
                <p>用户名: {user.username}</p>
                <p>邮箱: {user.email}</p>
              </div>
            )}
          </div>

          {!isAuthenticated ? (
            <>
              {/* 邮箱输入 */}
              <div>
                <label className="block text-sm font-medium mb-1">邮箱</label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="请输入邮箱"
                />
              </div>

              {/* 验证码输入 */}
              <div>
                <label className="block text-sm font-medium mb-1">验证码</label>
                <Input
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  maxLength={6}
                />
              </div>

              {/* 操作按钮 */}
              <div className="space-y-2">
                <Button
                  onClick={sendCode}
                  disabled={loading || !email}
                  variant="outline"
                  className="w-full"
                >
                  发送验证码
                </Button>
                
                <Button
                  onClick={handleLogin}
                  disabled={loading || !email || !code}
                  className="w-full"
                >
                  使用useAuth登录
                </Button>

                <Button
                  onClick={testDirectApiCall}
                  disabled={loading || !email || !code}
                  variant="secondary"
                  className="w-full"
                >
                  直接API调用测试
                </Button>
              </div>
            </>
          ) : (
            <Button onClick={handleLogout} variant="destructive" className="w-full">
              登出
            </Button>
          )}

          {/* 消息显示 */}
          {message && (
            <div className={`p-3 rounded-md text-sm ${
              message.includes('成功') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          {/* 快捷操作 */}
          <div className="pt-4 border-t">
            <h4 className="font-semibold mb-2">快捷操作:</h4>
            <div className="space-y-2 text-sm">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/debug-auth'}
                className="w-full"
              >
                访问调试页面
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/profile'}
                className="w-full"
              >
                访问个人中心
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
                className="w-full"
              >
                刷新页面
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
