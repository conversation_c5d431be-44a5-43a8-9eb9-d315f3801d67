'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Loader2, Users, Star, Calendar, MapPin, Link as LinkIcon, Mail, Grid, List } from 'lucide-react';
import Link from 'next/link';
import { UserRole } from '@/types/auth';
import { PromptCard } from '@/components/prompt/prompt-card';

interface Creator {
  id: number;
  username: string;
  avatarUrl?: string;
  bio?: string;
  role: UserRole;
  followerCount: number;
  followingCount: number;
  createdAt: string;
  totalEarnings?: number;
  totalPrompts?: number;
}

interface Prompt {
  id: number;
  title: string;
  description: string;
  content?: string;
  price: number;
  originalPrice?: number;
  isFree: boolean;
  isFeatured: boolean;
  aiModel: string;
  categoryId: number;
  categoryName: string;
  creator: {
    id: number;
    username: string;
    avatarUrl?: string;
    bio?: string;
    role: string;
    followerCount: number;
    followingCount: number;
    createdAt: string;
  };
  tags: string[];
  status: string;
  viewCount: number;
  downloadCount: number;
  likeCount: number;
  ratingAvg: number;
  ratingCount: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  isPurchased: boolean;
  isLiked: boolean;
  previewImages: string[];
  exampleOutputs: string[];
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

interface PageResponse<T> {
  data: T[];
  total: number;
  totalPages: number;
  page: number;
  size: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export default function CreatorDetailPage() {
  const params = useParams();
  const [creator, setCreator] = useState<Creator | null>(null);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [promptsLoading, setPromptsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [promptsError, setPromptsError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const creatorId = Number(params.id);

  useEffect(() => {
    fetchCreator();
    fetchCreatorPrompts();
  }, [creatorId, currentPage]);

  const fetchCreator = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8080/api/users/${creatorId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result: ApiResponse<Creator> = await response.json();
      
      if (result.code === 200) {
        setCreator(result.data);
      } else {
        throw new Error(result.message || '获取创作者信息失败');
      }
    } catch (err) {
      console.error('获取创作者信息失败:', err);
      setError(err instanceof Error ? err.message : '获取创作者信息失败');
      
      // 设置模拟数据以便测试
      setCreator({
        id: creatorId,
        username: `创作者${creatorId}`,
        avatarUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=creator${creatorId}`,
        bio: '这是一位优秀的AI提示词创作者，专注于为用户提供高质量的创意内容。拥有丰富的创作经验和独特的创作风格。',
        role: UserRole.CREATOR,
        followerCount: Math.floor(Math.random() * 2000) + 100,
        followingCount: Math.floor(Math.random() * 200) + 10,
        createdAt: '2024-01-15T10:00:00Z',
        totalEarnings: Math.floor(Math.random() * 50000) + 5000,
        totalPrompts: Math.floor(Math.random() * 100) + 10
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCreatorPrompts = async () => {
    try {
      setPromptsLoading(true);
      setPromptsError(null);

      const response = await fetch(`http://localhost:8080/api/prompts/user/${creatorId}?page=${currentPage}&size=12`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<PageResponse<Prompt>> = await response.json();

      if (result.code === 200) {
        setPrompts(result.data.data);
        setTotalPages(result.data.totalPages);
      } else {
        throw new Error(result.message || '获取创作者提示词失败');
      }
    } catch (err) {
      console.error('获取创作者提示词失败:', err);
      setPromptsError(err instanceof Error ? err.message : '获取创作者提示词失败');

      // 设置模拟数据以便测试
      const mockPrompts: Prompt[] = [
        {
          id: 1,
          title: 'AI写作助手提示词',
          description: '专业的AI写作助手提示词，帮助您快速生成高质量的文章内容',
          content: '你是一个专业的写作助手...',
          price: 29.99,
          isFree: false,
          isFeatured: true,
          aiModel: 'GPT-4',
          category: {
            id: 1,
            name: '写作',
            slug: 'writing'
          },
          creator: {
            id: creatorId,
            username: `创作者${creatorId}`,
            avatarUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=creator${creatorId}`
          },
          tags: ['写作', 'AI助手', '内容创作'],
          viewCount: 1250,
          purchaseCount: 89,
          ratingAvg: 4.8,
          ratingCount: 45,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          id: 2,
          title: '代码生成专家',
          description: '高效的代码生成提示词，支持多种编程语言',
          content: '你是一个代码生成专家...',
          price: 39.99,
          isFree: false,
          isFeatured: false,
          aiModel: 'GPT-4',
          category: {
            id: 2,
            name: '编程',
            slug: 'programming'
          },
          creator: {
            id: creatorId,
            username: `创作者${creatorId}`,
            avatarUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=creator${creatorId}`
          },
          tags: ['编程', '代码生成', '开发'],
          viewCount: 890,
          purchaseCount: 67,
          ratingAvg: 4.6,
          ratingCount: 32,
          createdAt: '2024-01-20T14:30:00Z',
          updatedAt: '2024-01-20T14:30:00Z'
        },
        {
          id: 3,
          title: '创意设计灵感',
          description: '激发创意设计灵感的AI提示词，适用于各种设计场景',
          content: '你是一个创意设计师...',
          price: 19.99,
          isFree: false,
          isFeatured: false,
          aiModel: 'DALL-E',
          category: {
            id: 3,
            name: '设计',
            slug: 'design'
          },
          creator: {
            id: creatorId,
            username: `创作者${creatorId}`,
            avatarUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=creator${creatorId}`
          },
          tags: ['设计', '创意', '灵感'],
          viewCount: 567,
          purchaseCount: 34,
          ratingAvg: 4.9,
          ratingCount: 28,
          createdAt: '2024-02-01T09:15:00Z',
          updatedAt: '2024-02-01T09:15:00Z'
        }
      ];
      setPrompts(mockPrompts);
      setTotalPages(1);
    } finally {
      setPromptsLoading(false);
    }
  };

  const formatEarnings = (earnings?: number) => {
    if (!earnings) return '¥0';
    return `¥${earnings.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载创作者信息中...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error && !creator) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-destructive mb-4">加载失败</h2>
            <p className="text-muted-foreground mb-6">{error}</p>
            <Button onClick={fetchCreator}>重试</Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!creator) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-4">创作者不存在</h2>
            <p className="text-muted-foreground mb-6">找不到指定的创作者信息</p>
            <Link href="/creators">
              <Button>返回创作者列表</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* 创作者头部信息 */}
        <div className="max-w-4xl mx-auto">
          <Card className="mb-8">
            <CardHeader>
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={creator.avatarUrl} alt={creator.username} />
                  <AvatarFallback className="text-2xl">
                    {creator.username.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold">{creator.username}</h1>
                    <Badge variant="secondary">
                      {creator.role === UserRole.CREATOR ? '创作者' : '用户'}
                    </Badge>
                  </div>
                  
                  <p className="text-muted-foreground mb-4 max-w-2xl">
                    {creator.bio || '这位创作者还没有添加个人简介'}
                  </p>
                  
                  <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{creator.followerCount} 关注者</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>加入于 {formatDate(creator.createdAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col gap-2">
                  <Button size="lg">
                    关注
                  </Button>
                  <Button variant="outline" size="lg">
                    发消息
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* 统计信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-primary">
                  {creator.totalPrompts || 0}
                </CardTitle>
                <CardDescription>发布的提示词</CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-green-600">
                  {formatEarnings(creator.totalEarnings)}
                </CardTitle>
                <CardDescription>总收益</CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-blue-600">
                  4.8
                </CardTitle>
                <CardDescription>
                  <div className="flex items-center justify-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span>平均评分</span>
                  </div>
                </CardDescription>
              </CardHeader>
            </Card>
          </div>

          {/* 作品展示区域 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>发布的作品</CardTitle>
                  <CardDescription>
                    查看 {creator.username} 发布的提示词作品
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {promptsLoading ? (
                <div className="flex justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : promptsError && (!prompts || prompts.length === 0) ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground mb-4">加载失败</p>
                  <Button onClick={fetchCreatorPrompts} variant="outline">
                    重试
                  </Button>
                </div>
              ) : !prompts || prompts.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <p className="mb-4">暂无作品展示</p>
                  <p className="text-sm">
                    该创作者还没有发布任何提示词作品
                  </p>
                </div>
              ) : (
                <>
                  {/* 提示词列表 */}
                  <div className={
                    viewMode === 'grid'
                      ? 'grid gap-6 sm:grid-cols-2 lg:grid-cols-3'
                      : 'space-y-4'
                  }>
                    {prompts.map((prompt) => (
                      <PromptCard
                        key={prompt.id}
                        prompt={prompt}
                        viewMode={viewMode}
                      />
                    ))}
                  </div>

                  {/* 分页 */}
                  {totalPages > 1 && (
                    <div className="mt-8 flex justify-center">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage === 1}
                          onClick={() => setCurrentPage(currentPage - 1)}
                        >
                          上一页
                        </Button>

                        <span className="px-4 py-2 text-sm">
                          第 {currentPage} 页，共 {totalPages} 页
                        </span>

                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage === totalPages}
                          onClick={() => setCurrentPage(currentPage + 1)}
                        >
                          下一页
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
