import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { QueryProvider } from '@/components/query-provider';
import './globals.css';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: {
    default: '中文AI提示词交易平台',
    template: '%s | 中文AI提示词交易平台',
  },
  description: '专业的中文AI提示词交易平台，提供高质量的AI提示词买卖服务',
  keywords: ['AI', '提示词', 'Prompt', '人工智能', '交易平台', '中文'],
  authors: [{ name: 'PromptStore Team' }],
  creator: 'PromptStore',
  publisher: 'PromptStore',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: '中文AI提示词交易平台',
    description: '专业的中文AI提示词交易平台，提供高质量的AI提示词买卖服务',
    siteName: '中文AI提示词交易平台',
  },
  twitter: {
    card: 'summary_large_image',
    title: '中文AI提示词交易平台',
    description: '专业的中文AI提示词交易平台，提供高质量的AI提示词买卖服务',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.variable}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
