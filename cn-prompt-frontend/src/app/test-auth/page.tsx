'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { authApi, VerificationType } from '@/lib/auth-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestAuthPage() {
  const { user, isAuthenticated, emailLogin, emailRegister, logout } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [username, setUsername] = useState('testuser2');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const sendCode = async (type: VerificationType) => {
    try {
      setLoading(true);
      setMessage('');
      await authApi.sendVerificationCode({ email, type });
      setMessage(`${type} 验证码发送成功，请查看后端日志获取验证码`);
    } catch (error: any) {
      setMessage(`发送失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async () => {
    try {
      setLoading(true);
      setMessage('');
      await emailRegister(email, username, code);
      setMessage('注册成功！');
    } catch (error: any) {
      setMessage(`注册失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    try {
      setLoading(true);
      setMessage('');
      await emailLogin(email, code);
      setMessage('登录成功！');
    } catch (error: any) {
      setMessage(`登录失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setMessage('登出成功！');
    } catch (error: any) {
      setMessage(`登出失败: ${error.message}`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>认证状态测试</CardTitle>
          <CardDescription>测试邮箱登录注册和状态管理</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前状态 */}
          <div className="p-4 bg-muted rounded-md">
            <h3 className="font-semibold mb-2">当前状态:</h3>
            <p>登录状态: {isAuthenticated ? '已登录' : '未登录'}</p>
            {user && (
              <div className="mt-2">
                <p>用户ID: {user.id}</p>
                <p>用户名: {user.username}</p>
                <p>邮箱: {user.email}</p>
                <p>角色: {user.role}</p>
              </div>
            )}
          </div>

          {!isAuthenticated ? (
            <>
              {/* 邮箱输入 */}
              <div>
                <label className="block text-sm font-medium mb-1">邮箱</label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="请输入邮箱"
                />
              </div>

              {/* 用户名输入 */}
              <div>
                <label className="block text-sm font-medium mb-1">用户名</label>
                <Input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="请输入用户名"
                />
              </div>

              {/* 验证码输入 */}
              <div>
                <label className="block text-sm font-medium mb-1">验证码</label>
                <Input
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  maxLength={6}
                />
              </div>

              {/* 操作按钮 */}
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Button
                    onClick={() => sendCode(VerificationType.REGISTER)}
                    disabled={loading || !email}
                    variant="outline"
                    size="sm"
                  >
                    发送注册验证码
                  </Button>
                  <Button
                    onClick={() => sendCode(VerificationType.LOGIN)}
                    disabled={loading || !email}
                    variant="outline"
                    size="sm"
                  >
                    发送登录验证码
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={handleRegister}
                    disabled={loading || !email || !username || !code}
                    className="flex-1"
                  >
                    注册
                  </Button>
                  <Button
                    onClick={handleLogin}
                    disabled={loading || !email || !code}
                    className="flex-1"
                  >
                    登录
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <Button onClick={handleLogout} variant="destructive" className="w-full">
              登出
            </Button>
          )}

          {/* 消息显示 */}
          {message && (
            <div className={`p-3 rounded-md text-sm ${
              message.includes('成功') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
