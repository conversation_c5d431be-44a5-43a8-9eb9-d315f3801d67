'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/use-auth';
import { PromptApi } from '@/lib/prompt-api';
import { OrderApi, OrderResponse } from '@/lib/order-api';
import {
  User,
  ShoppingBag,
  Heart,
  FileText,
  TrendingUp,
  Settings,
  Plus,
  Eye,
  Download,
  Edit,
  Trash2
} from 'lucide-react';
import Link from 'next/link';
import { formatPrice } from '@/lib/utils';

export default function DashboardPage() {
  const { user, isAuthenticated, requireAuth } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [userPrompts, setUserPrompts] = useState([]);
  const [promptsLoading, setPromptsLoading] = useState(false);
  const [promptsError, setPromptsError] = useState(null);
  const [userOrders, setUserOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersError, setOrdersError] = useState(null);

  // 获取用户的提示词
  useEffect(() => {
    if (user?.id && activeTab === 'prompts') {
      fetchUserPrompts();
    }
  }, [user?.id, activeTab]);

  // 获取用户的订单
  useEffect(() => {
    if (user?.id && activeTab === 'purchases') {
      fetchUserOrders();
    }
  }, [user?.id, activeTab]);

  const fetchUserPrompts = async () => {
    try {
      setPromptsLoading(true);
      setPromptsError(null);
      const response = await PromptApi.getUserPrompts(user.id);
      setUserPrompts(response.data || []);
    } catch (error) {
      console.error('获取用户提示词失败:', error);
      setPromptsError(error.message);
    } finally {
      setPromptsLoading(false);
    }
  };

  const fetchUserOrders = async () => {
    try {
      setOrdersLoading(true);
      setOrdersError(null);
      const response = await OrderApi.getUserOrders();
      setUserOrders(response.data || []);
    } catch (error) {
      console.error('获取用户订单失败:', error);
      setOrdersError(error.message);
    } finally {
      setOrdersLoading(false);
    }
  };

  // 如果未登录，重定向到登录页
  if (!requireAuth()) {
    return null;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center py-20">
          <LoadingSpinner size="lg" />
        </div>
        <Footer />
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: '概览', icon: TrendingUp },
    { id: 'purchases', name: '我的购买', icon: ShoppingBag },
    { id: 'favorites', name: '我的收藏', icon: Heart },
    { id: 'prompts', name: '我的提示词', icon: FileText, show: user.role === 'CREATOR' },
    { id: 'profile', name: '个人资料', icon: User },
    { id: 'settings', name: '账户设置', icon: Settings },
  ].filter(tab => tab.show !== false);

  const stats = [
    {
      name: '总消费',
      value: formatPrice(user.totalSpent || 0),
      icon: ShoppingBag,
      color: 'text-blue-600',
      bg: 'bg-blue-100',
    },
    {
      name: '总收益',
      value: formatPrice(user.totalEarnings || 0),
      icon: TrendingUp,
      color: 'text-green-600',
      bg: 'bg-green-100',
      show: user.role === 'CREATOR',
    },
    {
      name: '关注者',
      value: user.followerCount || 0,
      icon: User,
      color: 'text-purple-600',
      bg: 'bg-purple-100',
    },
    {
      name: '关注中',
      value: user.followingCount || 0,
      icon: Heart,
      color: 'text-pink-600',
      bg: 'bg-pink-100',
    },
  ].filter(stat => stat.show !== false);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container-custom py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">个人中心</h1>
          <p className="text-muted-foreground">
            管理您的账户、订单和提示词
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-4">
          {/* 侧边栏导航 */}
          <div className="lg:col-span-1">
            <div className="bg-card border rounded-lg p-6">
              {/* 用户信息 */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="h-10 w-10 text-white" />
                </div>
                <h3 className="font-semibold text-lg">{user.username}</h3>
                <p className="text-sm text-muted-foreground">
                  {user.role === 'CREATOR' ? '创作者' : '用户'}
                </p>
              </div>

              {/* 导航菜单 */}
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>

              {/* 升级为创作者 */}
              {user.role === 'USER' && (
                <div className="mt-6 pt-6 border-t">
                  <Button className="w-full" asChild>
                    <Link href="/creator/apply">
                      <Plus className="h-4 w-4 mr-2" />
                      成为创作者
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="lg:col-span-3">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* 统计卡片 */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {stats.map((stat, index) => {
                    const Icon = stat.icon;
                    return (
                      <div key={index} className="bg-card border rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">{stat.name}</p>
                            <p className="text-2xl font-bold">{stat.value}</p>
                          </div>
                          <div className={`p-3 rounded-lg ${stat.bg}`}>
                            <Icon className={`h-6 w-6 ${stat.color}`} />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* 快速操作 */}
                <div className="bg-card border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">快速操作</h2>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                      <Link href="/browse">
                        <Eye className="h-6 w-6 mb-2" />
                        <span>浏览提示词</span>
                      </Link>
                    </Button>
                    
                    <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                      <Link href="/dashboard?tab=purchases">
                        <ShoppingBag className="h-6 w-6 mb-2" />
                        <span>我的购买</span>
                      </Link>
                    </Button>
                    
                    <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                      <Link href="/dashboard?tab=favorites">
                        <Heart className="h-6 w-6 mb-2" />
                        <span>我的收藏</span>
                      </Link>
                    </Button>
                    
                    {user.role === 'CREATOR' && (
                      <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                        <Link href="/create">
                          <Plus className="h-6 w-6 mb-2" />
                          <span>发布提示词</span>
                        </Link>
                      </Button>
                    )}
                    
                    <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                      <Link href="/dashboard?tab=profile">
                        <User className="h-6 w-6 mb-2" />
                        <span>编辑资料</span>
                      </Link>
                    </Button>
                    
                    <Button variant="outline" className="h-auto p-4 flex-col" asChild>
                      <Link href="/dashboard?tab=settings">
                        <Settings className="h-6 w-6 mb-2" />
                        <span>账户设置</span>
                      </Link>
                    </Button>
                  </div>
                </div>

                {/* 最近活动 */}
                <div className="bg-card border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">最近活动</h2>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Download className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">购买了提示词</p>
                        <p className="text-sm text-muted-foreground">专业营销文案生成器</p>
                      </div>
                      <span className="text-sm text-muted-foreground">2小时前</span>
                    </div>
                    
                    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <Heart className="h-5 w-5 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">收藏了提示词</p>
                        <p className="text-sm text-muted-foreground">AI绘画风格指导</p>
                      </div>
                      <span className="text-sm text-muted-foreground">1天前</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'purchases' && (
              <div className="bg-card border rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">我的购买</h2>
                  <Button onClick={fetchUserOrders} variant="outline" size="sm">
                    刷新
                  </Button>
                </div>

                {ordersLoading ? (
                  <div className="flex justify-center py-12">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : ordersError ? (
                  <div className="text-center py-12 text-red-500">
                    <ShoppingBag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="mb-4">加载失败: {ordersError}</p>
                    <Button onClick={fetchUserOrders} variant="outline">
                      重试
                    </Button>
                  </div>
                ) : userOrders.length === 0 ? (
                  <div className="text-center py-12">
                    <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">暂无购买记录</p>
                    <Button asChild>
                      <Link href="/browse">去浏览提示词</Link>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 简化统计信息 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-primary">
                          {userOrders.length}
                        </div>
                        <div className="text-xs text-muted-foreground">总订单</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-green-600">
                          {userOrders.filter(o => o.status === 'PAID').length}
                        </div>
                        <div className="text-xs text-muted-foreground">已支付</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-yellow-600">
                          {userOrders.filter(o => o.status === 'PENDING').length}
                        </div>
                        <div className="text-xs text-muted-foreground">待支付</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-blue-600">
                          ¥{userOrders.filter(o => o.status === 'PAID').reduce((sum, o) => sum + o.finalAmount, 0).toFixed(2)}
                        </div>
                        <div className="text-xs text-muted-foreground">总消费</div>
                      </div>
                    </div>

                    {/* 最近订单列表 */}
                    <div className="space-y-3">
                      {userOrders.slice(0, 5).map((order) => (
                        <div key={order.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium text-sm">订单: {order.orderNo}</h3>
                                <Badge variant={
                                  order.status === 'PAID' ? 'default' :
                                  order.status === 'PENDING' ? 'secondary' :
                                  order.status === 'CANCELLED' ? 'outline' : 'destructive'
                                } className="text-xs">
                                  {order.status === 'PAID' ? '已支付' :
                                   order.status === 'PENDING' ? '待支付' :
                                   order.status === 'CANCELLED' ? '已取消' : '已退款'}
                                </Badge>
                              </div>
                              <div className="text-xs text-muted-foreground mb-2">
                                {new Date(order.createdAt).toLocaleDateString()}
                              </div>
                              <div className="font-semibold text-primary">
                                ¥{order.finalAmount.toFixed(2)}
                              </div>
                              {order.items && order.items.length > 0 && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {order.items.map(item => item.promptTitle).join(', ')}
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1 ml-3">
                              {order.status === 'PENDING' && (
                                <Button size="sm" variant="ghost">
                                  支付
                                </Button>
                              )}
                              <Button size="sm" variant="ghost">
                                <Eye className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}

                      {userOrders.length > 5 && (
                        <div className="text-center pt-4">
                          <Button variant="outline" asChild>
                            <Link href="/profile?tab=purchases">
                              查看全部 {userOrders.length} 个订单
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'favorites' && (
              <div className="bg-card border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">我的收藏</h2>
                <div className="text-center py-12">
                  <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">暂无收藏内容</p>
                  <Button asChild>
                    <Link href="/browse">去发现好内容</Link>
                  </Button>
                </div>
              </div>
            )}

            {activeTab === 'prompts' && user.role === 'CREATOR' && (
              <div className="bg-card border rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">我的提示词</h2>
                  <Button asChild>
                    <Link href="/create">
                      <Plus className="h-4 w-4 mr-2" />
                      发布新提示词
                    </Link>
                  </Button>
                </div>

                {promptsLoading ? (
                  <div className="flex justify-center py-12">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : promptsError ? (
                  <div className="text-center py-12 text-red-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="mb-4">加载失败: {promptsError}</p>
                    <Button onClick={fetchUserPrompts} variant="outline">
                      重试
                    </Button>
                  </div>
                ) : userPrompts.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">还没有发布任何提示词</p>
                    <Button asChild>
                      <Link href="/create">开始创作</Link>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 统计信息 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-primary">
                          {userPrompts.length}
                        </div>
                        <div className="text-xs text-muted-foreground">总数</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-green-600">
                          {userPrompts.filter(p => p.status === 'APPROVED').length}
                        </div>
                        <div className="text-xs text-muted-foreground">已发布</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-yellow-600">
                          {userPrompts.filter(p => p.status === 'PENDING').length}
                        </div>
                        <div className="text-xs text-muted-foreground">审核中</div>
                      </div>
                      <div className="bg-muted/50 rounded-lg p-3 text-center">
                        <div className="text-xl font-bold text-gray-600">
                          {userPrompts.filter(p => p.status === 'DRAFT').length}
                        </div>
                        <div className="text-xs text-muted-foreground">草稿</div>
                      </div>
                    </div>

                    {/* 提示词列表 */}
                    <div className="space-y-3">
                      {userPrompts.slice(0, 5).map((prompt) => (
                        <div key={prompt.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium">{prompt.title}</h3>
                                <Badge variant={
                                  prompt.status === 'APPROVED' ? 'default' :
                                  prompt.status === 'PENDING' ? 'secondary' :
                                  prompt.status === 'DRAFT' ? 'outline' : 'destructive'
                                } className="text-xs">
                                  {prompt.status === 'APPROVED' ? '已发布' :
                                   prompt.status === 'PENDING' ? '审核中' :
                                   prompt.status === 'DRAFT' ? '草稿' : '已拒绝'}
                                </Badge>
                                {prompt.isFree && (
                                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                                    免费
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2 line-clamp-1">
                                {prompt.description}
                              </p>
                              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                                <span>{prompt.isFree ? '免费' : `¥${prompt.price}`}</span>
                                <span>浏览 {prompt.viewCount}</span>
                                <span>下载 {prompt.downloadCount}</span>
                                <span>{new Date(prompt.createdAt).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1 ml-3">
                              <Button size="sm" variant="ghost" asChild>
                                <Link href={`/prompt/${prompt.id}`}>
                                  <Eye className="h-3 w-3" />
                                </Link>
                              </Button>
                              {prompt.status === 'DRAFT' && (
                                <Button size="sm" variant="ghost" asChild>
                                  <Link href={`/create?edit=${prompt.id}`}>
                                    <Edit className="h-3 w-3" />
                                  </Link>
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {userPrompts.length > 5 && (
                        <div className="text-center pt-4">
                          <Button variant="outline" asChild>
                            <Link href="/profile?tab=prompts">
                              查看全部 {userPrompts.length} 个提示词
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="bg-card border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">个人资料</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">用户名</label>
                    <input
                      type="text"
                      value={user.username}
                      className="w-full px-3 py-2 border rounded-lg"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">邮箱</label>
                    <input
                      type="email"
                      value={user.email || ''}
                      className="w-full px-3 py-2 border rounded-lg"
                      placeholder="请设置邮箱"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">个人简介</label>
                    <textarea
                      value={user.bio || ''}
                      className="w-full px-3 py-2 border rounded-lg h-24"
                      placeholder="介绍一下自己..."
                    />
                  </div>
                  
                  <Button>保存修改</Button>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="bg-card border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">账户设置</h2>
                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">邮箱验证</h3>
                      <p className="text-sm text-muted-foreground">
                        {user.emailVerified ? '已验证' : '未验证'}
                      </p>
                    </div>
                    {!user.emailVerified && (
                      <Button variant="outline" size="sm">验证邮箱</Button>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">手机验证</h3>
                      <p className="text-sm text-muted-foreground">
                        {user.phoneVerified ? '已验证' : '未验证'}
                      </p>
                    </div>
                    {!user.phoneVerified && (
                      <Button variant="outline" size="sm">验证手机</Button>
                    )}
                  </div>
                  
                  <div className="pt-6 border-t">
                    <Button variant="destructive">注销账户</Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
