'use client';

import { useState } from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, UserPlus, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/hooks/use-auth';
import { authApi, VerificationType } from '@/lib/auth-api';

interface EmailRegisterForm {
  email: string;
  username: string;
  code: string;
  bio: string;
}

export default function EmailRegisterPage() {
  const { emailRegister, isLoading } = useAuth();
  const [form, setForm] = useState<EmailRegisterForm>({
    email: '',
    username: '',
    code: '',
    bio: ''
  });
  const [sendingCode, setSendingCode] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof EmailRegisterForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateUsername = (username: string) => {
    const usernameRegex = /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/;
    return username.length >= 2 && username.length <= 20 && usernameRegex.test(username);
  };

  const sendVerificationCode = async () => {
    if (!form.email) {
      setError('请输入邮箱地址');
      return;
    }

    if (!validateEmail(form.email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    try {
      setSendingCode(true);
      setError(null);

      await authApi.sendVerificationCode({
        email: form.email,
        type: VerificationType.REGISTER
      });

      setCodeSent(true);
      setCountdown(60);

      // 开始倒计时
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (err: any) {
      console.error('发送验证码失败:', err);
      setError(err.message || '发送验证码失败，请检查网络连接');
    } finally {
      setSendingCode(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!form.email || !form.username || !form.code) {
      setError('请填写完整信息');
      return;
    }

    if (!validateEmail(form.email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    if (!validateUsername(form.username)) {
      setError('用户名长度必须在2-20个字符之间，只能包含字母、数字、中文、下划线和短横线');
      return;
    }

    if (!/^\d{6}$/.test(form.code)) {
      setError('验证码必须是6位数字');
      return;
    }

    if (form.bio && form.bio.length > 200) {
      setError('个人简介不能超过200个字符');
      return;
    }

    try {
      setError(null);
      await emailRegister(form.email, form.username, form.code, form.bio);
    } catch (err: any) {
      console.error('注册失败:', err);
      setError(err.message || '注册失败，请检查网络连接');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          {/* 返回按钮 */}
          <div className="mb-6">
            <Link href="/auth/login" className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground">
              <ArrowLeft className="h-4 w-4 mr-1" />
              返回登录选择
            </Link>
          </div>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <UserPlus className="h-6 w-6 text-primary" />
              </div>
              <CardTitle className="text-2xl">邮箱注册</CardTitle>
              <CardDescription>
                创建您的账号，开始使用提示词商店
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleRegister} className="space-y-4">
                {/* 邮箱输入 */}
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱地址 *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入邮箱地址"
                    value={form.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={isLoading || sendingCode}
                  />
                </div>

                {/* 用户名输入 */}
                <div className="space-y-2">
                  <Label htmlFor="username">用户名 *</Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="请输入用户名（2-20个字符）"
                    value={form.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    disabled={isLoading || sendingCode}
                    maxLength={20}
                  />
                </div>

                {/* 验证码输入 */}
                <div className="space-y-2">
                  <Label htmlFor="code">验证码 *</Label>
                  <div className="flex gap-2">
                    <Input
                      id="code"
                      type="text"
                      placeholder="请输入6位验证码"
                      value={form.code}
                      onChange={(e) => handleInputChange('code', e.target.value)}
                      disabled={isLoading || sendingCode}
                      maxLength={6}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={sendVerificationCode}
                      disabled={sendingCode || countdown > 0 || !form.email}
                      className="whitespace-nowrap"
                    >
                      {sendingCode ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                          发送中
                        </>
                      ) : countdown > 0 ? (
                        `${countdown}s`
                      ) : codeSent ? (
                        '重新发送'
                      ) : (
                        '发送验证码'
                      )}
                    </Button>
                  </div>
                </div>

                {/* 个人简介输入 */}
                <div className="space-y-2">
                  <Label htmlFor="bio">个人简介</Label>
                  <Textarea
                    id="bio"
                    placeholder="请输入个人简介（可选，最多200个字符）"
                    value={form.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    disabled={isLoading || sendingCode}
                    maxLength={200}
                    rows={3}
                  />
                  <div className="text-xs text-muted-foreground text-right">
                    {form.bio.length}/200
                  </div>
                </div>

                {/* 错误信息 */}
                {error && (
                  <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                    {error}
                  </div>
                )}

                {/* 成功信息 */}
                {codeSent && !error && (
                  <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md">
                    验证码已发送到您的邮箱，请查收
                  </div>
                )}

                {/* 注册按钮 */}
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading || sendingCode || !form.email || !form.username || !form.code}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      注册中...
                    </>
                  ) : (
                    '注册'
                  )}
                </Button>

                {/* 登录链接 */}
                <div className="text-center text-sm text-muted-foreground">
                  已有账号？{' '}
                  <Link href="/auth/email-login" className="text-primary hover:underline">
                    立即登录
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
