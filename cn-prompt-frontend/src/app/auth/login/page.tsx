'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuth } from '@/hooks/use-auth';
import { QrCode, RefreshCw } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import Logo from '@/components/layout/logo';

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, getWechatQRCode, login } = useAuth();
  
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [qrState, setQrState] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      const redirect = searchParams.get('redirect') || '/';
      router.push(redirect);
    }
  }, [isAuthenticated, router, searchParams]);

  // 获取微信登录二维码
  const fetchQRCode = async () => {
    try {
      setIsRefreshing(true);
      const response = await getWechatQRCode();
      setQrCodeUrl(response.qrCodeUrl);
      setQrState(response.state);
    } catch (error) {
      toast.error('获取二维码失败，请重试');
    } finally {
      setIsRefreshing(false);
    }
  };

  // 初始化获取二维码
  useEffect(() => {
    fetchQRCode();
  }, []);

  // 处理微信回调
  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    
    if (code && state) {
      handleWechatCallback(code, state);
    }
  }, [searchParams]);

  const handleWechatCallback = async (code: string, state: string) => {
    try {
      setIsLoading(true);
      await login(code, state);
      
      const redirect = searchParams.get('redirect') || '/dashboard';
      router.push(redirect);
    } catch (error: any) {
      toast.error(error.message || '登录失败，请重试');
      // 登录失败后重新获取二维码
      fetchQRCode();
    } finally {
      setIsLoading(false);
    }
  };

  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="mb-8 text-center">
          <Link href="/" className="inline-flex items-center space-x-2 mb-8">
            <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center">
              <span className="text-white font-bold text-xs">LOGO</span>
            </div>
            <span className="text-xl font-bold">AI提示词</span>
          </Link>
        </div>

        <div className="mx-auto max-w-md">
          <div className="bg-card border rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold mb-2">欢迎登录</h1>
              <p className="text-muted-foreground">
                使用微信扫码登录，开始您的AI创作之旅
              </p>
            </div>

            {/* 微信登录区域 */}
            <div className="space-y-6">
              {/* 二维码区域 */}
              <div className="relative">
                <div className="bg-muted rounded-lg p-8 text-center">
                  {qrCodeUrl ? (
                    <div className="space-y-4">
                      {/* 二维码 */}
                      <div className="relative inline-block">
                        <div className="w-48 h-48 bg-white rounded-lg p-4 shadow-sm">
                          <iframe
                            src={qrCodeUrl}
                            className="w-full h-full border-0"
                            title="微信登录二维码"
                          />
                        </div>
                        
                        {/* 加载遮罩 */}
                        {(isLoading || isRefreshing) && (
                          <div className="absolute inset-0 bg-background/80 rounded-lg flex items-center justify-center">
                            <LoadingSpinner size="lg" />
                          </div>
                        )}
                      </div>

                      {/* 提示文字 */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium">请使用微信扫描二维码</p>
                        <p className="text-xs text-muted-foreground">
                          扫码后在手机上确认登录
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="py-12">
                      <QrCode className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">正在加载二维码...</p>
                    </div>
                  )}
                </div>

                {/* 刷新按钮 */}
                <div className="absolute top-2 right-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={fetchQRCode}
                    disabled={isRefreshing}
                    className="h-8 w-8"
                  >
                    <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>

              {/* 登录说明 */}
              <div className="bg-muted/50 rounded-lg p-4">
                <h3 className="text-sm font-medium mb-2">登录说明</h3>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• 首次登录将自动创建账户</li>
                  <li>• 支持微信扫码快速登录</li>
                  <li>• 登录后可享受完整功能</li>
                </ul>
              </div>

              {/* 其他登录方式 */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground mb-4">
                  或选择其他登录方式
                </p>

                <div className="flex justify-center space-x-4">
                  <Button variant="outline" size="sm" disabled>
                    手机号登录
                  </Button>
                  <Link href="/auth/email-login">
                    <Button variant="outline" size="sm">
                      邮箱登录
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* 底部链接 */}
          <div className="mt-8 text-center text-xs text-muted-foreground">
            <p>
              登录即表示您同意我们的
              <Link href="/terms" className="text-primary hover:underline mx-1">
                服务条款
              </Link>
              和
              <Link href="/privacy" className="text-primary hover:underline mx-1">
                隐私政策
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
