'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Loader2, Search, Users, Star, TrendingUp, Award } from 'lucide-react';
import Link from 'next/link';
import { UserRole } from '@/types/auth';

interface Creator {
  id: number;
  username: string;
  avatarUrl?: string;
  bio?: string;
  role: UserRole;
  followerCount: number;
  followingCount: number;
  createdAt: string;
  totalEarnings?: number;
  totalPrompts?: number;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}

export default function CreatorsPage() {
  const [creators, setCreators] = useState<Creator[]>([]);
  const [topCreators, setTopCreators] = useState<Creator[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchCreators();
    fetchTopCreators();
  }, [currentPage, searchTerm]);

  const fetchCreators = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        role: 'CREATOR',
        page: currentPage.toString(),
        size: '12'
      });
      
      if (searchTerm) {
        params.append('username', searchTerm);
      }

      const response = await fetch(`http://localhost:8080/api/users/search?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result: ApiResponse<PageResponse<Creator>> = await response.json();
      
      if (result.code === 200) {
        setCreators(result.data.content);
        setTotalPages(result.data.totalPages);
      } else {
        throw new Error(result.message || '获取创作者失败');
      }
    } catch (err) {
      console.error('获取创作者失败:', err);
      setError(err instanceof Error ? err.message : '获取创作者失败');
      // 设置模拟数据以便测试
      setCreators([
        {
          id: 1,
          username: 'AI创作大师',
          avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator1',
          bio: '专注于AI写作提示词创作，已帮助数千用户提升写作效率',
          role: UserRole.CREATOR,
          followerCount: 1250,
          followingCount: 89,
          createdAt: '2024-01-15T10:00:00Z',
          totalEarnings: 15680,
          totalPrompts: 45
        },
        {
          id: 2,
          username: '设计灵感师',
          avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator2',
          bio: '设计类提示词专家，擅长创作视觉设计相关的AI提示词',
          role: UserRole.CREATOR,
          followerCount: 980,
          followingCount: 156,
          createdAt: '2024-02-01T14:30:00Z',
          totalEarnings: 12340,
          totalPrompts: 38
        },
        {
          id: 3,
          username: '编程助手',
          avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator3',
          bio: '编程开发类提示词创作者，专注于代码生成和技术文档',
          role: UserRole.CREATOR,
          followerCount: 2100,
          followingCount: 67,
          createdAt: '2024-01-20T09:15:00Z',
          totalEarnings: 23450,
          totalPrompts: 62
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchTopCreators = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/users/creators/top?limit=5');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result: ApiResponse<Creator[]> = await response.json();
      
      if (result.code === 200) {
        setTopCreators(result.data);
      } else {
        // 设置模拟数据
        setTopCreators([
          {
            id: 3,
            username: '编程助手',
            avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator3',
            bio: '编程开发类提示词创作者',
            role: UserRole.CREATOR,
            followerCount: 2100,
            followingCount: 67,
            createdAt: '2024-01-20T09:15:00Z',
            totalEarnings: 23450
          },
          {
            id: 1,
            username: 'AI创作大师',
            avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=creator1',
            bio: '专注于AI写作提示词创作',
            role: UserRole.CREATOR,
            followerCount: 1250,
            followingCount: 89,
            createdAt: '2024-01-15T10:00:00Z',
            totalEarnings: 15680
          }
        ]);
      }
    } catch (err) {
      console.error('获取顶级创作者失败:', err);
    }
  };

  const formatEarnings = (earnings?: number) => {
    if (!earnings) return '¥0';
    return `¥${earnings.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  if (loading && (!creators || creators.length === 0)) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载创作者中...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            优秀创作者
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            发现才华横溢的AI提示词创作者，找到最适合你需求的专业服务
          </p>
        </div>

        {/* 顶级创作者排行榜 */}
        {topCreators && topCreators.length > 0 && (
          <div className="mb-12">
            <div className="flex items-center gap-2 mb-6">
              <Award className="h-6 w-6 text-yellow-500" />
              <h2 className="text-2xl font-bold">顶级创作者</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {topCreators.map((creator, index) => (
                <Card key={creator.id} className="relative overflow-hidden">
                  {index < 3 && (
                    <div className="absolute top-4 right-4">
                      <Badge variant={index === 0 ? 'default' : 'secondary'}>
                        #{index + 1}
                      </Badge>
                    </div>
                  )}
                  <CardHeader className="text-center">
                    <Avatar className="h-16 w-16 mx-auto mb-4">
                      <AvatarImage src={creator.avatarUrl} alt={creator.username} />
                      <AvatarFallback>{creator.username.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <CardTitle className="text-lg">{creator.username}</CardTitle>
                    <CardDescription className="text-sm">
                      {creator.bio}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{creator.followerCount} 关注者</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-4 w-4" />
                        <span>{formatEarnings(creator.totalEarnings)}</span>
                      </div>
                    </div>
                    <Link href={`/creator/${creator.id}`}>
                      <Button className="w-full" size="sm">
                        查看详情
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* 搜索栏 */}
        <div className="max-w-md mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              placeholder="搜索创作者..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            />
          </div>
        </div>

        {/* 创作者网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {creators && creators.map((creator) => (
            <Card key={creator.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <Avatar className="h-12 w-12 mx-auto mb-3">
                  <AvatarImage src={creator.avatarUrl} alt={creator.username} />
                  <AvatarFallback>{creator.username.charAt(0)}</AvatarFallback>
                </Avatar>
                <CardTitle className="text-lg">{creator.username}</CardTitle>
                <CardDescription className="text-sm line-clamp-2">
                  {creator.bio || '这位创作者还没有添加个人简介'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center justify-between">
                    <span>关注者</span>
                    <span>{creator.followerCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>提示词</span>
                    <span>{creator.totalPrompts || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>加入时间</span>
                    <span>{formatDate(creator.createdAt)}</span>
                  </div>
                </div>
                <Link href={`/creator/${creator.id}`}>
                  <Button className="w-full" size="sm" variant="outline">
                    查看主页
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 空状态 */}
        {(!creators || creators.length === 0) && !loading && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">没有找到创作者</h3>
            <p className="text-muted-foreground">
              {searchTerm ? '尝试调整搜索条件' : '暂时还没有创作者加入'}
            </p>
          </div>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                上一页
              </Button>
              <span className="flex items-center px-4 text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
}
