'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/use-auth';
import { FavoriteButton } from '@/components/prompt/favorite-button';
import { Heart, Clock, User, Tag } from 'lucide-react';

interface FavoritePrompt {
  id: number;
  title: string;
  description: string;
  price: number;
  averageRating: number;
  reviewCount: number;
  favoriteCount: number;
  createdAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
  category: {
    id: number;
    name: string;
  };
}

export default function FavoritesPage() {
  const { isAuthenticated, requireAuth } = useAuth();
  const [loading, setLoading] = useState(true);
  const [favorites, setFavorites] = useState<FavoritePrompt[]>([]);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (!requireAuth()) return;
    loadFavorites();
  }, [currentPage]);

  const loadFavorites = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/favorites/my?page=${currentPage}&size=12`, {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('获取收藏列表失败');

      const result = await response.json();
      if (result.code === 200) {
        setFavorites(result.data.content || []);
        setTotalPages(result.data.totalPages || 1);
      } else {
        throw new Error(result.message || '获取收藏列表失败');
      }
    } catch (error: any) {
      console.error('加载收藏列表失败:', error);
      toast.error(error.message || '加载收藏列表失败');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
        <span className="ml-1 text-sm text-gray-600">
          {rating.toFixed(1)} ({reviewCount})
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-2">
            <Heart className="w-6 h-6 text-red-500" />
            <h1 className="text-3xl font-bold text-gray-900">我的收藏</h1>
          </div>
          <p className="text-gray-600">管理您收藏的提示词</p>
        </div>

        {favorites.length === 0 ? (
          <div className="text-center py-12">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏</h3>
            <p className="text-gray-500 mb-6">您还没有收藏任何提示词</p>
            <Link
              href="/browse"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              去浏览提示词
            </Link>
          </div>
        ) : (
          <>
            {/* 收藏统计 */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">收藏统计</h3>
                  <p className="text-sm text-gray-500">您总共收藏了 {favorites.length} 个提示词</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">{favorites.length}</div>
                  <div className="text-sm text-gray-500">个收藏</div>
                </div>
              </div>
            </div>

            {/* 收藏列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {favorites.map((prompt) => (
                <div key={prompt.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    {/* 标题和描述 */}
                    <div className="mb-4">
                      <Link href={`/prompts/${prompt.id}`}>
                        <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer line-clamp-2">
                          {prompt.title}
                        </h3>
                      </Link>
                      <p className="text-sm text-gray-600 mt-2 line-clamp-3">
                        {prompt.description}
                      </p>
                    </div>

                    {/* 评分 */}
                    <div className="mb-4">
                      {renderStars(prompt.averageRating)}
                    </div>

                    {/* 作者信息 */}
                    <div className="flex items-center mb-4">
                      <div className="flex items-center space-x-2">
                        {prompt.user.avatar ? (
                          <img
                            src={prompt.user.avatar}
                            alt={prompt.user.username}
                            className="w-6 h-6 rounded-full"
                          />
                        ) : (
                          <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                            <User className="w-3 h-3 text-gray-600" />
                          </div>
                        )}
                        <span className="text-sm text-gray-600">{prompt.user.username}</span>
                      </div>
                    </div>

                    {/* 分类和时间 */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-1">
                        <Tag className="w-4 h-4" />
                        <span>{prompt.category.name}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{new Date(prompt.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* 价格和操作 */}
                    <div className="flex items-center justify-between">
                      <div className="text-lg font-bold text-blue-600">
                        {prompt.price === 0 ? '免费' : `¥${prompt.price}`}
                      </div>
                      <div className="flex items-center space-x-2">
                        <FavoriteButton promptId={prompt.id} />
                        <Link
                          href={`/prompts/${prompt.id}`}
                          className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                        >
                          查看详情
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="mt-8 flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 text-sm rounded-md ${
                            currentPage === page
                              ? 'bg-blue-600 text-white'
                              : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                  </div>
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
