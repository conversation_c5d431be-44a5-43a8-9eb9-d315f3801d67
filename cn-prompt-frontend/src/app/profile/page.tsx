'use client';

import { useAuth } from '@/hooks/use-auth';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { PromptApi } from '@/lib/prompt-api';
import { OrderApi } from '@/lib/order-api';
import {
  User,
  Mail,
  Calendar,
  Star,
  Heart,
  ShoppingBag,
  FileText,
  Settings,
  Edit,
  Plus,
  Eye,
} from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function ProfilePage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [userPrompts, setUserPrompts] = useState([]);
  const [promptsLoading, setPromptsLoading] = useState(false);
  const [promptsError, setPromptsError] = useState(null);
  const [userOrders, setUserOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersError, setOrdersError] = useState(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // This page requires authentication
      // In a real app, you would redirect here
    }
  }, [isAuthenticated, isLoading]);

  // 获取用户的提示词
  useEffect(() => {
    if (user?.id) {
      fetchUserPrompts();
    }
  }, [user?.id]);

  // 获取用户的订单
  useEffect(() => {
    if (user?.id) {
      fetchUserOrders();
    }
  }, [user?.id]);

  const fetchUserPrompts = async () => {
    try {
      setPromptsLoading(true);
      setPromptsError(null);
      const response = await PromptApi.getUserPrompts(user.id);
      setUserPrompts(response.data || []);
    } catch (error) {
      console.error('获取用户提示词失败:', error);
      setPromptsError(error.message);
    } finally {
      setPromptsLoading(false);
    }
  };

  const fetchUserOrders = async () => {
    try {
      setOrdersLoading(true);
      setOrdersError(null);
      const response = await OrderApi.getUserOrders();
      setUserOrders(response.data || []);
    } catch (error) {
      console.error('获取用户订单失败:', error);
      setOrdersError(error.message);
    } finally {
      setOrdersLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 用户信息卡片 */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
                    <User className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">{user.username}</CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <Mail className="h-4 w-4 mr-1" />
                      {user.email}
                    </CardDescription>
                    <div className="flex items-center mt-2 space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        加入于 {new Date(user.createdAt).toLocaleDateString('zh-CN')}
                      </div>
                      <Badge variant={user.status === 'ACTIVE' ? 'default' : 'secondary'}>
                        {user.status === 'ACTIVE' ? '活跃' : user.status}
                      </Badge>
                      <Badge variant="outline">
                        {user.role === 'USER' ? '用户' : user.role}
                      </Badge>
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/profile/edit">
                    <Edit className="h-4 w-4 mr-2" />
                    编辑资料
                  </Link>
                </Button>
              </div>
              {user.bio && (
                <div className="mt-4">
                  <p className="text-muted-foreground">{user.bio}</p>
                </div>
              )}
            </CardHeader>
          </Card>

          {/* 统计信息 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">发布的提示词</p>
                    <p className="text-2xl font-bold">{user.totalPrompts || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <ShoppingBag className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">总收益</p>
                    <p className="text-2xl font-bold">¥{user.totalEarnings || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Heart className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">关注者</p>
                    <p className="text-2xl font-bold">{user.followerCount || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Star className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">关注中</p>
                    <p className="text-2xl font-bold">{user.followingCount || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 详细信息标签页 */}
          <Tabs defaultValue="prompts" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="prompts">我的提示词</TabsTrigger>
              <TabsTrigger value="purchases">购买记录</TabsTrigger>
              <TabsTrigger value="favorites">收藏夹</TabsTrigger>
              <TabsTrigger value="settings">设置</TabsTrigger>
            </TabsList>

            <TabsContent value="prompts" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>我发布的提示词</CardTitle>
                      <CardDescription>
                        管理您发布的所有提示词
                      </CardDescription>
                    </div>
                    <Button asChild>
                      <Link href="/create">
                        <Plus className="h-4 w-4 mr-2" />
                        发布新提示词
                      </Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {promptsLoading ? (
                    <div className="flex justify-center py-12">
                      <LoadingSpinner size="lg" />
                    </div>
                  ) : promptsError ? (
                    <div className="text-center py-12 text-red-500">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="mb-4">加载失败: {promptsError}</p>
                      <Button onClick={fetchUserPrompts} variant="outline">
                        重试
                      </Button>
                    </div>
                  ) : userPrompts.length === 0 ? (
                    <div className="text-center py-12 text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="mb-4">您还没有发布任何提示词</p>
                      <Button asChild>
                        <Link href="/create">
                          发布第一个提示词
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* 统计信息 */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-primary">
                            {userPrompts.length}
                          </div>
                          <div className="text-sm text-muted-foreground">总提示词</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {userPrompts.filter(p => p.status === 'APPROVED').length}
                          </div>
                          <div className="text-sm text-muted-foreground">已发布</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-yellow-600">
                            {userPrompts.filter(p => p.status === 'PENDING').length}
                          </div>
                          <div className="text-sm text-muted-foreground">审核中</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-gray-600">
                            {userPrompts.filter(p => p.status === 'DRAFT').length}
                          </div>
                          <div className="text-sm text-muted-foreground">草稿</div>
                        </div>
                      </div>

                      {/* 提示词列表 */}
                      <div className="grid gap-4">
                        {userPrompts.map((prompt) => (
                          <div key={prompt.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h3 className="font-semibold text-lg">{prompt.title}</h3>
                                  <Badge variant={
                                    prompt.status === 'APPROVED' ? 'default' :
                                    prompt.status === 'PENDING' ? 'secondary' :
                                    prompt.status === 'DRAFT' ? 'outline' : 'destructive'
                                  }>
                                    {prompt.status === 'APPROVED' ? '已发布' :
                                     prompt.status === 'PENDING' ? '审核中' :
                                     prompt.status === 'DRAFT' ? '草稿' : '已拒绝'}
                                  </Badge>
                                  {prompt.isFree && (
                                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                                      免费
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                                  {prompt.description}
                                </p>
                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <span>价格: {prompt.isFree ? '免费' : `¥${prompt.price}`}</span>
                                  <span>浏览: {prompt.viewCount}</span>
                                  <span>下载: {prompt.downloadCount}</span>
                                  <span>点赞: {prompt.likeCount}</span>
                                  <span>创建: {new Date(prompt.createdAt).toLocaleDateString()}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                <Button size="sm" variant="outline" asChild>
                                  <Link href={`/prompt/${prompt.id}`}>
                                    <Eye className="h-4 w-4 mr-1" />
                                    查看
                                  </Link>
                                </Button>
                                {prompt.status === 'DRAFT' && (
                                  <Button size="sm" variant="outline" asChild>
                                    <Link href={`/create?edit=${prompt.id}`}>
                                      <Edit className="h-4 w-4 mr-1" />
                                      编辑
                                    </Link>
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="purchases" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>购买记录</CardTitle>
                      <CardDescription>
                        查看您的所有购买记录
                      </CardDescription>
                    </div>
                    <Button onClick={fetchUserOrders} variant="outline" size="sm">
                      刷新
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {ordersLoading ? (
                    <div className="flex justify-center py-12">
                      <LoadingSpinner size="lg" />
                    </div>
                  ) : ordersError ? (
                    <div className="text-center py-12 text-red-500">
                      <ShoppingBag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="mb-4">加载失败: {ordersError}</p>
                      <Button onClick={fetchUserOrders} variant="outline">
                        重试
                      </Button>
                    </div>
                  ) : userOrders.length === 0 ? (
                    <div className="text-center py-12 text-muted-foreground">
                      <ShoppingBag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="mb-4">您还没有购买任何提示词</p>
                      <Button asChild>
                        <Link href="/">
                          去逛逛
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* 统计信息 */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-primary">
                            {userOrders.length}
                          </div>
                          <div className="text-sm text-muted-foreground">总订单</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {userOrders.filter(o => o.status === 'PAID').length}
                          </div>
                          <div className="text-sm text-muted-foreground">已支付</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-yellow-600">
                            {userOrders.filter(o => o.status === 'PENDING').length}
                          </div>
                          <div className="text-sm text-muted-foreground">待支付</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            ¥{userOrders.filter(o => o.status === 'PAID').reduce((sum, o) => sum + o.finalAmount, 0).toFixed(2)}
                          </div>
                          <div className="text-sm text-muted-foreground">总消费</div>
                        </div>
                      </div>

                      {/* 订单列表 */}
                      <div className="space-y-4">
                        {userOrders.map((order) => (
                          <div key={order.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h3 className="font-semibold">订单号: {order.orderNo}</h3>
                                  <Badge variant={
                                    order.status === 'PAID' ? 'default' :
                                    order.status === 'PENDING' ? 'secondary' :
                                    order.status === 'CANCELLED' ? 'outline' : 'destructive'
                                  }>
                                    {order.status === 'PAID' ? '已支付' :
                                     order.status === 'PENDING' ? '待支付' :
                                     order.status === 'CANCELLED' ? '已取消' : '已退款'}
                                  </Badge>
                                </div>
                                <div className="text-sm text-muted-foreground mb-2">
                                  创建时间: {new Date(order.createdAt).toLocaleString()}
                                  {order.paymentTime && (
                                    <span className="ml-4">
                                      支付时间: {new Date(order.paymentTime).toLocaleString()}
                                    </span>
                                  )}
                                </div>
                                <div className="text-lg font-semibold text-primary">
                                  ¥{order.finalAmount.toFixed(2)}
                                  {order.totalAmount !== order.finalAmount && (
                                    <span className="text-sm text-muted-foreground line-through ml-2">
                                      ¥{order.totalAmount.toFixed(2)}
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                {order.status === 'PENDING' && (
                                  <Button size="sm" variant="default">
                                    去支付
                                  </Button>
                                )}
                                <Button size="sm" variant="outline">
                                  查看详情
                                </Button>
                              </div>
                            </div>

                            {/* 订单项 */}
                            {order.items && order.items.length > 0 && (
                              <div className="border-t pt-3 mt-3">
                                <div className="space-y-2">
                                  {order.items.map((item) => (
                                    <div key={item.id} className="flex items-center justify-between text-sm">
                                      <div className="flex-1">
                                        <span className="font-medium">{item.promptTitle}</span>
                                        <span className="text-muted-foreground ml-2">x{item.quantity}</span>
                                      </div>
                                      <div className="text-right">
                                        <span className="font-medium">¥{item.totalPrice.toFixed(2)}</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="favorites" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>我的收藏</CardTitle>
                  <CardDescription>
                    查看您收藏的提示词
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <Heart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="mb-4">您还没有收藏任何提示词</p>
                    <Button asChild>
                      <Link href="/">
                        去发现
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>账户设置</CardTitle>
                  <CardDescription>
                    管理您的账户设置和偏好
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium">邮箱验证</h4>
                        <p className="text-sm text-muted-foreground">
                          {user.emailVerified ? '已验证' : '未验证'}
                        </p>
                      </div>
                      <Badge variant={user.emailVerified ? 'default' : 'destructive'}>
                        {user.emailVerified ? '已验证' : '未验证'}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium">手机验证</h4>
                        <p className="text-sm text-muted-foreground">
                          {user.phoneVerified ? '已验证' : '未验证'}
                        </p>
                      </div>
                      <Badge variant={user.phoneVerified ? 'default' : 'secondary'}>
                        {user.phoneVerified ? '已验证' : '未验证'}
                      </Badge>
                    </div>

                    <div className="pt-4">
                      <Button variant="outline" className="w-full">
                        <Settings className="h-4 w-4 mr-2" />
                        更多设置
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
