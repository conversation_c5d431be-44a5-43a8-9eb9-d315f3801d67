'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ImageUpload } from '@/components/ui/image-upload';
import { useAuth } from '@/hooks/use-auth';
import { useCategories } from '@/hooks/use-categories';
import { PromptApi } from '@/lib/prompt-api';
import { ModelApi, ModelConfig, ModelVersion } from '@/lib/model-api';
import { toast } from 'react-hot-toast';
import { Save, Eye, Upload, X } from 'lucide-react';

export default function CreatePromptPage() {
  const router = useRouter();
  const { user, requireCreator } = useAuth();
  const { data: categories } = useCategories();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modelConfig, setModelConfig] = useState<ModelConfig>({});
  const [availableVersions, setAvailableVersions] = useState<ModelVersion[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    aiModel: 'ChatGPT',
    modelVersion: '',
    categoryId: '',
    price: '',
    originalPrice: '',
    usageInstructions: '',
    tags: '',
    isFree: false,
    imageUrl: '',
  });
  
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [exampleOutputs, setExampleOutputs] = useState<string[]>(['']);

  // 临时模型配置（直到后端API修复）
  const tempModelConfig: ModelConfig = {
    'ChatGPT': [
      { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '快速响应，成本较低' },
      { value: 'gpt-3.5-turbo-16k', label: 'GPT-3.5 Turbo 16K', description: '支持更长上下文' },
      { value: 'gpt-3.5-turbo-1106', label: 'GPT-3.5 Turbo (Nov 2023)', description: '最新优化版本' }
    ],
    'GPT-4': [
      { value: 'gpt-4', label: 'GPT-4', description: '最强推理能力' },
      { value: 'gpt-4-32k', label: 'GPT-4 32K', description: '超长上下文支持' },
      { value: 'gpt-4-1106-preview', label: 'GPT-4 Turbo', description: '最新GPT-4版本' },
      { value: 'gpt-4-vision-preview', label: 'GPT-4 Vision', description: '支持图像理解' }
    ],
    'Claude': [
      { value: 'claude-3-haiku', label: 'Claude 3 Haiku', description: '快速轻量版本' },
      { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet', description: '平衡性能版本' },
      { value: 'claude-3-opus', label: 'Claude 3 Opus', description: '最强性能版本' },
      { value: 'claude-2.1', label: 'Claude 2.1', description: '经典稳定版本' }
    ],
    'Gemini': [
      { value: 'gemini-pro', label: 'Gemini Pro', description: '多模态能力' },
      { value: 'gemini-pro-vision', label: 'Gemini Pro Vision', description: '图像理解能力' },
      { value: 'gemini-ultra', label: 'Gemini Ultra', description: '最强版本（限量）' }
    ],
    'Qwen': [
      { value: 'qwen-turbo', label: '通义千问-Turbo', description: '快速响应版本' },
      { value: 'qwen-plus', label: '通义千问-Plus', description: '增强版本' },
      { value: 'qwen-max', label: '通义千问-Max', description: '最强版本' },
      { value: 'qwen-max-longcontext', label: '通义千问-Max-LC', description: '长文本版本' }
    ]
  };

  // 加载模型配置
  useEffect(() => {
    const loadModelConfig = async () => {
      try {
        // 尝试从API获取配置
        const config = await ModelApi.getAllModelVersions();

        // 如果API返回空对象，使用临时配置
        const finalConfig = Object.keys(config).length > 0 ? config : tempModelConfig;
        console.log('模型配置加载成功:', finalConfig);
        setModelConfig(finalConfig);

        // 设置默认模型和版本
        const models = Object.keys(finalConfig);
        console.log('可用模型列表:', models);
        if (models.length > 0) {
          const defaultModel = models[0];
          const versions = finalConfig[defaultModel] || [];
          console.log('默认模型:', defaultModel, '可用版本:', versions);
          setFormData(prev => ({
            ...prev,
            aiModel: defaultModel,
            modelVersion: versions.length > 0 ? versions[0].value : ''
          }));
          setAvailableVersions(versions);
        }
      } catch (error) {
        console.error('加载模型配置失败，使用临时配置:', error);
        // 使用临时配置
        console.log('使用临时模型配置:', tempModelConfig);
        setModelConfig(tempModelConfig);
        const models = Object.keys(tempModelConfig);
        console.log('临时配置模型列表:', models);
        if (models.length > 0) {
          const defaultModel = models[0];
          const versions = tempModelConfig[defaultModel] || [];
          console.log('临时配置默认模型:', defaultModel, '版本:', versions);
          setFormData(prev => ({
            ...prev,
            aiModel: defaultModel,
            modelVersion: versions.length > 0 ? versions[0].value : ''
          }));
          setAvailableVersions(versions);
        }
      }
    };

    loadModelConfig();
  }, []);

  // 检查创作者权限
  if (!requireCreator()) {
    return null;
  }

  // 处理模型变化
  const handleModelChange = (model: string) => {
    const versions = modelConfig[model] || [];
    setAvailableVersions(versions);
    setFormData(prev => ({
      ...prev,
      aiModel: model,
      modelVersion: versions.length > 0 ? versions[0].value : ''
    }));
  };

  const handleInputChange = (field: string, value: any) => {
    if (field === 'aiModel') {
      handleModelChange(value);
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleAddExampleOutput = () => {
    setExampleOutputs(prev => [...prev, '']);
  };

  const handleRemoveExampleOutput = (index: number) => {
    setExampleOutputs(prev => prev.filter((_, i) => i !== index));
  };

  const handleExampleOutputChange = (index: number, value: string) => {
    setExampleOutputs(prev => prev.map((output, i) => i === index ? value : output));
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          toast.error(`文件 ${file.name} 格式不支持，只支持JPG、PNG、GIF、WebP格式`);
          return null;
        }

        // 验证文件大小 (5MB)
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
          toast.error(`文件 ${file.name} 大小超过5MB限制`);
          return null;
        }

        const formData = new FormData();
        formData.append('file', file);

        const token = JSON.parse(localStorage.getItem('token') || '{}').accessToken;
        if (!token) {
          toast.error('请先登录');
          return null;
        }

        const response = await fetch('http://localhost:8080/api/upload/image', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`上传 ${file.name} 失败: HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.code === 200) {
          return result.data.url;
        } else {
          throw new Error(result.message || `上传 ${file.name} 失败`);
        }
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      const validUrls = uploadedUrls.filter(url => url !== null) as string[];

      if (validUrls.length > 0) {
        setPreviewImages(prev => [...prev, ...validUrls]);
        toast.success(`成功上传 ${validUrls.length} 张图片`);
      }
    } catch (error: any) {
      console.error('批量上传图片失败:', error);
      toast.error(error.message || '图片上传失败，请重试');
    }

    // 清空input值，允许重新选择同一文件
    e.target.value = '';
  };

  const handleRemovePreviewImage = async (index: number) => {
    const imageUrl = previewImages[index];

    try {
      const token = JSON.parse(localStorage.getItem('token') || '{}').accessToken;
      if (token && imageUrl) {
        // 尝试删除服务器上的文件
        await fetch(`http://localhost:8080/api/upload/image?url=${encodeURIComponent(imageUrl)}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('删除服务器图片失败:', error);
      // 删除失败不影响UI操作
    }

    // 从预览列表中移除
    setPreviewImages(prev => prev.filter((_, i) => i !== index));
    toast.success('图片已移除');
  };

  const handleSubmit = async (isDraft = false) => {
    try {
      setIsSubmitting(true);

      // 验证必填字段
      if (!formData.title.trim()) {
        toast.error('请输入标题');
        return;
      }
      if (!formData.description.trim()) {
        toast.error('请输入描述');
        return;
      }
      if (!formData.content.trim()) {
        toast.error('请输入提示词内容');
        return;
      }
      if (!formData.categoryId) {
        toast.error('请选择分类');
        return;
      }
      if (!formData.isFree && !formData.price) {
        toast.error('请设置价格或选择免费');
        return;
      }

      const requestData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        content: formData.content.trim(),
        aiModel: formData.aiModel,
        modelVersion: formData.modelVersion,
        categoryId: Number(formData.categoryId),
        price: formData.isFree ? 0 : Number(formData.price),
        originalPrice: formData.originalPrice ? Number(formData.originalPrice) : null,
        previewImages: previewImages,
        exampleOutputs: exampleOutputs.filter(output => output.trim()),
        usageInstructions: formData.usageInstructions.trim() || null,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        isFree: formData.isFree,
        imageUrl: formData.imageUrl.trim() || null,
      };

      // 调用创建API
      const result = await PromptApi.create(requestData);
      console.log('Prompt created:', result);

      if (isDraft) {
        toast.success('草稿保存成功');
      } else {
        // 如果不是草稿，尝试发布
        try {
          await PromptApi.publish(result.id);
          toast.success('提示词创建并提交审核成功');
        } catch (publishError) {
          console.error('发布失败:', publishError);
          toast.success('提示词创建成功，但发布失败，请稍后在个人中心发布');
        }
      }

      router.push('/dashboard?tab=prompts');

    } catch (error: any) {
      toast.error(error.message || '创建失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container-custom py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">创建提示词</h1>
          <p className="text-muted-foreground">
            分享您的创意提示词，帮助更多人提升AI使用效率
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* 主要表单 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 基本信息 */}
            <div className="bg-card border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">基本信息</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    标题 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    placeholder="请输入提示词标题"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    maxLength={200}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {formData.title.length}/200
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    描述 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm min-h-[100px]"
                    placeholder="请描述这个提示词的用途和特点"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    maxLength={1000}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {formData.description.length}/1000
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      AI模型 <span className="text-red-500">*</span>
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                      value={formData.aiModel}
                      onChange={(e) => handleInputChange('aiModel', e.target.value)}
                    >
                      {Object.keys(modelConfig).length === 0 && (
                        <option value="">加载中...</option>
                      )}
                      {Object.keys(modelConfig).map(model => (
                        <option key={model} value={model}>{model}</option>
                      ))}
                    </select>
                    <div className="text-xs text-muted-foreground mt-1">
                      可用模型: {Object.keys(modelConfig).length}个
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      模型版本 <span className="text-red-500">*</span>
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                      value={formData.modelVersion}
                      onChange={(e) => handleInputChange('modelVersion', e.target.value)}
                      disabled={availableVersions.length === 0}
                    >
                      {availableVersions.map(version => (
                        <option key={version.value} value={version.value} title={version.description}>
                          {version.label}
                        </option>
                      ))}
                    </select>
                    <div className="text-xs text-muted-foreground mt-1">
                      {availableVersions.length === 0 ? '请先选择AI模型' : `可用版本: ${availableVersions.length}个`}
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      分类 <span className="text-red-500">*</span>
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                      value={formData.categoryId}
                      onChange={(e) => handleInputChange('categoryId', e.target.value)}
                    >
                      <option value="">请选择分类</option>
                      {categories?.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">标签</label>
                  <Input
                    placeholder="请输入标签，用逗号分隔"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    例如：营销,文案,创意
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">封面图片</label>
                  <ImageUpload
                    value={formData.imageUrl || undefined}
                    onChange={(url) => handleInputChange('imageUrl', url || '')}
                    disabled={isSubmitting}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    上传一张封面图片，让您的提示词更吸引人
                  </div>
                </div>
              </div>
            </div>

            {/* 提示词内容 */}
            <div className="bg-card border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">提示词内容</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    提示词内容 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm font-mono min-h-[200px]"
                    placeholder="请输入完整的提示词内容..."
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    maxLength={10000}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {formData.content.length}/10000
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">使用说明</label>
                  <textarea
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm min-h-[100px]"
                    placeholder="请说明如何使用这个提示词..."
                    value={formData.usageInstructions}
                    onChange={(e) => handleInputChange('usageInstructions', e.target.value)}
                    maxLength={2000}
                  />
                </div>
              </div>
            </div>

            {/* 示例输出 */}
            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">示例输出</h2>
                <Button variant="outline" size="sm" onClick={handleAddExampleOutput}>
                  添加示例
                </Button>
              </div>
              
              <div className="space-y-4">
                {exampleOutputs.map((output, index) => (
                  <div key={index} className="relative">
                    <textarea
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm min-h-[100px]"
                      placeholder={`示例输出 ${index + 1}`}
                      value={output}
                      onChange={(e) => handleExampleOutputChange(index, e.target.value)}
                    />
                    {exampleOutputs.length > 1 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 h-6 w-6"
                        onClick={() => handleRemoveExampleOutput(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 预览图片 */}
            <div className="bg-card border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">预览图片</h2>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    点击上传或拖拽图片到此处
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <Button variant="outline" asChild>
                    <label htmlFor="image-upload" className="cursor-pointer">
                      选择图片
                    </label>
                  </Button>
                </div>

                {previewImages.length > 0 && (
                  <div className="grid gap-4 md:grid-cols-2">
                    {previewImages.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`预览 ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6"
                          onClick={() => handleRemovePreviewImage(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 定价设置 */}
            <div className="bg-card border rounded-lg p-6 sticky top-8">
              <h3 className="font-semibold mb-4">定价设置</h3>
              
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isFree"
                    checked={formData.isFree}
                    onChange={(e) => handleInputChange('isFree', e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="isFree" className="text-sm">免费提供</label>
                </div>

                {!formData.isFree && (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        售价 (元) <span className="text-red-500">*</span>
                      </label>
                      <Input
                        type="number"
                        placeholder="0.00"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">原价 (元)</label>
                      <Input
                        type="number"
                        placeholder="0.00"
                        value={formData.originalPrice}
                        onChange={(e) => handleInputChange('originalPrice', e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        可选，用于显示折扣
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="space-y-3 mt-6">
                <Button 
                  className="w-full" 
                  onClick={() => handleSubmit(false)}
                  loading={isSubmitting}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  发布提示词
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => handleSubmit(true)}
                  loading={isSubmitting}
                >
                  <Save className="h-4 w-4 mr-2" />
                  保存草稿
                </Button>
              </div>

              <div className="text-xs text-muted-foreground mt-4">
                发布后需要审核通过才能上架销售
              </div>
            </div>

            {/* 创作提示 */}
            <div className="bg-card border rounded-lg p-6">
              <h3 className="font-semibold mb-4">创作提示</h3>
              <div className="space-y-3 text-sm text-muted-foreground">
                <div>• 标题要简洁明了，突出核心功能</div>
                <div>• 描述要详细说明使用场景和效果</div>
                <div>• 提示词内容要完整可用</div>
                <div>• 添加使用说明帮助用户理解</div>
                <div>• 提供示例输出展示效果</div>
                <div>• 合理定价，考虑内容价值</div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
