/**
 * 用户角色枚举
 */
export enum UserRole {
  USER = 'USER',
  CREATOR = 'CREATOR',
  ADMIN = 'ADMIN',
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BANNED = 'BANNED',
}

/**
 * 用户信息接口
 */
export interface User {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  avatarUrl?: string;
  bio?: string;
  role: UserRole;
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  totalEarnings: number;
  totalSpent: number;
  followerCount: number;
  followingCount: number;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: User;
}

/**
 * 微信二维码响应接口
 */
export interface WechatQRCodeResponse {
  qrCodeUrl: string;
  state: string;
  expiresIn: number;
}

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  timestamp: number;
}

/**
 * 认证状态接口
 */
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * 登录表单接口
 */
export interface LoginForm {
  code: string;
  state?: string;
}

/**
 * 令牌信息接口
 */
export interface TokenInfo {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}
