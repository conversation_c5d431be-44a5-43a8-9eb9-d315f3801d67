import { apiClient } from './api';

export interface OrderCreateRequest {
  promptIds: number[];
  paymentMethod: 'ALIPAY' | 'WECHAT';
  remark?: string;
}

export interface OrderResponse {
  id: number;
  orderNo: string;
  userId: number;
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'REFUNDED';
  paymentMethod: 'ALIPAY' | 'WECHAT';
  paymentId?: string;
  paymentTime?: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
  items: OrderItemResponse[];
}

export interface OrderItemResponse {
  id: number;
  orderId: number;
  promptId: number;
  promptTitle: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
  creatorId: number;
  commissionRate: number;
  creatorEarnings: number;
  platformEarnings: number;
}

/**
 * 订单API服务
 */
export class OrderApi {
  /**
   * 创建订单
   */
  static async create(data: OrderCreateRequest): Promise<OrderResponse> {
    return apiClient.post('/orders', data);
  }

  /**
   * 获取订单详情
   */
  static async getById(id: number): Promise<OrderResponse> {
    return apiClient.get(`/orders/${id}`);
  }

  /**
   * 获取用户订单列表
   */
  static async getUserOrders(params?: {
    status?: string;
    page?: number;
    size?: number;
  }): Promise<{
    data: OrderResponse[];
    totalPages: number;
    totalElements: number;
    currentPage: number;
  }> {
    const searchParams = new URLSearchParams();
    if (params?.status) searchParams.set('status', params.status);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.size) searchParams.set('size', params.size.toString());

    return apiClient.get(`/orders/user?${searchParams.toString()}`);
  }

  /**
   * 支付订单
   */
  static async pay(id: number): Promise<string> {
    return apiClient.post(`/orders/${id}/pay`);
  }

  /**
   * 取消订单
   */
  static async cancel(id: number): Promise<void> {
    return apiClient.post(`/orders/${id}/cancel`);
  }

  /**
   * 检查是否已购买提示词
   */
  static async checkPurchase(promptId: number): Promise<boolean> {
    return apiClient.get(`/orders/check-purchase/${promptId}`);
  }

  /**
   * 获取订单统计
   */
  static async getStats(): Promise<{
    totalOrders: number;
    totalAmount: number;
    pendingOrders: number;
    paidOrders: number;
  }> {
    return apiClient.get('/orders/stats');
  }

  /**
   * 申请退款
   */
  static async requestRefund(id: number, reason: string): Promise<void> {
    return apiClient.post(`/orders/${id}/refund`, { reason });
  }
}

/**
 * 购买提示词的便捷方法
 */
export async function purchasePrompt(promptId: number, paymentMethod: 'ALIPAY' | 'WECHAT' = 'ALIPAY'): Promise<string> {
  try {
    // 1. 创建订单
    const order = await OrderApi.create({
      promptIds: [promptId],
      paymentMethod,
    });

    // 2. 创建支付
    const paymentUrl = await OrderApi.pay(order.id);

    return paymentUrl;
  } catch (error) {
    console.error('购买失败:', error);
    throw error;
  }
}

/**
 * 购买多个提示词的便捷方法
 */
export async function purchaseMultiplePrompts(promptIds: number[], paymentMethod: 'ALIPAY' | 'WECHAT' = 'ALIPAY'): Promise<string> {
  try {
    // 1. 创建订单
    const order = await OrderApi.create({
      promptIds,
      paymentMethod,
    });

    // 2. 创建支付
    const paymentUrl = await OrderApi.pay(order.id);

    return paymentUrl;
  } catch (error) {
    console.error('批量购买失败:', error);
    throw error;
  }
}
