import { apiClient } from './api';

/**
 * 验证类型枚举
 */
export enum VerificationType {
  REGISTER = 'REGISTER',
  LOGIN = 'LOGIN',
  RESET_PASSWORD = 'RESET_PASSWORD',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  BIND_EMAIL = 'BIND_EMAIL',
}

/**
 * 发送验证码请求
 */
export interface SendVerificationCodeRequest {
  email: string;
  type: VerificationType;
}

/**
 * 邮箱登录请求
 */
export interface EmailLoginRequest {
  email: string;
  code: string;
}

/**
 * 邮箱注册请求
 */
export interface EmailRegisterRequest {
  email: string;
  username: string;
  code: string;
  bio?: string;
}

/**
 * 认证API
 */
export const authApi = {
  /**
   * 发送验证码
   */
  sendVerificationCode: async (request: SendVerificationCodeRequest): Promise<void> => {
    await apiClient.post('/auth/email/send-code', request);
  },

  /**
   * 邮箱登录
   */
  emailLogin: async (request: EmailLoginRequest) => {
    return await apiClient.post('/auth/email/login', request);
  },

  /**
   * 邮箱注册
   */
  emailRegister: async (request: EmailRegisterRequest) => {
    return await apiClient.post('/auth/email/register', request);
  },
};
