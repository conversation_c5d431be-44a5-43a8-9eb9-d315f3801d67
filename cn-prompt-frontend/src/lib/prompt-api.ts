import { apiClient } from './api';

export interface CreatePromptRequest {
  title: string;
  description: string;
  content: string;
  aiModel: string;
  categoryId: number;
  price: number;
  originalPrice?: number | null;
  previewImages: string[];
  exampleOutputs: string[];
  usageInstructions?: string | null;
  tags: string[];
  isFree: boolean;
}

export interface PromptResponse {
  id: number;
  title: string;
  description: string;
  content: string;
  aiModel: string;
  categoryId: number;
  price: number;
  originalPrice?: number;
  previewImages: string[];
  exampleOutputs: string[];
  usageInstructions?: string;
  tags: string[];
  isFree: boolean;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED';
  userId: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  viewCount: number;
  downloadCount: number;
  likeCount: number;
  ratingAvg: number;
  ratingCount: number;
  isFeatured: boolean;
}

/**
 * 提示词API服务
 */
export class PromptApi {
  /**
   * 创建提示词
   */
  static async create(data: CreatePromptRequest): Promise<PromptResponse> {
    return apiClient.post('/prompts', data);
  }

  /**
   * 发布提示词
   */
  static async publish(id: number): Promise<void> {
    return apiClient.post(`/prompts/${id}/publish`);
  }

  /**
   * 获取提示词详情
   */
  static async getById(id: number): Promise<PromptResponse> {
    return apiClient.get(`/prompts/${id}`);
  }

  /**
   * 更新提示词
   */
  static async update(id: number, data: Partial<CreatePromptRequest>): Promise<PromptResponse> {
    return apiClient.put(`/prompts/${id}`, data);
  }

  /**
   * 删除提示词
   */
  static async delete(id: number): Promise<void> {
    return apiClient.delete(`/prompts/${id}`);
  }

  /**
   * 获取用户的提示词列表
   */
  static async getUserPrompts(userId: number, params?: {
    status?: string;
    page?: number;
    size?: number;
  }): Promise<{
    data: PromptResponse[];
    totalPages: number;
    totalElements: number;
    currentPage: number;
  }> {
    const searchParams = new URLSearchParams();
    if (params?.status) searchParams.set('status', params.status);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.size) searchParams.set('size', params.size.toString());

    return apiClient.get(`/prompts/user/${userId}?${searchParams.toString()}`);
  }

  /**
   * 搜索提示词
   */
  static async search(params?: {
    keyword?: string;
    categoryId?: number;
    aiModel?: string;
    minPrice?: number;
    maxPrice?: number;
    isFree?: boolean;
    isFeatured?: boolean;
    creatorId?: number;
    sortBy?: string;
    page?: number;
    size?: number;
  }): Promise<{
    data: PromptResponse[];
    totalPages: number;
    totalElements: number;
    currentPage: number;
  }> {
    const searchParams = new URLSearchParams();
    
    if (params?.keyword) searchParams.set('keyword', params.keyword);
    if (params?.categoryId) searchParams.set('categoryId', params.categoryId.toString());
    if (params?.aiModel) searchParams.set('aiModel', params.aiModel);
    if (params?.minPrice !== undefined) searchParams.set('minPrice', params.minPrice.toString());
    if (params?.maxPrice !== undefined) searchParams.set('maxPrice', params.maxPrice.toString());
    if (params?.isFree !== undefined) searchParams.set('isFree', params.isFree.toString());
    if (params?.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());
    if (params?.creatorId) searchParams.set('creatorId', params.creatorId.toString());
    if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.size) searchParams.set('size', params.size.toString());

    return apiClient.get(`/prompts/search?${searchParams.toString()}`);
  }

  /**
   * 获取热门提示词
   */
  static async getHot(limit = 10): Promise<PromptResponse[]> {
    return apiClient.get(`/prompts/hot?limit=${limit}`);
  }

  /**
   * 获取最新提示词
   */
  static async getLatest(limit = 10): Promise<PromptResponse[]> {
    return apiClient.get(`/prompts/latest?limit=${limit}`);
  }

  /**
   * 点赞提示词
   */
  static async like(id: number): Promise<void> {
    return apiClient.post(`/prompts/${id}/like`);
  }

  /**
   * 取消点赞提示词
   */
  static async unlike(id: number): Promise<void> {
    return apiClient.delete(`/prompts/${id}/like`);
  }

  /**
   * 收藏提示词
   */
  static async favorite(id: number): Promise<void> {
    return apiClient.post(`/prompts/${id}/favorite`);
  }

  /**
   * 取消收藏提示词
   */
  static async unfavorite(id: number): Promise<void> {
    return apiClient.delete(`/prompts/${id}/favorite`);
  }
}
