import { apiClient } from './api';

/**
 * 图片API服务
 */
export class ImageApi {
  /**
   * 生成带签名的图片访问URL
   */
  static async generateSignedUrl(url: string, expireMinutes: number = 60): Promise<string> {
    try {
      if (!url) return '';
      
      // 如果不是OSS图片，直接返回原URL
      if (!url.includes('cnprompt.oss-cn-shanghai.aliyuncs.com')) {
        return url;
      }
      
      // 如果已经是签名URL，直接返回
      if (url.includes('Expires=') && url.includes('Signature=')) {
        return url;
      }
      
      // 直接使用axios而不是apiClient，避免响应拦截器的干扰
      const token = JSON.parse(localStorage.getItem('token') || '{}').accessToken;
      if (!token) {
        console.warn('未找到访问token');
        return url;
      }

      const response = await fetch(`http://localhost:8080/api/upload/image/signed-url?url=${encodeURIComponent(url)}&expireMinutes=${expireMinutes}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        console.error('API请求失败:', response.status, response.statusText);
        return url;
      }

      const data = await response.json();
      console.log('API响应数据:', data);

      // 根据实际API响应格式获取签名URL
      if (data.code === 200) {
        const signedUrl = data.data || data.message;
        if (signedUrl) {
          return signedUrl;
        }
      }

      console.warn('未找到签名URL:', data);
      return url;
    } catch (error) {
      console.error('生成签名URL失败:', error);
      console.error('错误详情:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data,
        url: url
      });

      // 如果是401错误，提示用户重新登录
      if (error.response?.status === 401) {
        console.warn('用户未授权，请重新登录');
      }

      return url; // 失败时返回原URL
    }
  }

  /**
   * 批量生成带签名的图片访问URL
   */
  static async generateBatchSignedUrls(
    urls: string[], 
    expireMinutes: number = 60
  ): Promise<Record<string, string>> {
    try {
      if (!urls || urls.length === 0) return {};
      
      // 过滤出需要签名的OSS图片
      const ossUrls = urls.filter(url => 
        url && 
        url.includes('cnprompt.oss-cn-shanghai.aliyuncs.com') &&
        !url.includes('Expires=')
      );
      
      if (ossUrls.length === 0) {
        // 如果没有需要签名的图片，返回原始映射
        const result: Record<string, string> = {};
        urls.forEach(url => {
          if (url) result[url] = url;
        });
        return result;
      }
      
      const response = await apiClient.post('/image/batch-signed-urls', ossUrls, {
        params: { expireMinutes }
      });
      
      const signedUrls = response.data || {};
      
      // 合并结果，包含所有URL
      const result: Record<string, string> = {};
      urls.forEach(url => {
        if (url) {
          result[url] = signedUrls[url] || url;
        }
      });
      
      return result;
    } catch (error) {
      console.error('批量生成签名URL失败:', error);
      // 失败时返回原始映射
      const result: Record<string, string> = {};
      urls.forEach(url => {
        if (url) result[url] = url;
      });
      return result;
    }
  }

  /**
   * 验证图片URL是否为本站图片
   */
  static async validateImageUrl(url: string): Promise<boolean> {
    try {
      if (!url) return false;
      
      const response = await apiClient.get('/image/validate', {
        params: { url }
      });
      
      return response.data === true;
    } catch (error) {
      console.error('验证图片URL失败:', error);
      return false;
    }
  }
}

/**
 * 图片URL处理工具函数
 */
export const imageUtils = {
  /**
   * 处理单个图片URL，自动生成签名
   */
  async processImageUrl(url: string, expireMinutes: number = 60): Promise<string> {
    return ImageApi.generateSignedUrl(url, expireMinutes);
  },

  /**
   * 处理多个图片URL，批量生成签名
   */
  async processImageUrls(urls: string[], expireMinutes: number = 60): Promise<string[]> {
    const signedUrls = await ImageApi.generateBatchSignedUrls(urls, expireMinutes);
    return urls.map(url => signedUrls[url] || url);
  },

  /**
   * 检查URL是否需要签名
   */
  needsSignature(url: string): boolean {
    return url && 
           url.includes('cnprompt.oss-cn-shanghai.aliyuncs.com') && 
           !url.includes('Expires=');
  },

  /**
   * 检查签名URL是否过期（基于URL中的Expires参数）
   */
  isSignatureExpired(url: string): boolean {
    try {
      if (!url.includes('Expires=')) return false;
      
      const urlObj = new URL(url);
      const expires = urlObj.searchParams.get('Expires');
      if (!expires) return false;
      
      const expiresTime = parseInt(expires) * 1000; // 转换为毫秒
      return Date.now() > expiresTime;
    } catch (error) {
      console.error('检查签名过期失败:', error);
      return true; // 出错时认为已过期
    }
  },

  /**
   * 获取图片的缓存键
   */
  getCacheKey(url: string): string {
    // 移除签名参数，使用原始URL作为缓存键
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.delete('Expires');
      urlObj.searchParams.delete('OSSAccessKeyId');
      urlObj.searchParams.delete('Signature');
      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }
};

/**
 * 图片URL缓存管理
 */
class ImageUrlCache {
  private cache = new Map<string, { url: string; expires: number }>();
  private readonly defaultTTL = 50 * 60 * 1000; // 50分钟缓存

  set(originalUrl: string, signedUrl: string, ttl: number = this.defaultTTL) {
    const expires = Date.now() + ttl;
    this.cache.set(originalUrl, { url: signedUrl, expires });
  }

  get(originalUrl: string): string | null {
    const cached = this.cache.get(originalUrl);
    if (!cached) return null;
    
    if (Date.now() > cached.expires) {
      this.cache.delete(originalUrl);
      return null;
    }
    
    return cached.url;
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

export const imageUrlCache = new ImageUrlCache();
