import { apiClient } from './api';

export interface ModelVersion {
  value: string;
  label: string;
  description: string;
}

export interface ModelConfig {
  [model: string]: ModelVersion[];
}

/**
 * 模型配置API服务
 */
export class ModelApi {
  /**
   * 获取所有模型列表
   */
  static async getAllModels(): Promise<string[]> {
    try {
      const response = await apiClient.get('/models');
      return response.data || [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  /**
   * 获取指定模型的版本列表
   */
  static async getModelVersions(model: string): Promise<ModelVersion[]> {
    try {
      const response = await apiClient.get(`/models/${encodeURIComponent(model)}/versions`);
      return response.data || [];
    } catch (error) {
      console.error('获取模型版本失败:', error);
      return [];
    }
  }

  /**
   * 获取所有模型及其版本的完整配置
   */
  static async getAllModelVersions(): Promise<ModelConfig> {
    try {
      const response = await apiClient.get('/models/all');
      return response.data || {};
    } catch (error) {
      console.error('获取完整模型配置失败:', error);
      return {};
    }
  }

  /**
   * 验证模型和版本的有效性
   */
  static async validateModelVersion(model: string, version: string): Promise<boolean> {
    try {
      const response = await apiClient.get('/models/validate', {
        params: { model, version }
      });
      return response.data || false;
    } catch (error) {
      console.error('验证模型版本失败:', error);
      return false;
    }
  }

  /**
   * 获取模型的默认版本
   */
  static async getDefaultVersion(model: string): Promise<string | null> {
    try {
      const response = await apiClient.get(`/models/${encodeURIComponent(model)}/default-version`);
      return response.data || null;
    } catch (error) {
      console.error('获取默认版本失败:', error);
      return null;
    }
  }
}
