import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, TokenInfo } from '@/types/auth';
import { toast } from 'react-hot-toast';

/**
 * API客户端类
 */
class ApiClient {
  private instance: AxiosInstance;
  private tokenInfo: TokenInfo | null = null;

  constructor() {
    this.instance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.loadTokenFromStorage();
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证头
        if (this.tokenInfo?.accessToken) {
          config.headers.Authorization = `Bearer ${this.tokenInfo.accessToken}`;
        }

        // 添加时间戳防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now(),
          };
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const { data } = response;
        
        // 检查业务状态码
        if (data.code !== 200) {
          const error = new Error(data.message || '请求失败');
          (error as any).code = data.code;
          throw error;
        }

        return response;
      },
      async (error) => {
        const { response } = error;

        // 处理401未授权错误
        if (response?.status === 401) {
          await this.handleUnauthorized();
          return Promise.reject(error);
        }

        // 处理网络错误
        if (!response) {
          toast.error('网络连接失败，请检查网络设置');
          return Promise.reject(new Error('网络连接失败'));
        }

        // 处理其他HTTP错误
        const message = response.data?.message || this.getErrorMessage(response.status);
        toast.error(message);

        return Promise.reject(error);
      }
    );
  }

  /**
   * 处理未授权错误
   */
  private async handleUnauthorized(): Promise<void> {
    if (this.tokenInfo?.refreshToken) {
      try {
        await this.refreshToken();
      } catch (error) {
        this.clearToken();
        this.redirectToLogin();
      }
    } else {
      this.clearToken();
      this.redirectToLogin();
    }
  }

  /**
   * 刷新令牌
   */
  private async refreshToken(): Promise<void> {
    if (!this.tokenInfo?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(
        `${this.instance.defaults.baseURL}/auth/refresh`,
        {},
        {
          headers: {
            Authorization: `Bearer ${this.tokenInfo.refreshToken}`,
          },
        }
      );

      const { data } = response.data;
      this.setToken({
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        expiresAt: Date.now() + data.expiresIn * 1000,
      });
    } catch (error) {
      throw new Error('Token refresh failed');
    }
  }

  /**
   * 重定向到登录页
   */
  private redirectToLogin(): void {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      if (currentPath !== '/auth/login') {
        window.location.href = `/auth/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    }
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    };

    return messages[status] || '请求失败';
  }

  /**
   * 从本地存储加载令牌
   */
  private loadTokenFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const tokenStr = localStorage.getItem('token');
      if (tokenStr) {
        const tokenInfo: TokenInfo = JSON.parse(tokenStr);
        
        // 检查令牌是否过期
        if (tokenInfo.expiresAt > Date.now()) {
          this.tokenInfo = tokenInfo;
        } else {
          localStorage.removeItem('token');
        }
      }
    } catch (error) {
      console.error('Failed to load token from storage:', error);
      localStorage.removeItem('token');
    }
  }

  /**
   * 设置令牌
   */
  public setToken(tokenInfo: TokenInfo): void {
    this.tokenInfo = tokenInfo;
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', JSON.stringify(tokenInfo));
    }
  }

  /**
   * 清除令牌
   */
  public clearToken(): void {
    this.tokenInfo = null;
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
    }
  }

  /**
   * 获取当前令牌
   */
  public getToken(): TokenInfo | null {
    return this.tokenInfo;
  }

  /**
   * 检查是否已认证
   */
  public isAuthenticated(): boolean {
    return !!(this.tokenInfo?.accessToken && this.tokenInfo.expiresAt > Date.now());
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data.data as T;
  }

  /**
   * POST请求
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  }

  /**
   * PUT请求
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data.data as T;
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data.data as T;
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient();

// 导出常用方法
export const { get, post, put, delete: del, upload } = apiClient;
