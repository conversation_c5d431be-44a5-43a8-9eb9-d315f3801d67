import Link from 'next/link';
import { Github, Twitter, Mail } from 'lucide-react';
import Logo from './logo';

export function Footer() {
  const footerLinks = {
    product: [
      { name: '浏览提示词', href: '/browse' },
      { name: '分类', href: '/categories' },
      { name: '创作者', href: '/creators' },
      { name: '定价', href: '/pricing' },
    ],
    company: [
      { name: '关于我们', href: '/about' },
      { name: '博客', href: '/blog' },
      { name: '招聘', href: '/careers' },
      { name: '联系我们', href: '/contact' },
    ],
    support: [
      { name: '帮助中心', href: '/help' },
      { name: '使用指南', href: '/guide' },
      { name: '常见问题', href: '/faq' },
      { name: '反馈建议', href: '/feedback' },
    ],
    legal: [
      { name: '服务条款', href: '/terms' },
      { name: '隐私政策', href: '/privacy' },
      { name: '版权声明', href: '/copyright' },
      { name: '免责声明', href: '/disclaimer' },
    ],
  };

  return (
    <footer className="bg-muted/50 border-t">
      <div className="container-custom">
        <div className="py-12">
          <div className="grid gap-8 lg:grid-cols-5">
            {/* Logo和描述 */}
            <div className="lg:col-span-2">
              <Link href="/" className="flex items-center space-x-2 mb-4">
                <Logo />
                <span className="text-xl font-bold">AI提示词</span>
              </Link>
              <p className="text-muted-foreground mb-6 max-w-md">
                专业的中文AI提示词交易平台，汇聚全球优秀创作者，提供高质量的AI提示词服务。
              </p>
              <div className="flex space-x-4">
                <Link
                  href="https://github.com"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Github className="h-5 w-5" />
                  <span className="sr-only">GitHub</span>
                </Link>
                <Link
                  href="https://twitter.com"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Twitter className="h-5 w-5" />
                  <span className="sr-only">Twitter</span>
                </Link>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Mail className="h-5 w-5" />
                  <span className="sr-only">邮箱</span>
                </Link>
              </div>
            </div>

            {/* 链接列 */}
            <div>
              <h3 className="font-semibold mb-4">产品</h3>
              <ul className="space-y-2">
                {footerLinks.product.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">公司</h3>
              <ul className="space-y-2">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">支持</h3>
              <ul className="space-y-2">
                {footerLinks.support.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="text-muted-foreground text-sm">
              © 2025 AI提示词. 保留所有权利.
            </p>
            <div className="flex space-x-6 mt-4 sm:mt-0">
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
