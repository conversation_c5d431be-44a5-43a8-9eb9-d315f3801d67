'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { formatPrice, formatRelativeTime } from '@/lib/utils';
import { purchasePrompt } from '@/lib/order-api';
import { Heart, Download, Eye, Star, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';

interface PromptCardProps {
  prompt: {
    id: number;
    title: string;
    description: string;
    price: number;
    originalPrice?: number;
    viewCount: number;
    downloadCount: number;
    likeCount: number;
    ratingAvg: number;
    ratingCount: number;
    isFeatured: boolean;
    isFree: boolean;
    createdAt: string;
    creator?: {
      id: number;
      username: string;
      avatarUrl?: string;
    };
    categoryName?: string;
    previewImages?: string[];
    isPurchased?: boolean;
    isLiked?: boolean;
  };
  viewMode?: 'grid' | 'list';
}

export function PromptCard({ prompt, viewMode = 'grid' }: PromptCardProps) {
  const [isLiked, setIsLiked] = useState(prompt.isLiked || false);
  const [likeCount, setLikeCount] = useState(prompt.likeCount);

  const handleLike = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // TODO: 实现点赞功能
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  const handlePurchase = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      if (prompt.isFree) {
        // 免费提示词直接获取
        toast.success('免费提示词获取成功！');
        // 可以在这里更新状态或刷新数据
      } else {
        // 付费提示词创建订单并跳转支付
        const paymentUrl = await purchasePrompt(prompt.id, 'ALIPAY');

        // 跳转到支付页面
        window.location.href = paymentUrl;
      }
    } catch (error: any) {
      console.error('购买失败:', error);
      toast.error(error.message || '购买失败，请重试');
    }
  };

  if (viewMode === 'list') {
    return (
      <Link href={`/prompt/${prompt.id}`}>
        <div className="bg-card border rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
          <div className="flex gap-6">
            {/* 预览图 */}
            <div className="flex-shrink-0">
              <div className="w-32 h-24 bg-muted rounded-lg overflow-hidden">
                {prompt.previewImages?.[0] ? (
                  <Image
                    src={prompt.previewImages[0]}
                    alt={prompt.title}
                    width={128}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                    <ShoppingCart className="h-8 w-8" />
                  </div>
                )}
              </div>
            </div>

            {/* 内容 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-semibold truncate">{prompt.title}</h3>
                    {prompt.isFeatured && (
                      <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                        精选
                      </span>
                    )}
                    {prompt.isFree && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        免费
                      </span>
                    )}
                  </div>
                  
                  <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                    {prompt.description}
                  </p>

                  {/* 创作者和分类 */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                    {prompt.creator && (
                      <div className="flex items-center gap-1">
                        <div className="w-4 h-4 bg-gradient-to-r from-primary to-secondary rounded-full" />
                        <span>{prompt.creator.username}</span>
                      </div>
                    )}
                    {prompt.categoryName && (
                      <span>分类: {prompt.categoryName}</span>
                    )}
                    <span>{formatRelativeTime(prompt.createdAt)}</span>
                  </div>

                  {/* 统计信息 */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      <span>{prompt.viewCount}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span>{prompt.downloadCount}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current text-yellow-400" />
                      <span>{prompt.ratingAvg.toFixed(1)} ({prompt.ratingCount})</span>
                    </div>
                  </div>
                </div>

                {/* 价格和操作 */}
                <div className="flex-shrink-0 text-right">
                  <div className="mb-3">
                    {prompt.isFree ? (
                      <div className="text-lg font-bold text-green-600">免费</div>
                    ) : (
                      <div>
                        <div className="text-lg font-bold text-primary">
                          {formatPrice(prompt.price)}
                        </div>
                        {prompt.originalPrice && prompt.originalPrice > prompt.price && (
                          <div className="text-xs text-muted-foreground line-through">
                            {formatPrice(prompt.originalPrice)}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLike}
                      className={cn(
                        "h-8 w-8 p-0",
                        isLiked && "text-red-500"
                      )}
                    >
                      <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
                    </Button>
                    
                    {prompt.isPurchased ? (
                      <Button size="sm" variant="outline">
                        已购买
                      </Button>
                    ) : (
                      <Button size="sm" onClick={handlePurchase}>
                        {prompt.isFree ? '获取' : '购买'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link href={`/prompt/${prompt.id}`}>
      <div className="bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
        {/* 预览图 */}
        <div className="aspect-video bg-muted relative">
          {prompt.previewImages?.[0] ? (
            <Image
              src={prompt.previewImages[0]}
              alt={prompt.title}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <ShoppingCart className="h-12 w-12" />
            </div>
          )}
          
          {/* 标签 */}
          <div className="absolute top-2 left-2 flex gap-1">
            {prompt.isFeatured && (
              <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                精选
              </span>
            )}
            {prompt.isFree && (
              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                免费
              </span>
            )}
          </div>

          {/* 点赞按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLike}
            className={cn(
              "absolute top-2 right-2 h-8 w-8 p-0 bg-background/80 hover:bg-background",
              isLiked && "text-red-500"
            )}
          >
            <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
          </Button>
        </div>

        {/* 内容 */}
        <div className="p-4">
          <h3 className="font-semibold mb-2 line-clamp-1">{prompt.title}</h3>
          <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
            {prompt.description}
          </p>

          {/* 创作者 */}
          {prompt.creator && (
            <div className="flex items-center gap-2 mb-3">
              <div className="w-5 h-5 bg-gradient-to-r from-primary to-secondary rounded-full" />
              <span className="text-sm text-muted-foreground">{prompt.creator.username}</span>
            </div>
          )}

          {/* 统计信息 */}
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{prompt.viewCount}</span>
              </div>
              <div className="flex items-center gap-1">
                <Download className="h-3 w-3" />
                <span>{prompt.downloadCount}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 fill-current text-yellow-400" />
              <span>{prompt.ratingAvg.toFixed(1)}</span>
            </div>
          </div>

          {/* 价格和购买 */}
          <div className="flex items-center justify-between">
            {prompt.isFree ? (
              <div className="text-lg font-bold text-green-600">免费</div>
            ) : (
              <div>
                <div className="text-lg font-bold text-primary">
                  {formatPrice(prompt.price)}
                </div>
                {prompt.originalPrice && prompt.originalPrice > prompt.price && (
                  <div className="text-xs text-muted-foreground line-through">
                    {formatPrice(prompt.originalPrice)}
                  </div>
                )}
              </div>
            )}

            {prompt.isPurchased ? (
              <Button size="sm" variant="outline">
                已购买
              </Button>
            ) : (
              <Button size="sm" onClick={handlePurchase}>
                {prompt.isFree ? '获取' : '购买'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
