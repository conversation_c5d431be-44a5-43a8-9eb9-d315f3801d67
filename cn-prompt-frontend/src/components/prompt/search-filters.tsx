'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCategories } from '@/hooks/use-categories';

interface SearchFiltersProps {
  initialFilters?: {
    categoryId?: number | null;
    aiModel?: string;
    minPrice?: number;
    maxPrice?: number;
    isFree?: boolean;
    isFeatured?: boolean;
    sortBy?: string;
  };
  onChange: (filters: any) => void;
}

export function SearchFilters({ initialFilters = {}, onChange }: SearchFiltersProps) {
  const [filters, setFilters] = useState({
    categoryId: initialFilters.categoryId || null,
    aiModel: initialFilters.aiModel || '',
    minPrice: initialFilters.minPrice || '',
    maxPrice: initialFilters.maxPrice || '',
    isFree: initialFilters.isFree || false,
    isFeatured: initialFilters.isFeatured || false,
    sortBy: initialFilters.sortBy || 'newest',
  });

  const { data: categories } = useCategories();

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onChange(newFilters);
  };

  const handleReset = () => {
    const resetFilters = {
      categoryId: null,
      aiModel: '',
      minPrice: '',
      maxPrice: '',
      isFree: false,
      isFeatured: false,
      sortBy: 'newest',
    };
    setFilters(resetFilters);
    onChange(resetFilters);
  };

  const sortOptions = [
    { value: 'newest', label: '最新发布' },
    { value: 'popular', label: '最受欢迎' },
    { value: 'rating', label: '评分最高' },
    { value: 'price_asc', label: '价格从低到高' },
    { value: 'price_desc', label: '价格从高到低' },
  ];

  const aiModels = [
    'ChatGPT',
    'Claude',
    'Midjourney',
    'Stable Diffusion',
    'DALL-E',
    'GPT-4',
    'Gemini',
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">筛选条件</h3>
        <Button variant="outline" size="sm" onClick={handleReset}>
          重置
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* 分类筛选 */}
        <div>
          <label className="text-sm font-medium mb-2 block">分类</label>
          <select
            value={filters.categoryId || ''}
            onChange={(e) => handleFilterChange('categoryId', e.target.value ? Number(e.target.value) : null)}
            className="w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="">全部分类</option>
            {categories?.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* AI模型筛选 */}
        <div>
          <label className="text-sm font-medium mb-2 block">AI模型</label>
          <select
            value={filters.aiModel}
            onChange={(e) => handleFilterChange('aiModel', e.target.value)}
            className="w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="">全部模型</option>
            {aiModels.map((model) => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>
        </div>

        {/* 排序方式 */}
        <div>
          <label className="text-sm font-medium mb-2 block">排序方式</label>
          <select
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
            className="w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* 价格范围 */}
        <div>
          <label className="text-sm font-medium mb-2 block">价格范围</label>
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="最低价"
              value={filters.minPrice}
              onChange={(e) => handleFilterChange('minPrice', e.target.value)}
              className="text-sm"
            />
            <span className="flex items-center text-muted-foreground">-</span>
            <Input
              type="number"
              placeholder="最高价"
              value={filters.maxPrice}
              onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
              className="text-sm"
            />
          </div>
        </div>

        {/* 特殊筛选 */}
        <div className="md:col-span-2">
          <label className="text-sm font-medium mb-2 block">特殊筛选</label>
          <div className="flex gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.isFree}
                onChange={(e) => handleFilterChange('isFree', e.target.checked)}
                className="rounded border-input"
              />
              <span className="text-sm">仅显示免费</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.isFeatured}
                onChange={(e) => handleFilterChange('isFeatured', e.target.checked)}
                className="rounded border-input"
              />
              <span className="text-sm">仅显示精选</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
