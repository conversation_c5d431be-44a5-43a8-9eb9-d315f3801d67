'use client';

import { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/use-auth';
import { cn } from '@/lib/utils';

interface FavoriteButtonProps {
  promptId: number;
  className?: string;
  showText?: boolean;
}

export function FavoriteButton({ promptId, className, showText = false }: FavoriteButtonProps) {
  const { isAuthenticated, requireAuth } = useAuth();
  const [isFavorited, setIsFavorited] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      checkFavoriteStatus();
    }
  }, [promptId, isAuthenticated]);

  const checkFavoriteStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/favorites/${promptId}/status`, {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setIsFavorited(result.data);
        }
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!requireAuth()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/favorites/${promptId}/toggle`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('操作失败');

      const result = await response.json();
      if (result.code === 200) {
        setIsFavorited(result.data);
        toast.success(result.data ? '已添加到收藏' : '已取消收藏');
      } else {
        throw new Error(result.message || '操作失败');
      }
    } catch (error: any) {
      console.error('收藏操作失败:', error);
      toast.error(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <button
        onClick={() => requireAuth()}
        className={cn(
          'flex items-center space-x-1 px-3 py-1.5 rounded-md border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors',
          className
        )}
      >
        <Heart className="w-4 h-4" />
        {showText && <span className="text-sm">收藏</span>}
      </button>
    );
  }

  return (
    <button
      onClick={toggleFavorite}
      disabled={loading}
      className={cn(
        'flex items-center space-x-1 px-3 py-1.5 rounded-md border transition-colors disabled:opacity-50',
        isFavorited
          ? 'border-red-300 bg-red-50 text-red-600 hover:bg-red-100'
          : 'border-gray-300 text-gray-600 hover:bg-gray-50',
        className
      )}
    >
      <Heart 
        className={cn(
          'w-4 h-4 transition-colors',
          isFavorited ? 'fill-current' : ''
        )} 
      />
      {showText && (
        <span className="text-sm">
          {loading ? '处理中...' : (isFavorited ? '已收藏' : '收藏')}
        </span>
      )}
    </button>
  );
}
