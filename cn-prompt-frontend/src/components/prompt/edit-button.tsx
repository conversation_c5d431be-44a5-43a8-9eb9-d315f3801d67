'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Edit3, Lock } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { cn } from '@/lib/utils';

interface EditButtonProps {
  promptId: number;
  authorId: number;
  auditStatus?: string;
  className?: string;
  showText?: boolean;
}

export function EditButton({ 
  promptId, 
  authorId, 
  auditStatus = 'APPROVED',
  className, 
  showText = true 
}: EditButtonProps) {
  const { user, isAuthenticated } = useAuth();
  const [canEdit, setCanEdit] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkEditPermission();
  }, [promptId, user, isAuthenticated]);

  const checkEditPermission = async () => {
    if (!isAuthenticated || !user) {
      setCanEdit(false);
      setLoading(false);
      return;
    }

    // 检查是否为作者
    if (user.id !== authorId) {
      setCanEdit(false);
      setLoading(false);
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setCanEdit(false);
        setLoading(false);
        return;
      }

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/prompts/${promptId}/edit`, {
        method: 'HEAD', // 只检查权限，不获取数据
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      setCanEdit(response.ok);
    } catch (error) {
      console.error('检查编辑权限失败:', error);
      setCanEdit(false);
    } finally {
      setLoading(false);
    }
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className={cn('flex items-center space-x-1', className)}>
        <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
        {showText && <span className="text-sm text-gray-500">检查权限...</span>}
      </div>
    );
  }

  // 如果没有编辑权限，不显示按钮
  if (!canEdit) {
    return null;
  }

  // 如果正在审核中，显示禁用状态
  if (auditStatus === 'PENDING') {
    return (
      <div 
        className={cn(
          'flex items-center space-x-1 px-3 py-1.5 rounded-md border border-gray-300 text-gray-400 cursor-not-allowed',
          className
        )}
        title="提示词正在审核中，暂时无法编辑"
      >
        <Lock className="w-4 h-4" />
        {showText && <span className="text-sm">审核中</span>}
      </div>
    );
  }

  return (
    <Link
      href={`/prompts/${promptId}/edit`}
      className={cn(
        'flex items-center space-x-1 px-3 py-1.5 rounded-md border border-blue-300 text-blue-600 hover:bg-blue-50 transition-colors',
        className
      )}
    >
      <Edit3 className="w-4 h-4" />
      {showText && <span className="text-sm">编辑</span>}
    </Link>
  );
}
