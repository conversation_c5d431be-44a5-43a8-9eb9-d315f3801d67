'use client';

import { useState, useEffect } from 'react';
import { Star, User } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/use-auth';
import { cn } from '@/lib/utils';

interface Review {
  id: number;
  rating: number;
  content: string;
  createdAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
}

interface ReviewStatistics {
  averageRating: number;
  totalCount: number;
  ratingDistribution: {
    [key: number]: number;
  };
}

interface ReviewSectionProps {
  promptId: number;
}

export function ReviewSection({ promptId }: ReviewSectionProps) {
  const { isAuthenticated, requireAuth, user } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [statistics, setStatistics] = useState<ReviewStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // 评价表单
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [content, setContent] = useState('');
  const [userReview, setUserReview] = useState<Review | null>(null);

  useEffect(() => {
    loadReviews();
    loadStatistics();
    if (isAuthenticated) {
      checkUserReview();
    }
  }, [promptId, isAuthenticated]);

  const loadReviews = async () => {
    try {
      const response = await fetch(`http://localhost:8080/api/reviews/${promptId}?page=1&size=10`);
      if (!response.ok) throw new Error('获取评价失败');

      const result = await response.json();
      if (result.code === 200) {
        setReviews(result.data.content || []);
      }
    } catch (error: any) {
      console.error('加载评价失败:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch(`http://localhost:8080/api/reviews/${promptId}/statistics`);
      if (!response.ok) throw new Error('获取统计失败');

      const result = await response.json();
      if (result.code === 200) {
        setStatistics(result.data);
      }
    } catch (error: any) {
      console.error('加载统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkUserReview = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/reviews/my?promptId=${promptId}`, {
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setUserReview(result.data);
        }
      }
    } catch (error) {
      console.error('检查用户评价失败:', error);
    }
  };

  const submitReview = async () => {
    if (!requireAuth()) return;
    
    if (rating === 0) {
      toast.error('请选择评分');
      return;
    }

    setSubmitting(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/reviews/${promptId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rating,
          content: content.trim()
        })
      });

      if (!response.ok) throw new Error('提交评价失败');

      const result = await response.json();
      if (result.code === 200) {
        toast.success('评价提交成功');
        setShowReviewForm(false);
        setRating(0);
        setContent('');
        loadReviews();
        loadStatistics();
        checkUserReview();
      } else {
        throw new Error(result.message || '提交评价失败');
      }
    } catch (error: any) {
      console.error('提交评价失败:', error);
      toast.error(error.message || '提交评价失败');
    } finally {
      setSubmitting(false);
    }
  };

  const deleteReview = async () => {
    if (!userReview) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('未登录');

      const tokenData = JSON.parse(token);
      const response = await fetch(`http://localhost:8080/api/reviews/${promptId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${tokenData.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('删除评价失败');

      const result = await response.json();
      if (result.code === 200) {
        toast.success('评价已删除');
        setUserReview(null);
        loadReviews();
        loadStatistics();
      } else {
        throw new Error(result.message || '删除评价失败');
      }
    } catch (error: any) {
      console.error('删除评价失败:', error);
      toast.error(error.message || '删除评价失败');
    }
  };

  const renderStars = (rating: number, interactive = false, size = 'w-5 h-5') => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              size,
              'cursor-pointer transition-colors',
              star <= (interactive ? (hoverRating || rating) : rating)
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            )}
            onClick={interactive ? () => setRating(star) : undefined}
            onMouseEnter={interactive ? () => setHoverRating(star) : undefined}
            onMouseLeave={interactive ? () => setHoverRating(0) : undefined}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* 评价统计 */}
      {statistics && (
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">用户评价</h3>
          
          <div className="flex items-center space-x-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">
                {statistics.averageRating.toFixed(1)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {renderStars(Math.round(statistics.averageRating))}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {statistics.totalCount} 条评价
              </div>
            </div>
            
            <div className="flex-1">
              {[5, 4, 3, 2, 1].map((star) => (
                <div key={star} className="flex items-center mb-1">
                  <span className="text-sm text-gray-600 w-8">{star}星</span>
                  <div className="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full"
                      style={{
                        width: `${statistics.totalCount > 0 
                          ? (statistics.ratingDistribution[star] || 0) / statistics.totalCount * 100 
                          : 0}%`
                      }}
                    />
                  </div>
                  <span className="text-sm text-gray-500 w-8">
                    {statistics.ratingDistribution[star] || 0}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 用户评价表单 */}
      <div className="p-6 border-b border-gray-200">
        {userReview ? (
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-900">您的评价</h4>
              <button
                onClick={deleteReview}
                className="text-sm text-red-600 hover:text-red-800"
              >
                删除
              </button>
            </div>
            <div className="flex items-center mb-2">
              {renderStars(userReview.rating)}
              <span className="ml-2 text-sm text-gray-600">
                {new Date(userReview.createdAt).toLocaleDateString()}
              </span>
            </div>
            {userReview.content && (
              <p className="text-gray-700">{userReview.content}</p>
            )}
          </div>
        ) : isAuthenticated ? (
          <div>
            {!showReviewForm ? (
              <button
                onClick={() => setShowReviewForm(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                写评价
              </button>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    评分 <span className="text-red-500">*</span>
                  </label>
                  {renderStars(rating, true)}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    评价内容
                  </label>
                  <textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="分享您的使用体验..."
                    maxLength={500}
                  />
                  <div className="text-right text-sm text-gray-500 mt-1">
                    {content.length}/500
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <button
                    onClick={submitReview}
                    disabled={submitting || rating === 0}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {submitting ? '提交中...' : '提交评价'}
                  </button>
                  <button
                    onClick={() => {
                      setShowReviewForm(false);
                      setRating(0);
                      setContent('');
                    }}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-gray-600 mb-4">登录后可以发表评价</p>
            <button
              onClick={() => requireAuth()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              立即登录
            </button>
          </div>
        )}
      </div>

      {/* 评价列表 */}
      <div className="p-6">
        <h4 className="font-medium text-gray-900 mb-4">全部评价</h4>
        
        {reviews.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无评价
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <div key={review.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {review.user.avatar ? (
                      <img
                        src={review.user.avatar}
                        alt={review.user.username}
                        className="w-8 h-8 rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <User className="w-4 h-4 text-gray-600" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-gray-900">{review.user.username}</span>
                      {renderStars(review.rating, false, 'w-4 h-4')}
                      <span className="text-sm text-gray-500">
                        {new Date(review.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    
                    {review.content && (
                      <p className="text-gray-700">{review.content}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
