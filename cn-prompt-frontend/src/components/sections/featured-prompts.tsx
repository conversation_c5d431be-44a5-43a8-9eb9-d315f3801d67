'use client';

import { But<PERSON> } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { PromptCard } from '@/components/prompt/prompt-card';
import { useHotPrompts } from '@/hooks/use-prompts';
import Link from 'next/link';

export function FeaturedPrompts() {
  const { data: prompts, isLoading, error } = useHotPrompts(6);

  return (
    <section className="py-20">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">精选提示词</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            发现最受欢迎和高质量的AI提示词，提升您的创作效率
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground mb-4">加载失败</p>
            <Button variant="outline" onClick={() => window.location.reload()}>
              重新加载
            </Button>
          </div>
        ) : prompts && prompts.length > 0 ? (
          <>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {prompts.map((prompt) => (
                <PromptCard key={prompt.id} prompt={prompt} />
              ))}
            </div>

            <div className="text-center mt-12">
              <Button variant="outline" size="lg" asChild>
                <Link href="/browse">查看更多提示词</Link>
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暂无精选提示词</p>
          </div>
        )}
      </div>
    </section>
  );
}
