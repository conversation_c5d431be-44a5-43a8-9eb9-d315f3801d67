export function Stats() {
  const stats = [
    { label: '提示词总数', value: '10,000+' },
    { label: '注册用户', value: '50,000+' },
    { label: '创作者', value: '2,000+' },
    { label: '交易次数', value: '100,000+' },
  ];

  return (
    <section className="py-16 bg-muted/50">
      <div className="container-custom">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-primary">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
