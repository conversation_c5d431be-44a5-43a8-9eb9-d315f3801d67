'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap, Users } from 'lucide-react';
import Link from 'next/link';

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 sm:py-32">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="h-[800px] w-[800px] rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 blur-3xl" />
        </div>
      </div>

      <div className="container-custom">
        <div className="mx-auto max-w-4xl text-center">
          {/* 标题 */}
          <div className="mb-8">
            <div className="mb-4 inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary">
              <Sparkles className="mr-2 h-4 w-4" />
              专业的中文AI提示词交易平台
            </div>
            <h1 className="mb-6 text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              发现优质
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                AI提示词
              </span>
              <br />
              释放创作潜能
            </h1>
            <p className="mx-auto max-w-2xl text-lg text-muted-foreground sm:text-xl">
              汇聚全球优秀创作者，提供高质量的中文AI提示词。
              无论您是AI新手还是专业用户，都能在这里找到完美的提示词。
            </p>
          </div>

          {/* 行动按钮 */}
          <div className="mb-12 flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Button size="lg" className="group" asChild>
              <Link href="/browse">
                开始探索
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/auth/login">
                立即登录
              </Link>
            </Button>
          </div>

          {/* 特性卡片 */}
          <div className="grid gap-6 sm:grid-cols-3">
            <div className="rounded-lg border bg-card p-6 text-center shadow-sm">
              <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">高质量内容</h3>
              <p className="text-sm text-muted-foreground">
                经过精心筛选的优质提示词，确保每一个都能产生出色的AI输出效果
              </p>
            </div>

            <div className="rounded-lg border bg-card p-6 text-center shadow-sm">
              <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">专业社区</h3>
              <p className="text-sm text-muted-foreground">
                汇聚AI领域的专业创作者和用户，分享经验，共同成长
              </p>
            </div>

            <div className="rounded-lg border bg-card p-6 text-center shadow-sm">
              <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">中文优化</h3>
              <p className="text-sm text-muted-foreground">
                专门针对中文语境优化的提示词，更符合中文用户的使用习惯
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
