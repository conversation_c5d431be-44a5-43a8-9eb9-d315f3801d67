export function Testimonials() {
  const testimonials = [
    {
      name: '张小明',
      role: '内容创作者',
      avatar: '/placeholder-avatar.jpg',
      content: '这个平台上的提示词质量非常高，大大提升了我的创作效率。特别是文案写作类的提示词，效果超出预期。',
      rating: 5,
    },
    {
      name: '李小红',
      role: 'UI设计师',
      avatar: '/placeholder-avatar.jpg',
      content: 'AI绘画提示词非常专业，帮助我快速生成各种风格的设计素材。客服响应也很及时，体验很好。',
      rating: 5,
    },
    {
      name: '王小华',
      role: '程序员',
      avatar: '/placeholder-avatar.jpg',
      content: '代码相关的提示词很实用，不仅能生成代码，还能帮助写文档和注释。对提升开发效率很有帮助。',
      rating: 4,
    },
  ];

  return (
    <section className="py-20 bg-muted/50">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">用户评价</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            看看其他用户对我们平台的评价
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-background border rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full mr-4" />
                <div>
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="flex mb-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span
                      key={i}
                      className={`text-sm ${
                        i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      ⭐
                    </span>
                  ))}
                </div>
                <p className="text-muted-foreground text-sm">{testimonial.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
