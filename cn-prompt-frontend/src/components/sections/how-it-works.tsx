import { Search, ShoppingCart, Download, Star } from 'lucide-react';

export function HowItWorks() {
  const steps = [
    {
      icon: Search,
      title: '浏览发现',
      description: '在我们的平台上浏览和搜索各种高质量的AI提示词',
    },
    {
      icon: ShoppingCart,
      title: '选择购买',
      description: '选择适合您需求的提示词，支持多种支付方式',
    },
    {
      icon: Download,
      title: '立即使用',
      description: '购买后立即获得提示词，可以在各种AI工具中使用',
    },
    {
      icon: Star,
      title: '评价反馈',
      description: '使用后可以评价和反馈，帮助其他用户做出选择',
    },
  ];

  return (
    <section className="py-20">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">如何使用</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            简单四步，轻松获得高质量的AI提示词
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <Icon className="h-8 w-8 text-primary" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                <p className="text-muted-foreground text-sm">{step.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
