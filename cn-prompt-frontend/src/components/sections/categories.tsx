'use client';

import Link from 'next/link';
import { FileText, Image, Code, MessageSquare, Palette, Zap } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRootCategories } from '@/hooks/use-categories';

// 图标映射
const iconMap: Record<string, any> = {
  '文案写作': FileText,
  'AI绘画': Image,
  '代码编程': Code,
  '对话聊天': MessageSquare,
  '创意设计': Palette,
  '效率工具': Zap,
};

export function Categories() {
  const { data: categories, isLoading, error } = useRootCategories();

  // 默认图标
  const getIcon = (name: string) => {
    return iconMap[name] || FileText;
  };

  return (
    <section className="py-20 bg-muted/50">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">热门分类</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            按分类浏览提示词，快速找到您需要的内容
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">加载分类失败</p>
          </div>
        ) : categories && categories.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {categories.map((category) => {
              const Icon = getIcon(category.name);
              return (
                <Link
                  key={category.id}
                  href={`/browse?categoryId=${category.id}`}
                  className="group block p-6 bg-background border rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg mr-4 group-hover:bg-primary/20 transition-colors">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {category.name}
                      </h3>
                      <p className="text-sm text-muted-foreground">{category.promptCount} 个提示词</p>
                    </div>
                  </div>
                  <p className="text-muted-foreground text-sm">{category.description}</p>
                </Link>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暂无分类数据</p>
          </div>
        )}
      </div>
    </section>
  );
}
