'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  value?: string;
  onChange: (url: string | null) => void;
  disabled?: boolean;
  className?: string;
}

export function ImageUpload({ value, onChange, disabled, className }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('只支持JPG、PNG、GIF、WebP格式的图片');
      return;
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('图片大小不能超过5MB');
      return;
    }

    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append('file', file);

      const token = JSON.parse(localStorage.getItem('token') || '{}').accessToken;
      if (!token) {
        toast.error('请先登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/upload/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.code === 200) {
        onChange(result.data.url);
        toast.success('图片上传成功');
      } else {
        throw new Error(result.message || '上传失败');
      }

    } catch (error: any) {
      console.error('图片上传失败:', error);
      toast.error(error.message || '图片上传失败，请重试');
    } finally {
      setIsUploading(false);
      // 清空input值，允许重新选择同一文件
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemove = async () => {
    if (!value) return;

    try {
      const token = JSON.parse(localStorage.getItem('token') || '{}').accessToken;
      if (token) {
        // 尝试删除服务器上的文件
        await fetch(`http://localhost:8080/api/upload/image?url=${encodeURIComponent(value)}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('删除图片失败:', error);
      // 删除失败不影响UI操作
    }

    onChange(null);
    toast.success('图片已移除');
  };

  const handleClick = () => {
    if (disabled || isUploading) return;
    fileInputRef.current?.click();
  };

  return (
    <div className={cn('space-y-2', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled || isUploading}
      />

      {value ? (
        <div className="relative group">
          <div className="relative w-full h-48 border-2 border-dashed border-gray-300 rounded-lg overflow-hidden">
            <img
              src={value}
              alt="上传的图片"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={handleRemove}
                disabled={disabled || isUploading}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <X className="h-4 w-4 mr-1" />
                移除
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div
          onClick={handleClick}
          className={cn(
            'w-full h-48 border-2 border-dashed border-gray-300 rounded-lg',
            'flex flex-col items-center justify-center cursor-pointer',
            'hover:border-gray-400 hover:bg-gray-50 transition-colors duration-200',
            disabled && 'cursor-not-allowed opacity-50',
            isUploading && 'cursor-not-allowed'
          )}
        >
          {isUploading ? (
            <div className="flex flex-col items-center">
              <LoadingSpinner size="lg" />
              <p className="mt-2 text-sm text-gray-500">上传中...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <div className="p-3 bg-gray-100 rounded-full mb-2">
                <ImageIcon className="h-6 w-6 text-gray-400" />
              </div>
              <p className="text-sm font-medium text-gray-700 mb-1">点击上传图片</p>
              <p className="text-xs text-gray-500">支持JPG、PNG、GIF、WebP格式，最大5MB</p>
            </div>
          )}
        </div>
      )}

      {!value && (
        <Button
          type="button"
          variant="outline"
          onClick={handleClick}
          disabled={disabled || isUploading}
          className="w-full"
        >
          <Upload className="h-4 w-4 mr-2" />
          {isUploading ? '上传中...' : '选择图片'}
        </Button>
      )}
    </div>
  );
}
