'use client';

import { useState, useEffect, useRef } from 'react';
import { ImageApi, imageUtils, imageUrlCache } from '@/lib/image-api';
import { cn } from '@/lib/utils';
import { ImageIcon, AlertCircle } from 'lucide-react';

interface SecureImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallback?: React.ReactNode;
  expireMinutes?: number;
  className?: string;
  onSignedUrlGenerated?: (signedUrl: string) => void;
}

/**
 * 安全图片组件
 * 自动处理OSS私有图片的签名URL生成
 */
export function SecureImage({
  src,
  alt,
  fallback,
  expireMinutes = 60,
  className,
  onSignedUrlGenerated,
  ...props
}: SecureImageProps) {
  const [signedUrl, setSignedUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 2;
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    generateSignedUrl();
  }, [src, expireMinutes, retryCount]);

  const generateSignedUrl = async () => {
    try {
      setIsLoading(true);
      setHasError(false);

      // 检查缓存
      const cacheKey = imageUtils.getCacheKey(src);
      const cachedUrl = imageUrlCache.get(cacheKey);
      
      if (cachedUrl && !imageUtils.isSignatureExpired(cachedUrl)) {
        if (mountedRef.current) {
          setSignedUrl(cachedUrl);
          setIsLoading(false);
          onSignedUrlGenerated?.(cachedUrl);
        }
        return;
      }

      // 生成新的签名URL
      const newSignedUrl = await ImageApi.generateSignedUrl(src, expireMinutes);
      
      if (mountedRef.current) {
        if (newSignedUrl && newSignedUrl !== src) {
          // 缓存签名URL
          imageUrlCache.set(cacheKey, newSignedUrl, (expireMinutes - 5) * 60 * 1000);
          setSignedUrl(newSignedUrl);
          onSignedUrlGenerated?.(newSignedUrl);
        } else {
          setSignedUrl(src);
        }
        setIsLoading(false);
      }
    } catch (error) {
      console.error('生成签名URL失败:', error);
      if (mountedRef.current) {
        setHasError(true);
        setIsLoading(false);
        // 失败时使用原URL
        setSignedUrl(src);
      }
    }
  };

  const handleImageError = () => {
    if (retryCount < maxRetries) {
      // 重试生成签名URL
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
      }, 1000 * (retryCount + 1)); // 递增延迟
    } else {
      setHasError(true);
      setIsLoading(false);
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // 如果没有src，显示fallback或默认占位符
  if (!src) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-muted rounded-md',
        className
      )}>
        {fallback || (
          <div className="flex flex-col items-center text-muted-foreground">
            <ImageIcon className="h-8 w-8 mb-2" />
            <span className="text-sm">暂无图片</span>
          </div>
        )}
      </div>
    );
  }

  // 加载中状态
  if (isLoading) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-muted rounded-md animate-pulse',
        className
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageIcon className="h-8 w-8 mb-2" />
          <span className="text-sm">加载中...</span>
        </div>
      </div>
    );
  }

  // 错误状态
  if (hasError && retryCount >= maxRetries) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-muted rounded-md',
        className
      )}>
        {fallback || (
          <div className="flex flex-col items-center text-muted-foreground">
            <AlertCircle className="h-8 w-8 mb-2 text-red-500" />
            <span className="text-sm">图片加载失败</span>
            <button 
              onClick={() => {
                setRetryCount(0);
                setHasError(false);
              }}
              className="text-xs text-blue-500 hover:underline mt-1"
            >
              重试
            </button>
          </div>
        )}
      </div>
    );
  }

  // 正常显示图片
  return (
    <img
      {...props}
      src={signedUrl || src}
      alt={alt}
      className={cn(className)}
      onLoad={handleImageLoad}
      onError={handleImageError}
      loading="lazy"
    />
  );
}

/**
 * 批量图片组件
 * 用于显示多张图片，自动批量处理签名URL
 */
interface SecureImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    [key: string]: any;
  }>;
  expireMinutes?: number;
  className?: string;
  imageClassName?: string;
  onAllSignedUrlsGenerated?: (signedUrls: Record<string, string>) => void;
}

export function SecureImageGallery({
  images,
  expireMinutes = 60,
  className,
  imageClassName,
  onAllSignedUrlsGenerated,
}: SecureImageGalleryProps) {
  const [signedUrls, setSignedUrls] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!images || images.length === 0) {
      setIsLoading(false);
      return;
    }

    generateBatchSignedUrls();
  }, [images, expireMinutes]);

  const generateBatchSignedUrls = async () => {
    try {
      setIsLoading(true);
      
      const urls = images.map(img => img.src).filter(Boolean);
      const batchSignedUrls = await ImageApi.generateBatchSignedUrls(urls, expireMinutes);
      
      setSignedUrls(batchSignedUrls);
      setIsLoading(false);
      onAllSignedUrlsGenerated?.(batchSignedUrls);
    } catch (error) {
      console.error('批量生成签名URL失败:', error);
      setIsLoading(false);
      
      // 失败时使用原URL
      const fallbackUrls: Record<string, string> = {};
      images.forEach(img => {
        if (img.src) fallbackUrls[img.src] = img.src;
      });
      setSignedUrls(fallbackUrls);
    }
  };

  if (!images || images.length === 0) {
    return (
      <div className={cn('text-center text-muted-foreground', className)}>
        暂无图片
      </div>
    );
  }

  return (
    <div className={cn('grid gap-4', className)}>
      {images.map((image, index) => (
        <SecureImage
          key={`${image.src}-${index}`}
          src={signedUrls[image.src] || image.src}
          alt={image.alt}
          className={imageClassName}
          expireMinutes={expireMinutes}
          {...image}
        />
      ))}
    </div>
  );
}
