import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';

interface CategoryResponse {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parentId?: number;
  iconUrl?: string;
  sortOrder: number;
  promptCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: CategoryResponse[];
  parent?: CategoryResponse;
}

export function useCategories() {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async (): Promise<CategoryResponse[]> => {
      return apiClient.get('/categories');
    },
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}

export function useRootCategories() {
  return useQuery({
    queryKey: ['categories', 'root'],
    queryFn: async (): Promise<CategoryResponse[]> => {
      return apiClient.get('/categories/root');
    },
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}

export function useCategory(id: number) {
  return useQuery({
    queryKey: ['category', id],
    queryFn: async (): Promise<CategoryResponse> => {
      return apiClient.get(`/categories/${id}`);
    },
    enabled: !!id,
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}

export function useCategoryBySlug(slug: string) {
  return useQuery({
    queryKey: ['category', 'slug', slug],
    queryFn: async (): Promise<CategoryResponse> => {
      return apiClient.get(`/categories/slug/${slug}`);
    },
    enabled: !!slug,
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}

export function useChildCategories(parentId: number) {
  return useQuery({
    queryKey: ['categories', 'children', parentId],
    queryFn: async (): Promise<CategoryResponse[]> => {
      return apiClient.get(`/categories/${parentId}/children`);
    },
    enabled: !!parentId,
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}

export function useHotCategories(limit = 10) {
  return useQuery({
    queryKey: ['categories', 'hot', limit],
    queryFn: async (): Promise<CategoryResponse[]> => {
      return apiClient.get(`/categories/hot?limit=${limit}`);
    },
    staleTime: 30 * 60 * 1000, // 30分钟
  });
}
