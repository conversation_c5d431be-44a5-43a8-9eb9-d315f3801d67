import { useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth-store';
import { User, UserRole } from '@/types/auth';

/**
 * 认证Hook
 */
export function useAuth() {
  const router = useRouter();
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    emailLogin,
    emailRegister,
    logout,
    refreshToken,
    getWechatQRCode,
    updateUser,
    clearError,
    setLoading,
  } = useAuthStore();

  /**
   * 检查用户是否有指定角色
   */
  const hasRole = useCallback((role: UserRole): boolean => {
    return user?.role === role;
  }, [user]);

  /**
   * 检查用户是否有任一指定角色
   */
  const hasAnyRole = useCallback((roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false;
  }, [user]);

  /**
   * 检查是否为创作者
   */
  const isCreator = useCallback((): boolean => {
    return hasRole(UserRole.CREATOR);
  }, [hasRole]);

  /**
   * 检查是否为管理员
   */
  const isAdmin = useCallback((): boolean => {
    return hasRole(UserRole.ADMIN);
  }, [hasRole]);

  /**
   * 检查是否为当前用户
   */
  const isCurrentUser = useCallback((userId: number): boolean => {
    return user?.id === userId;
  }, [user]);

  /**
   * 要求认证
   */
  const requireAuth = useCallback((redirectTo?: string) => {
    if (!isAuthenticated) {
      const redirect = redirectTo || window.location.pathname;
      router.push(`/auth/login?redirect=${encodeURIComponent(redirect)}`);
      return false;
    }
    return true;
  }, [isAuthenticated, router]);

  /**
   * 要求指定角色
   */
  const requireRole = useCallback((role: UserRole, redirectTo?: string) => {
    if (!requireAuth(redirectTo)) return false;
    
    if (!hasRole(role)) {
      router.push('/403'); // 权限不足页面
      return false;
    }
    return true;
  }, [requireAuth, hasRole, router]);

  /**
   * 要求创作者权限
   */
  const requireCreator = useCallback((redirectTo?: string) => {
    return requireRole(UserRole.CREATOR, redirectTo);
  }, [requireRole]);

  /**
   * 要求管理员权限
   */
  const requireAdmin = useCallback((redirectTo?: string) => {
    return requireRole(UserRole.ADMIN, redirectTo);
  }, [requireRole]);

  /**
   * 微信登录
   */
  const handleWechatLogin = useCallback(async (code: string, state?: string) => {
    try {
      await login(code, state);

      // 登录成功后重定向
      const urlParams = new URLSearchParams(window.location.search);
      const redirect = urlParams.get('redirect') || '/dashboard';
      router.push(redirect);
    } catch (error) {
      console.error('Wechat login failed:', error);
    }
  }, [login, router]);

  /**
   * 邮箱登录
   */
  const handleEmailLogin = useCallback(async (email: string, code: string) => {
    try {
      await emailLogin(email, code);

      // 登录成功后重定向
      const urlParams = new URLSearchParams(window.location.search);
      const redirect = urlParams.get('redirect') || '/';
      router.push(redirect);
    } catch (error) {
      console.error('Email login failed:', error);
      throw error;
    }
  }, [emailLogin, router]);

  /**
   * 邮箱注册
   */
  const handleEmailRegister = useCallback(async (email: string, username: string, code: string, bio?: string) => {
    try {
      await emailRegister(email, username, code, bio);

      // 注册成功后重定向
      const urlParams = new URLSearchParams(window.location.search);
      const redirect = urlParams.get('redirect') || '/';
      router.push(redirect);
    } catch (error) {
      console.error('Email register failed:', error);
      throw error;
    }
  }, [emailRegister, router]);

  /**
   * 登出
   */
  const handleLogout = useCallback(async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [logout, router]);

  /**
   * 获取微信登录二维码
   */
  const getWechatLoginQRCode = useCallback(async () => {
    try {
      return await getWechatQRCode();
    } catch (error) {
      console.error('Get wechat QR code failed:', error);
      throw error;
    }
  }, [getWechatQRCode]);

  /**
   * 自动刷新令牌
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Auto refresh token failed:', error);
        // 刷新失败，可能需要重新登录
      }
    }, 30 * 60 * 1000); // 每30分钟刷新一次

    return () => clearInterval(interval);
  }, [isAuthenticated, refreshToken]);

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,

    // User info
    hasRole,
    hasAnyRole,
    isCreator,
    isAdmin,
    isCurrentUser,

    // Auth actions
    login: handleWechatLogin,
    emailLogin: handleEmailLogin,
    emailRegister: handleEmailRegister,
    logout: handleLogout,
    getWechatQRCode: getWechatLoginQRCode,
    updateUser,
    clearError,
    setLoading,

    // Auth guards
    requireAuth,
    requireRole,
    requireCreator,
    requireAdmin,
  };
}
