import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';

interface UsePromptsParams {
  keyword?: string;
  categoryId?: number | null;
  aiModel?: string;
  minPrice?: number;
  maxPrice?: number;
  isFree?: boolean;
  isFeatured?: boolean;
  creatorId?: number;
  sortBy?: string;
  page?: number;
  size?: number;
}

interface PromptResponse {
  id: number;
  title: string;
  description: string;
  price: number;
  originalPrice?: number;
  viewCount: number;
  downloadCount: number;
  likeCount: number;
  ratingAvg: number;
  ratingCount: number;
  isFeatured: boolean;
  isFree: boolean;
  createdAt: string;
  creator?: {
    id: number;
    username: string;
    avatarUrl?: string;
  };
  categoryName?: string;
  previewImages?: string[];
  isPurchased?: boolean;
  isLiked?: boolean;
}

interface PageResponse<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export function usePrompts(params: UsePromptsParams = {}) {
  return useQuery({
    queryKey: ['prompts', params],
    queryFn: async (): Promise<PageResponse<PromptResponse>> => {
      const searchParams = new URLSearchParams();
      
      if (params.keyword) searchParams.set('keyword', params.keyword);
      if (params.categoryId) searchParams.set('categoryId', params.categoryId.toString());
      if (params.aiModel) searchParams.set('aiModel', params.aiModel);
      if (params.minPrice) searchParams.set('minPrice', params.minPrice.toString());
      if (params.maxPrice) searchParams.set('maxPrice', params.maxPrice.toString());
      if (params.isFree !== undefined) searchParams.set('isFree', params.isFree.toString());
      if (params.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());
      if (params.creatorId) searchParams.set('creatorId', params.creatorId.toString());
      if (params.sortBy) searchParams.set('sortBy', params.sortBy);
      if (params.page) searchParams.set('page', params.page.toString());
      if (params.size) searchParams.set('size', params.size.toString());

      return apiClient.get(`/prompts/search?${searchParams.toString()}`);
    },
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

export function usePrompt(id: number) {
  return useQuery({
    queryKey: ['prompt', id],
    queryFn: async (): Promise<PromptResponse> => {
      return apiClient.get(`/prompts/${id}`);
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

export function useHotPrompts(limit = 10) {
  return useQuery({
    queryKey: ['prompts', 'hot', limit],
    queryFn: async (): Promise<PromptResponse[]> => {
      return apiClient.get(`/prompts/hot?limit=${limit}`);
    },
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

export function useLatestPrompts(limit = 10) {
  return useQuery({
    queryKey: ['prompts', 'latest', limit],
    queryFn: async (): Promise<PromptResponse[]> => {
      return apiClient.get(`/prompts/latest?limit=${limit}`);
    },
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

export function useUserPrompts(userId: number, status?: string, page = 1, size = 20) {
  return useQuery({
    queryKey: ['prompts', 'user', userId, status, page, size],
    queryFn: async (): Promise<PageResponse<PromptResponse>> => {
      const searchParams = new URLSearchParams();
      if (status) searchParams.set('status', status);
      searchParams.set('page', page.toString());
      searchParams.set('size', size.toString());

      return apiClient.get(`/prompts/user/${userId}?${searchParams.toString()}`);
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}
