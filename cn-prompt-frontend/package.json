{"name": "cn-prompt-frontend", "version": "1.0.0", "description": "中文AI提示词交易平台前端", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next/font": "14.0.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.83.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/blocks": "^7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "storybook": "^7.5.3", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}