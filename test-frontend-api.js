// 测试前端API调用
const puppeteer = require('puppeteer');

async function testFrontendAPI() {
  console.log('🧪 开始测试前端API调用...\n');

  const browser = await puppeteer.launch({ 
    headless: false, 
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  try {
    const page = await browser.newPage();
    
    // 1. 访问测试页面
    console.log('📱 1. 访问测试页面...');
    await page.goto('http://localhost:3000/test-create');
    await page.waitForSelector('button');
    
    // 2. 设置登录状态
    console.log('🔐 2. 设置登录状态...');
    await page.evaluate(() => {
      // 设置token
      const tokenInfo = {
        accessToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTM0NTk3NjQsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiVVNFUiIsInR5cGUiOiJhY2Nlc3MifQ.Ls0WRVpCXAEJynBR0uGMbMDCWa_JU3vXZPew_J4O7CU',
        refreshToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTYwNDgxNjQsInR5cGUiOiJyZWZyZXNoIn0.kDXGRbFyzzUsiLODr36fkAahO0WjeWccRaAGMve3xUw',
        expiresAt: Date.now() + 3600 * 1000,
      };
      localStorage.setItem('token', JSON.stringify(tokenInfo));
      
      // 设置认证store
      const authStore = {
        state: {
          user: {
            id: 6,
            username: 'testuser',
            email: '<EMAIL>',
            role: 'CREATOR'
          },
          isAuthenticated: true,
        },
        version: 0,
      };
      localStorage.setItem('auth-store', JSON.stringify(authStore));
      
      console.log('✅ 登录状态已设置');
    });
    
    // 3. 监听控制台日志
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'log') {
        console.log('📝 Console:', text);
      } else if (type === 'error') {
        console.error('❌ Console Error:', text);
      }
    });
    
    // 4. 监听网络请求
    await page.setRequestInterception(true);
    page.on('request', request => {
      const url = request.url();
      if (url.includes('/api/prompts')) {
        console.log(`🌐 API Request: ${request.method()} ${url}`);
        if (request.postData()) {
          console.log('📤 Request Data:', request.postData().substring(0, 200) + '...');
        }
      }
      request.continue();
    });
    
    page.on('response', response => {
      const url = response.url();
      if (url.includes('/api/prompts')) {
        console.log(`📥 API Response: ${response.status()} ${url}`);
      }
    });
    
    // 5. 测试创建提示词
    console.log('\n🚀 3. 测试创建提示词...');
    await page.click('button:first-child'); // 第一个按钮：测试创建提示词
    
    // 等待API调用完成
    await page.waitForTimeout(5000);
    
    // 6. 测试获取用户提示词
    console.log('\n📋 4. 测试获取用户提示词...');
    await page.click('button:nth-child(2)'); // 第二个按钮：获取用户提示词
    
    // 等待API调用完成
    await page.waitForTimeout(3000);
    
    // 7. 测试搜索提示词
    console.log('\n🔍 5. 测试搜索提示词...');
    await page.click('button:nth-child(3)'); // 第三个按钮：搜索提示词
    
    // 等待API调用完成
    await page.waitForTimeout(3000);
    
    console.log('\n🎉 前端API测试完成！');
    
    // 8. 检查页面上的结果
    const resultText = await page.evaluate(() => {
      const resultDiv = document.querySelector('.bg-green-50, .bg-red-50');
      return resultDiv ? resultDiv.textContent : '没有找到结果';
    });
    
    console.log('📊 页面结果:', resultText.substring(0, 200) + '...');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 保持浏览器打开以便查看结果
    console.log('\n💡 浏览器将保持打开状态，请手动关闭...');
    // await browser.close();
  }
}

// 检查是否安装了puppeteer
try {
  require('puppeteer');
  testFrontendAPI();
} catch (error) {
  console.log('❌ 需要安装puppeteer: npm install puppeteer');
  console.log('或者手动测试：');
  console.log('1. 访问 http://localhost:3000/test-create');
  console.log('2. 在浏览器控制台执行以下代码设置登录状态：');
  console.log(`
// 设置token
const tokenInfo = {
  accessToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTM0NTk3NjQsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiVVNFUiIsInR5cGUiOiJhY2Nlc3MifQ.Ls0WRVpCXAEJynBR0uGMbMDCWa_JU3vXZPew_J4O7CU',
  refreshToken: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2IiwiaWF0IjoxNzUzNDU2MTY0LCJleHAiOjE3NTYwNDgxNjQsInR5cGUiOiJyZWZyZXNoIn0.kDXGRbFyzzUsiLODr36fkAahO0WjeWccRaAGMve3xUw',
  expiresAt: Date.now() + 3600 * 1000,
};
localStorage.setItem('token', JSON.stringify(tokenInfo));

// 设置认证store
const authStore = {
  state: {
    user: { id: 6, username: 'testuser', email: '<EMAIL>', role: 'CREATOR' },
    isAuthenticated: true,
  },
  version: 0,
};
localStorage.setItem('auth-store', JSON.stringify(authStore));

console.log('✅ 登录状态已设置，现在可以测试API调用');
  `);
  console.log('3. 刷新页面并点击测试按钮');
}
