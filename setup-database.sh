#!/bin/bash

# 数据库初始化脚本

echo "🗄️  开始初始化数据库..."

# 数据库连接信息
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="cn-prompt"
DB_USER="root"

# 提示输入密码
echo "请输入MySQL root密码:"
read -s DB_PASSWORD

echo ""
echo "📋 数据库配置信息:"
echo "   主机: $DB_HOST:$DB_PORT"
echo "   数据库: $DB_NAME"
echo "   用户: $DB_USER"
echo ""

# 检查MySQL连接
echo "🔍 检查MySQL连接..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
    echo "❌ MySQL连接失败，请检查连接信息"
    exit 1
fi
echo "✅ MySQL连接成功"

# 执行初始化SQL
echo ""
echo "📝 执行数据库初始化脚本..."
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" < init.sql; then
    echo "✅ 数据库初始化成功"
else
    echo "❌ 数据库初始化失败"
    exit 1
fi

# 验证数据库结构
echo ""
echo "🔍 验证数据库结构..."

# 检查数据库是否存在
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1;" &> /dev/null; then
    echo "✅ 数据库 '$DB_NAME' 创建成功"
else
    echo "❌ 数据库 '$DB_NAME' 创建失败"
    exit 1
fi

# 检查表是否创建成功
echo ""
echo "📊 检查数据表:"
tables=("users" "categories" "prompts" "orders" "order_items")

for table in "${tables[@]}"; do
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "DESCRIBE $table;" &> /dev/null; then
        echo "✅ 表 '$table' 创建成功"
    else
        echo "❌ 表 '$table' 创建失败"
    fi
done

# 检查测试数据
echo ""
echo "📋 检查测试数据:"

# 检查分类数据
category_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "SELECT COUNT(*) FROM categories;")
echo "✅ 分类数据: $category_count 条记录"

# 检查用户数据
user_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "SELECT COUNT(*) FROM users;")
echo "✅ 用户数据: $user_count 条记录"

# 检查提示词数据
prompt_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "SELECT COUNT(*) FROM prompts;")
echo "✅ 提示词数据: $prompt_count 条记录"

echo ""
echo "🎉 数据库初始化完成！"
echo ""
echo "📋 数据库信息总结:"
echo "   数据库名: $DB_NAME"
echo "   字符集: utf8mb4"
echo "   排序规则: utf8mb4_unicode_ci"
echo "   数据表: ${#tables[@]} 个"
echo "   测试数据: 已插入"
echo ""
echo "🔗 连接信息:"
echo "   JDBC URL: **************************************************************************************************************"
echo "   用户名: $DB_USER"
echo ""
echo "📝 下一步:"
echo "   1. 更新 application.yml 中的数据库配置"
echo "   2. 启动后端服务: ./start.sh 或 ./deploy.sh"
echo "   3. 访问应用: http://localhost:3000"
